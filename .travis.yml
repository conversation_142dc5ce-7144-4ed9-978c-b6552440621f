language: node_js
node_js: [lts/*]
before_install: [npm install -g npm@latest]
install: [npm ci]
script:
    - npm run lint
    - npm run dist
cache:
    directories:
        - node_modules
        - '$HOME/.npm'
branches:
    only:
        - master
        - /^v\d+\.\d+(\.\d+)?(-\S*)?$/ # tagged with version
        # - release
notifications:
    slack:
        secure: dctz771vJCv6UdjBz65n8XkKx5q/LF5UIY+qSRAxLpqwFp9Y3i9trUNoSaDya4PH5sfAHbUHxsBvG8njIKDJY1pckcrCt2Ml2+9fRQ01cCMggNnSK5OlcuoL6lrnbMbaLz3XEUKJY8mfJnnHggUFNqT0IhCR4xgcxiyg90AtHhIGQcIpkFrpmwbW+ZXm7PMTP/svGkL3OMs4lDO6V8XzEGKXd+4rkcvINZwOtKDXbIf7Wm5MXQ2lf4o1DviAJeFqlIUPRRonAs2KPUAl86t+M22EWDExy6NOtmbdA68m+pDaBJ0mMN0OwxsoEJh5e7ml1/UAs82hCu+Kl3xW4emDrrF7k6C065NmWeRqLBVJxcfy+rdNsiBfJhPmBBGc0VTYLqnWR5PLxDBHgLyO7zAjG4n4G8WpCEY0j891Xaw3Cktz8tWWo336BxCmsd2zOUdoWr+aQsml25mlpLYuX2t4HR66jJdz8A7X+e5m4V3/BE+afMKhRYgeuwPVq03TBAtHZp5jLr0TCw9t3qyNrUEXGtqoAYdCThCP1zcpxTTYnxoYHgoEfiE8OVMkMf6R3JpCUQMk1+AbpyVIHeCxfaq7jCZjUG4exMGFNFhTSjl3ePoCLdwfP75uEIiQU/LOJGb2JrJWVjTEBs5P/atk71ijomygAU3D6BtYwoZm0Hqy+l8=
    webhooks:
        urls:
            - '$GITTER_URL'
        on_success: change # options: [always|never|change] default: always
        on_failure: always # options: [always|never|change] default: always
        on_start: never # options: [always|never|change] default: always
