.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}

.sl-hidden {
	display: none;
}
.sl-flex {
	display: flex;
}
.sl-block {
	display: block;
}
@media (min-width: 50rem) {
	.md\:sl-hidden {
		display: none;
	}
	.md\:sl-flex {
		display: flex;
	}
	.md\:sl-block {
		display: block;
	}
}
@media (min-width: 72rem) {
	.lg\:sl-hidden {
		display: none;
	}
	.lg\:sl-flex {
		display: flex;
	}
	.lg\:sl-block {
		display: block;
	}
}
[data-theme='light'] .light\:sl-hidden {
	display: none;
}
[data-theme='dark'] .dark\:sl-hidden {
	display: none;
}

/*
Flip an element around the y-axis when in an RTL context.
Primarily useful for things where we can’t rely on writing direction like icons.

<Icon name="right-arrow" class="rtl:flip" />

In a LTR context: →					In a RTL context: ←
*/
[dir='rtl'] .rtl\:flip:not(:where([dir='rtl'] [dir='ltr'] *)) {
	transform: matrix(-1, 0, 0, 1, 0, 0);
}
