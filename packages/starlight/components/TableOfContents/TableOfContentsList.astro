---
import type { TocItem } from '../../utils/generateToC';

interface Props {
	toc: TocItem[];
	depth?: number;
	isMobile?: boolean;
}

const { toc, isMobile = false, depth = 0 } = Astro.props;
---

<ul class:list={{ isMobile }}>
	{
		toc.map((heading) => (
			<li>
				<a href={'#' + heading.slug}>
					<span>{heading.text}</span>
				</a>
				{heading.children.length > 0 && (
					<Astro.self toc={heading.children} depth={depth + 1} isMobile={isMobile} />
				)}
			</li>
		))
	}
</ul>

<style define:vars={{ depth }}>
	ul {
		padding: 0;
		list-style: none;
	}
	a {
		--pad-inline: 0.5rem;
		display: block;
		border-radius: 0.25rem;
		padding-block: 0.25rem;
		padding-inline: calc(1rem * var(--depth) + var(--pad-inline)) var(--pad-inline);
		line-height: 1.25;
	}
	a[aria-current='true'] {
		color: var(--sl-color-text-accent);
	}
	.isMobile a {
		--pad-inline: 1rem;
		display: flex;
		justify-content: space-between;
		gap: var(--pad-inline);
		border-top: 1px solid var(--sl-color-gray-6);
		border-radius: 0;
		padding-block: 0.5rem;
		color: var(--sl-color-text);
		font-size: var(--sl-text-sm);
		text-decoration: none;
		outline-offset: var(--sl-outline-offset-inside);
	}
	.isMobile:first-child > li:first-child > a {
		border-top: 0;
	}
	.isMobile a[aria-current='true'],
	.isMobile a[aria-current='true']:hover,
	.isMobile a[aria-current='true']:focus {
		color: var(--sl-color-white);
		background-color: unset;
	}
	.isMobile a[aria-current='true']::after {
		content: '';
		width: 1rem;
		background-color: var(--sl-color-text-accent);
		/* Check mark SVG icon */
		-webkit-mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxNCAxNCc+PHBhdGggZD0nTTEwLjkxNCA0LjIwNmEuNTgzLjU4MyAwIDAgMC0uODI4IDBMNS43NCA4LjU1NyAzLjkxNCA2LjcyNmEuNTk2LjU5NiAwIDAgMC0uODI4Ljg1N2wyLjI0IDIuMjRhLjU4My41ODMgMCAwIDAgLjgyOCAwbDQuNzYtNC43NmEuNTgzLjU4MyAwIDAgMCAwLS44NTdaJy8+PC9zdmc+Cg==');
		mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxNCAxNCc+PHBhdGggZD0nTTEwLjkxNCA0LjIwNmEuNTgzLjU4MyAwIDAgMC0uODI4IDBMNS43NCA4LjU1NyAzLjkxNCA2LjcyNmEuNTk2LjU5NiAwIDAgMC0uODI4Ljg1N2wyLjI0IDIuMjRhLjU4My41ODMgMCAwIDAgLjgyOCAwbDQuNzYtNC43NmEuNTgzLjU4MyAwIDAgMCAwLS44NTdaJy8+PC9zdmc+Cg==');
		-webkit-mask-repeat: no-repeat;
		mask-repeat: no-repeat;
		flex-shrink: 0;
	}
</style>
