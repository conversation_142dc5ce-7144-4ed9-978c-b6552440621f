import { Environment, MFEConfCreator, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';
import create from '../../default/word-editor';
import type { WordEditorConfig } from '@viclass/editor.word';
import type { OperationMode, EditorLookup } from '@viclass/editor.core';

const embedCreate: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const desc = await create(spec, env, request);
    const settings = (desc.impl as EditorLookup).settings as WordEditorConfig;
    settings.docViewMode = 'full-viewport';
    settings.operationMode = 'CLOUD' as OperationMode;

    return desc;
};

export default embedCreate;
