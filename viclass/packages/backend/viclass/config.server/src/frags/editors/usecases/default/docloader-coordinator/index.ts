import { ModuleLookup } from '@viclass/editor.core';
import type { DocLoaderCoordinatorConfig } from '@viclass/editor.coordinator/docloader';
import { Environment, MFEConfCreator, MFESpec, MFEDescription, MFEConfRequest } from 'src/app.model';
import * as mapping from 'src/frags/editor-mapping';

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const lookup: ModuleLookup = {
        type: 'module',
        remoteName: 'coordinator.docloader',
        remoteEntry: `${env.scheme}://${env.domain}/modules/coordinator.docloader/coordinator.docloader.js`,
        exposedModule: './coordinator.docloader',
    };

    const settings: Partial<DocLoaderCoordinatorConfig> = {
        editorTypeMapping: mapping.default,
    };

    return {
        item: 'DocLoaderCoordinator',
        impl: {
            lookup: lookup,
            settings: settings,
        },
    };
};

export default create;
