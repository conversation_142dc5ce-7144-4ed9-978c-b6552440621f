import { EditorUILookup } from '@viclass/editorui.loader';
import { Environment, MFEConfRequest, MFEDescription, MFESpec, MFESpecCommonToolUISettings } from 'src/app.model';

const switchToolSettings = {
    availableEditors: ['FreeDrawingEditor', 'WordEditor', 'GeometryEditor', 'MathEditor', 'MathGraphEditor'],
    iconClasses: {
        FreeDrawingEditor: 'vcon_document_freedrawing',
        WordEditor: 'vcon_document_word',
        GeometryEditor: 'vcon_document_geometry',
        MathEditor: 'vcon_document_mathtype',
        MathGraphEditor: 'vcon_document_magh',
    },
};

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    const uiLookup: EditorUILookup = {
        editorType: 'CommonToolsEditor',
        uiImpl: {
            type: 'module',
            remoteName: 'editorui.commontools',
            remoteEntry: `${env.scheme}://${env.domain}/modules/mfe/editorui.commontools.js`,
            exposedModule: './editorui.commontools',
        },
        style: {
            type: 'module',
            remoteName: 'editorui.commontools.style',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.commontools.style.js`,
            exposedModule: './editorui.commontools.style',
        },
    };

    if (spec.ui && spec.ui !== true && spec.ui.settings) {
        const settings = spec.ui.settings as MFESpecCommonToolUISettings;

        if (settings.switch) {
            const lookupSettings = {
                availableEditors: [],
                iconClasses: {},
            };

            for (const e of settings.switch) {
                lookupSettings.availableEditors.push(e);
                lookupSettings.iconClasses[e] = switchToolSettings.iconClasses[e];
            }

            uiLookup.settings = lookupSettings;
        }
    }

    return {
        item: 'CommonToolsEditor',
        ui: uiLookup,
    };
}
