import { Environment, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    return {
        item: 'FreeDrawingEditor',
        ui: {
            editorType: 'FreeDrawingEditor',
            uiImpl: {
                type: 'module',
                remoteName: 'editorui.freedrawing',
                remoteEntry: `${env.scheme}://${env.domain}/modules/editorui.freedrawing/editorui.freedrawing.js`,
                exposedModule: './editorui.freedrawing',
            },
            style: {
                type: 'module',
                remoteName: 'editorui.freedrawing.style',
                remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.freedrawing.style.js`,
                exposedModule: './editorui.freedrawing.style',
            },
        },
    };
}
