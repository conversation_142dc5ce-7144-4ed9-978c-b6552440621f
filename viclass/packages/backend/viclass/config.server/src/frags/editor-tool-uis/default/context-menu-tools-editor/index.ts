import { Environment, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    return {
        item: 'ContextMenuToolsEditor',
        ui: {
            editorType: 'ContextMenuToolsEditor',
            uiImpl: {
                type: 'module',
                remoteName: 'editorui.commontools',
                remoteEntry: '/modules/mfe/editorui.commontools.js',
                exposedModule: './editorui.contextmenutool',
            },
            style: {
                type: 'module',
                remoteName: 'editorui.commontools.style',
                remoteEntry: '/modules/themes/editorui.commontools.style.js',
                exposedModule: './editorui.commontools.style',
            },
        },
    };
}
