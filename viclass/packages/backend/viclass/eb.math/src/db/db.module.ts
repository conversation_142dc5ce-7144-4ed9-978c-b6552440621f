import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { MathDocColName, MathDocMongoSchema, MathDocName } from './db.mongo.schema';
import { MathDocGatewayService } from './mathdoc.gateway.service';
import { CacheModule } from '@nestjs/cache-manager';
import { MathDocCacheService } from './mathdoc.cache';

const logger = new Logger('DatabaseModule');

@Module({
    imports: [
        ConfigModule,
        MongooseModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: (conf: ConfigService) => {
                logger.log(`Mongoose Connection String : ${conf.get('database.connection')}`);
                return {
                    uri: conf.get('database.connection'),
                };
            },
            inject: [ConfigService],
        }),

        MongooseModule.forFeature([
            {
                name: MathDocName,
                schema: MathDocMongoSchema,
                collection: MathDocColName,
            },
        ]),
        CacheModule.register({
            ttl: 60 * 10, // seconds
            max: 500, // maximum number of items in cache
        }),
    ],
    providers: [MathDocGatewayService, MathDocCacheService],
    exports: [MathDocGatewayService, MathDocCacheService],
})
export class DatabaseModule {}
