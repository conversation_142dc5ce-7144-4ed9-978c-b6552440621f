import { BadRequestException, Controller, Logger, Post, Query, Req } from '@nestjs/common';
import { CmdMetaProto } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { Request } from 'express';
import mongoose from 'mongoose';
import { EquationPojo, MathGraphDocPojo } from 'src/db/db.mongo.schema';
import { MathGraphDocCacheService } from 'src/db/maghdoc.cache';
import { MathGraphDocGatewayService } from 'src/db/maghdoc.gateway.service';
import {
    DeleteEquationCmdProto,
    MathGraphCmdTypeProto,
    UpdateContentCmdProto,
    UpdateDocStateCmdProto,
} from '@viclass/proto/editor.magh';
import { MaghDocRenderProp } from '@viclass/editor.magh';

const log = new Logger('ProcessCmdCtrl');

@Controller()
export class ProcessCmdCtrl {
    constructor(
        private readonly maghDb: MathGraphDocGatewayService,
        private readonly docCache: MathGraphDocCacheService
    ) {}

    @Post('/cmd')
    async processCommand(@Req() req: Request, @Query('globalId') globalId: string) {
        const cmdBuffer = req.body as Buffer;

        const cmd = Uint8Array.from(cmdBuffer);

        let index = 0;

        const metaLength = cmd[index++];
        const metaArrBuf = cmd.slice(index, index + metaLength);
        index += metaLength;
        const stateData = cmd.slice(index);

        const metaProto = CmdMetaProto.deserializeBinary(metaArrBuf);

        if (metaProto.getChannelCode() != 5) {
            throw new BadRequestException({
                message: 'Invalid Channel Code ' + metaProto.getChannelCode(),
            });
        }
        if (!globalId || !mongoose.isObjectIdOrHexString(globalId))
            throw new BadRequestException({
                message: 'Global Id Not Available',
            });

        switch (metaProto.getCmdType()) {
            case FCCmdTypeProto.INSERT_DOC:
            case FCCmdTypeProto.INSERT_LAYER:
            case FCCmdTypeProto.PREVIEW_BOUNDARY:
            case FCCmdTypeProto.REMOVE_DOC: {
                log.log(`ignore command of type ${metaProto.getCmdType()}`);
                return {
                    status: 'ok',
                    message: `ignored command type ${metaProto.getCmdType()}`,
                };
            }
            case MathGraphCmdTypeProto.UPDATE_CONTENT: {
                return await this.handleUpdateContent(UpdateContentCmdProto.deserializeBinary(stateData));
            }
            case MathGraphCmdTypeProto.UPDATE_DOC_STATE: {
                return await this.handleUpdateDocState(UpdateDocStateCmdProto.deserializeBinary(stateData));
            }
            case MathGraphCmdTypeProto.DELETE_EQUATION: {
                return await this.handleDeleteEquation(DeleteEquationCmdProto.deserializeBinary(stateData));
            }
            default:
                throw new BadRequestException({
                    message: `MathGraph command type unknown ${metaProto.getCmdType()}!`,
                });
        }
    }

    async handleUpdateContent(cmd: UpdateContentCmdProto) {
        const globalId = cmd.getGlobalId();
        const doc = await this.docCache.get(globalId);
        if (!doc) {
            throw new BadRequestException({
                message: `MathGraph document ${globalId} not found`,
            });
        }

        const cmdVersion = cmd.getVersion();
        if (doc.version >= cmdVersion) {
            log.log(`ignored, version of doc ${globalId}: ${doc.version} >= cmd version: ${cmdVersion}`);
            return {
                status: 'ok',
                message: `ignored, version of doc ${globalId}: ${doc.version} >= cmd version: ${cmdVersion}`,
            };
        }

        doc.version = cmdVersion;
        const index = cmd.getPlotIndex();
        if (index < 0 || index > doc.equations.length) {
            throw new BadRequestException({
                message: `invalid plot index: ${index}`,
            });
        }

        const latex = cmd.getLatex();
        const serialized = cmd.getSerialized();

        let newPlots: EquationPojo[];
        if (cmd.hasIsInsert() && cmd.getIsInsert()) {
            newPlots = [...doc.equations];
            newPlots.splice(index, 0, {
                latex: latex,
                serializedValue: serialized,
            });
        } else {
            newPlots =
                index < doc.equations.length
                    ? doc.equations.map((equation, i) =>
                          i === index ? { latex: latex, serializedValue: serialized } : equation
                      )
                    : [...doc.equations, { latex: latex, serializedValue: serialized }];
        }

        const newDoc: MathGraphDocPojo = {
            ...doc,
            version: doc.version,
            equations: newPlots,
        };

        const result = await this.maghDb.updateDocument(newDoc);
        await this.docCache.set(globalId, result.toObject());

        return {
            status: 'ok',
            message: 'content updated',
        };
    }

    async handleUpdateDocState(cmd: UpdateDocStateCmdProto) {
        const globalId = cmd.getGlobalId();
        const doc = await this.docCache.get(globalId);
        if (!doc) {
            throw new BadRequestException({
                message: `MathGraph document ${globalId} not found`,
            });
        }
        const proto = cmd.getDocRenderProp();
        const docRenderProp = <MaghDocRenderProp>{};

        if (proto.hasScreenUnit()) docRenderProp.screenUnit = proto.getScreenUnit();
        if (proto.hasScale()) docRenderProp.scale = proto.getScale();
        if (proto.getTranslationList().length) docRenderProp.translation = proto.getTranslationList();
        else docRenderProp.translation = [0, 0, 0];
        if (proto.getRotationList().length) docRenderProp.rotation = proto.getRotationList();
        else docRenderProp.rotation = [0, 0, 0];
        if (proto.hasValid()) docRenderProp.valid = proto.getValid();
        if (proto.hasAxis()) docRenderProp.axis = proto.getAxis();
        if (proto.hasGrid()) docRenderProp.grid = proto.getGrid();
        if (proto.hasDetailGrid()) docRenderProp.detailGrid = proto.getDetailGrid();
        if (proto.hasLineStyle()) docRenderProp.lineStyle = proto.getLineStyle();
        if (proto.hasLineWidth()) docRenderProp.lineWidth = proto.getLineWidth();
        if (proto.hasOpacity()) docRenderProp.opacity = proto.getOpacity();

        const result = await this.maghDb.updateDocState(cmd.getGlobalId(), docRenderProp);
        await this.docCache.set(globalId, result.toObject());

        return {
            status: 'ok',
            message: 'doc state updated',
        };
    }

    async handleDeleteEquation(cmd: DeleteEquationCmdProto) {
        const globalId = cmd.getGlobalId();
        const doc = await this.docCache.get(globalId);
        if (!doc) {
            throw new BadRequestException({
                message: `MathGraph document ${globalId} not found`,
            });
        }

        const cmdVersion = cmd.getVersion();
        if (doc.version >= cmdVersion) {
            log.log(`ignored, version of doc ${globalId}: ${doc.version} >= cmd version: ${cmdVersion}`);
            return {
                status: 'ok',
                message: `ignored, version of doc ${globalId}: ${doc.version} >= cmd version: ${cmdVersion}`,
            };
        }

        doc.version = cmdVersion;
        const index = cmd.getPlotIndex();
        if (index < 0 || index >= doc.equations.length) {
            throw new BadRequestException({
                message: `invalid plot index: ${index}`,
            });
        }

        const newDoc: MathGraphDocPojo = {
            ...doc,
            version: doc.version,
            equations: doc.equations.filter((_eq, i) => i !== index),
        };

        const result = await this.maghDb.updateDocument(newDoc);
        await this.docCache.set(globalId, result.toObject());

        return {
            status: 'ok',
            message: 'content updated',
        };
    }
}
