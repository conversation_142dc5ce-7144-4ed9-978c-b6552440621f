/// <reference types="../typings" />

import type { DocLoaderCoordinator, DocLoaderCoordinatorConfig } from '@viclass/editor.coordinator/docloader';
import { environment } from './environments/environment';

export namespace DocLoader {
    const docLoaderModuleOptions = environment.moduleLookups.coordinatorDocLoader;

    //TODO: this method should not require configuration. This configuration SHOULD be optional,
    //only user with certain needs will have to override the configuration
    export async function createLoader(config: DocLoaderCoordinatorConfig): Promise<DocLoaderCoordinator> {
        const module = await viclass.mfeRT.loadRemoteModule(docLoaderModuleOptions);
        const ctor = module.DocLoaderCoordinator as typeof DocLoaderCoordinator;

        return new ctor(config);
    }
}

// manually export to global scope
if (window) {
    window['viclass'] = window['viclass'] || ({} as any);
    window['viclass']['DocLoader'] = DocLoader;
}
