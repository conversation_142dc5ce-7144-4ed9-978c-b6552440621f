{"skipLink.label": "Skip to content", "search.label": "Search", "search.ctrlKey": "Ctrl", "search.cancelLabel": "Cancel", "search.devWarning": "Search is only available in production builds. \nTry building and previewing the site to test it out locally.", "themeSelect.accessibleLabel": "Select theme", "themeSelect.dark": "Dark", "themeSelect.light": "Light", "themeSelect.auto": "Auto", "languageSelect.accessibleLabel": "Select language", "menuButton.accessibleLabel": "<PERSON><PERSON>", "sidebarNav.accessibleLabel": "Main", "tableOfContents.onThisPage": "Table of content", "tableOfContents.overview": "Overview", "i18n.untranslatedContent": "This content is not available in your language yet.", "page.editLink": "Edit page", "page.lastUpdated": "Last updated:", "page.previousLink": "Previous", "page.nextLink": "Next", "page.draft": "This content is a draft and will not be included in production builds.", "404.text": "Page not found. Check the URL or try using the search bar.", "aside.note": "Note", "aside.tip": "Tip", "aside.caution": "Caution", "aside.danger": "Danger", "fileTree.directory": "Directory", "builtWithStarlight.label": "Built with Starlight", "expressiveCode.copyButtonCopied": "Copied!", "expressiveCode.copyButtonTooltip": "Copy to clipboard", "expressiveCode.terminalWindowFallbackTitle": "Terminal window", "pagefind.clear_search": "Clear", "pagefind.load_more": "Load more results", "pagefind.search_label": "Search this site", "pagefind.filters_label": "Filters", "pagefind.zero_results": "No results for [SEARCH_TERM]", "pagefind.many_results": "[COUNT] results for [SEARCH_TERM]", "pagefind.one_result": "[COUNT] result for [SEARCH_TERM]", "pagefind.alt_search": "No results for [SEARCH_TERM]. Showing results for [DIFFERENT_TERM] instead", "pagefind.search_suggestion": "No results for [SEARCH_TERM]. Try one of the following searches:", "pagefind.searching": "Searching for [SEARCH_TERM]..."}