import { ClassroomActivity } from '../activities';
import { UserAvailableStatus } from '@viclass/portal.common';

export interface ClassroomNotification {
    id: string;
    emittedBy: string;
    message: string;
    createdTime: number;
    expiredTime: number;
    data: ClassroomNotificationData;
    show: boolean;
}

export type EntityType =
    | 'LeaveClassND'
    | 'RaiseHandND'
    | 'AcceptRaiseHandND'
    | 'AcceptShareScreenND'
    | 'RejectRaiseHandND'
    | 'RejectShareScreenND'
    | 'CancelRaiseHandND'
    | 'ShareScreenRemovedND'
    | 'RegisterND'
    | 'RegistrationCancelledND'
    | 'AcceptRegistrationND'
    | 'RejectRegistrationND'
    | 'RequestPresentationND'
    | 'StopPresentationND'
    | 'AcceptPresentationRequestND'
    | 'RejectPresentationRequestND'
    | 'CancelPresentationRequestND'
    | 'NewQuestionND'
    | 'StopQuestionND'
    | 'JoinClassND'
    | 'StartClassND'
    | 'StopClassND'
    | 'PinnedCoordStateND'
    | 'UnpinnedCoordStateND'
    | 'RequestPinTabND'
    | 'CancelRequestPinTabND'
    | 'RejectRequestPinTabND'
    | 'ApproveRequestPinTabND'
    | 'UpdateRequestPinTabND'
    | 'UserAvailableStatusChangedND'
    | 'ReqShareScreenND'
    | 'CancelShareScreenND';

export interface ClassroomNotificationData {
    entityType: EntityType;
}

export interface RegistrationCancelledND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface RequestPinTabND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
    coordStateId: string;
}

export interface CancelRequestPinTabND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
    coordStateId: string;
}

export interface UpdateRequestPinTabND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
    coordStateId: string;
    status?: string;
    title?: string;
}

export interface RejectRequestPinTabND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
    coordStateId: string;
}

export interface ApproveRequestPinTabND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
    coordStateId: string;
}

export interface AcceptRaiseHandND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface AcceptShareScreenND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface RequestPresentationND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
    activityId: string;
    activity: ClassroomActivity;
}

export interface RejectRaiseHandND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface RejectShareScreenND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface AcceptPresentationRequestND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
    activityId: string;
}

export interface AcceptRegistrationND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface CancelRaiseHandND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

/**
 * Notification data for ending a self-initiated screen-sharing session.
 */
export interface ShareScreenRemovedND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

/**
 * Notification data for canceling a screen-sharing session.
 */
export interface CancelShareScreenND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface JoinClassND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface LeaveClassND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface RaiseHandND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface ReqShareScreenND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface RegisterND extends ClassroomNotificationData {
    lsId: string;
    regId: string;
}

export interface RejectPresentationRequestND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
    activityId: string;
}

export interface CancelPresentationRequestND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
    activityId: string;
}

export interface RejectRegistrationND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface EndPresentationND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
}

export interface StopPresentationND extends ClassroomNotificationData {
    lsId: string;
    targetUserId: string;
}

export interface StartClassND extends ClassroomNotificationData {
    lsId: string;
    ownerId: string;
}

export interface StopClassND extends ClassroomNotificationData {
    lsId: string;
    ownerId: string;
}

export interface NewQuestionND extends ClassroomNotificationData {
    lsId: string;
    activityId: string;
    activity: ClassroomActivity;
}

export interface StopQuestionND extends ClassroomNotificationData {
    lsId: string;
    activityId: string;
}

export interface UserAvailableStatusChangedND extends ClassroomNotificationData {
    lsId: string;
    callingUserId: string;
    status: UserAvailableStatus;
}

/**
 * NOTIFICATION MODELS
 */
/**
 * Notification view model contains information needed to work with Notification features on the UI
 * It is created by transforming the notification received in the notification gateway and add more information
 */
export declare interface NotificationViewModel {
    entityType: EntityType;
    id: string; // notification id
    emittedBy: string;
    callingUserId?: string; // if a notification comes from a member, this is the user id of the member
    regId?: string; // if a notification comes from a member, this is the registration id of the member
    message: string;
    createdTime: number;
    unread: boolean;
}
