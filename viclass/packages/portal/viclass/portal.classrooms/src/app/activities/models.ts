export type ActivityType = 'QuickQuestionAD' | 'RequestPresentationAD';
export type YesNoResponse = 'ACCEPTED' | 'REJECTED';
export type ActivityStatus = 'ON_GOING' | ' EXPIRED' | 'CANCELLED' | 'INACTIVE' | 'FINISHED';

export interface ClassroomActivity {
    id: string;
    lsId: string;
    createdBy: string;
    status: ActivityStatus;
    createdTime: number;
    data: ActivityData;
}

export interface ActivityData {
    activityType: ActivityType;
}

export interface QuickQuestionAD extends ActivityData {
    question: string;
}

export interface RequestPresentationAD extends ActivityData {
    requestedTo: string;
    response?: YesNoResponse;
}
