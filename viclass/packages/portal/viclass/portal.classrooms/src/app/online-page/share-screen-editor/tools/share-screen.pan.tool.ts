import {
    BoardViewportManager,
    DefaultPointerEventData,
    MouseEventData,
    mouseLocation,
    NativeEventTarget,
    PanFeature,
    PointerEventData,
    SupportPanFeature,
} from '@viclass/editor.core';
import { ShareScreenDocCtrl } from '../docs/sharescreen.doc.ctrl';
import { ShareScreenEditor } from '../sharescreen.api';
import { ShareScreenPanToolState, ShareScreenToolType } from './models';
import { ShareScreenTool } from './share-screen.tool';
import { validatePointerPos } from './tool.utils';

export class ShareScreenPanTool extends ShareScreenTool<ShareScreenPanToolState> implements SupportPanFeature {
    override toolState: ShareScreenPanToolState = { doc: undefined };
    constructor(
        editor: ShareScreenEditor,
        private panFeature: PanFeature
    ) {
        super(editor);
    }

    get toolType(): ShareScreenToolType {
        return 'ShareScreenPanTool';
    }

    override resetState() {
        this.clear();
    }

    clear() {
        this.toolState.doc = undefined;
        this.started = false;
        this.lastPointerMove = undefined;
    }

    override onBlur() {
        this.clear();
    }

    override handleMouseEvent(event: MouseEventData<NativeEventTarget<any>>): MouseEventData<NativeEventTarget<any>> {
        return event;
    }

    override onAttachViewport() {
        this.panFeature.registerPanHandler(this.toolbar.viewport.id, this.editor.editorType, this);
    }

    /**
     * Checks if the translation has reached the movement boundary based on mouse movement and zoom.
     *
     * @param deltaX - The horizontal movement delta (negative or positive).
     * @param deltaY - The vertical movement delta (negative or positive).
     * @param wrapperRect - The bounding rectangle of the video wrapper element.
     * @param zoom - The current zoom level of the viewport.
     * @param translation - The current translation [x, y] coordinates.
     * @returns True if the translation is at the boundary and should not move further; otherwise false.
     */
    private isAtBoundary(
        deltaX: number,
        deltaY: number,
        wrapperRect: DOMRect,
        zoom: number,
        translation: number[]
    ): boolean {
        const halfWidth = (wrapperRect.width * zoom) / 2;
        const halfHeight = (wrapperRect.height * zoom) / 2;
        const [transX, transY] = translation;

        const atHorizontal = (deltaX > 0 && transX === halfWidth) || (deltaX < 0 && transX === -halfWidth);

        const atVertical = (deltaY < 0 && transY === halfHeight) || (deltaY > 0 && transY === -halfHeight);

        return atHorizontal || atVertical;
    }

    isPanHandleAble(docCtrl: ShareScreenDocCtrl, event: PointerEventData<NativeEventTarget<any>>): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        try {
            if (event instanceof DefaultPointerEventData) {
                const mousePos = mouseLocation(event);
                const vpZoom = (this.toolbar.viewport as BoardViewportManager).zoomLevel;

                const deltaX = -event.nativeEvent.movementX;
                const deltaY = -event.nativeEvent.movementY;

                const videoWrapper = docCtrl.getComponent().videoWrapperRef.nativeElement;
                const wrapperRect = videoWrapper.getBoundingClientRect();

                if (
                    !validatePointerPos(mousePos, docCtrl) ||
                    this.isAtBoundary(deltaX, deltaY, wrapperRect, vpZoom, docCtrl.translation)
                ) {
                    return false;
                }
                return true;
            }
        } catch (error) {
            return false;
        }

        return false;
    }

    isPanHandling(docCtrl: ShareScreenDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        if (this.toolState.doc === docCtrl) return true;
        return false;
    }

    startPan(docCtrl: ShareScreenDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        this.toolState.doc = docCtrl;
        this.started = true;
    }

    stopPan(docCtrl: ShareScreenDocCtrl) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (this.toolState.doc !== docCtrl) return;

        this.clear();
    }

    translateViewpoint(docCtrl: ShareScreenDocCtrl, deltaXInScreen: number, deltaYInScreen: number) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        if (docCtrl !== this.toolState.doc || !docCtrl.translation) return;

        const x = docCtrl.translation[0] + deltaXInScreen * docCtrl.zoomLevel;
        const y = docCtrl.translation[1] - deltaYInScreen * docCtrl.zoomLevel;

        docCtrl.pan(x, y);

        this.toolbar.update('ShareScreenPanTool', this.toolState);
    }
}
