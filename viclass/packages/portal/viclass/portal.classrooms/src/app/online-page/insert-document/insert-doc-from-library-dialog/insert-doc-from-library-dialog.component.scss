::ng-deep .insert-from-library-popup {
    max-width: max(70vw, 800px) !important;
}

::ng-deep .insert-from-library-popup .mat-mdc-dialog-container {
    border-radius: 35px !important;
    min-width: 500px;
    max-height: calc(100vh - 100px) !important;
    overflow: hidden !important;
}

::ng-deep .insert-from-library-popup .mat-mdc-dialog-container .mdc-dialog__surface {
    overflow: hidden !important;
    background: transparent !important;
}

::ng-deep .insert-from-library-popup app-insert-doc-from-library-dialog {
    max-height: inherit;
    display: block;
}

::ng-deep .insert-from-library-popup .mat-mdc-dialog-container .popup-container {
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

@media (min-width: 1200px) {
    ::ng-deep .insert-from-library-popup .mat-mdc-dialog-container {
        min-width: 500px;
    }
}
