import {
    <PERSON><PERSON><PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    ToolEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { ShareScreenToolType } from '../sharescreen.api';
import { ShareScreenEditor } from '../sharescreen.editor';
import { ShareScreenDocEvent, ShareScreenDocFocusedES, ShareScreenToolEventData } from './models';
import { ShareScreenTool } from './share-screen.tool';

const LISTEN_DOC_EVENT_TOOLS: ShareScreenToolType[] = ['ShareScreenZoomTool'];

export class ShareScreenToolBar extends DefaultToolBar<ShareScreenToolType, ShareScreenTool<ToolState>> {
    override keyboardHandler?: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler?: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    toolListener: ToolEventListener<ShareScreenToolBar, any>;
    maghDocEventListener: VEventListener<ShareScreenDocEvent>;

    constructor(private editor: ShareScreenEditor) {
        super(editor.coordinator);
        this.keyboardHandler = new this._keyboardHandler(this);

        this.toolListener = new this._toolListener(this);
        this.maghDocEventListener = new this.ShareScreenDocEventListener(this);
    }

    protected override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        if (vpMode == 'Disabled') this.disable();
        else this.enable();

        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    switch (type) {
                        case 'ShareScreenZoomTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'ViewMode': {
                    switch (type) {
                        case 'ShareScreenZoomTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                            break;
                        }
                    }
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);

        this.editor.toolbars.set(viewport.id, this);
        this.editor.selectDelegator.registerDocEventListener(this.maghDocEventListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);

        if (this.maghDocEventListener) {
            this.editor.selectDelegator.unregisterDocEventListener(this.maghDocEventListener);
        }

        this.editor.toolbars.delete(viewport.id);
    }

    private _toolListener = class implements ToolEventListener<ShareScreenToolBar, any> {
        constructor(private toolbar: ShareScreenToolBar) {}

        onEvent(eventData: ShareScreenToolEventData): ShareScreenToolEventData | Promise<ShareScreenToolEventData> {
            if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(eventData.toolType)) return eventData;
            const tool = eventData.source.getTool(eventData.toolType);
            return tool ? tool.handleToolEvent(eventData) : eventData;
        }
    };

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: ShareScreenToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-edit-mode': {
                        this.toolbar.onViewportModeChanged('EditMode');
                        break;
                    }
                    case 'viewport-interactive-mode': {
                        this.toolbar.onViewportModeChanged('InteractiveMode');
                        break;
                    }
                    case 'viewport-view-mode': {
                        this.toolbar.onViewportModeChanged('ViewMode');
                        break;
                    }
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(eventData.state.vmId, evs);
                        this.toolbar.onViewportModeChanged('Disabled');
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    override addTool(toolType: ShareScreenToolType, tool: ShareScreenTool<ToolState>): void {
        super.addTool(toolType, tool);
        tool.registerToolbar(this);
    }

    private ShareScreenDocEventListener = class implements VEventListener<ShareScreenDocEvent> {
        constructor(private toolbar: ShareScreenToolBar) {}

        onEvent(eventData: ShareScreenDocEvent): ShareScreenDocEvent | Promise<ShareScreenDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: ShareScreenToolBar) {}

        onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (this.toolbar.isDisabled() || event.nativeEvent.repeat) return event;
            // currently nothing to do
            return event;
        }
    };
}
