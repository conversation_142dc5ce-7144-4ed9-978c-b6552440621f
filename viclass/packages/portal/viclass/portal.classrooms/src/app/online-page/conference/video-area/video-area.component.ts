import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, OnDestroy, ViewChild } from '@angular/core';
import { LSessionRegistrationModel } from '@viclass/portal.common';
import { BehaviorSubject, combineLatest, map, Observable, Subscription, take } from 'rxjs';
import { MemberStateService } from '../../member.state.service';
import { ShareScreenInfo, ShareScreenService } from '../../share-screen-editor/share-screen.service';
import { ClassroomConferenceService, ParticipantStream } from '../classroom.conference.service';
import { ShareScreenStreamTileComponent } from '../sharescreen-stream-tile/sharescreen-stream-tile.component';
import { StreamTileComponent } from '../stream-tile/stream-tile.component';
type VideoItem =
    | {
          data: string;
          type: 'camera';
      }
    | {
          data: ShareScreenInfo;
          type: 'shareScreen';
      };

@Component({
    standalone: true,
    imports: [CommonModule, StreamTileComponent, ShareScreenStreamTileComponent],
    selector: '[video-area]',
    templateUrl: './video-area.component.html',
    styleUrls: ['./video-area.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VideoAreaComponent implements AfterViewInit, OnDestroy {
    private actiVideoForUserId: string;
    rsSub: Subscription;
    isAutoLargeResizePresenter: boolean = true;
    userRegIdLargeResize: string;
    resizeObserver!: ResizeObserver;

    private startIndex$ = new BehaviorSubject<number>(0);
    showLeftButton$!: Observable<boolean>;
    showRightButton$!: Observable<boolean>;

    maxVisibleItems = 4;
    visibleItems$: Observable<VideoItem[]>;
    totalItems$!: Observable<number>;

    allItems$: Observable<VideoItem[]>;

    constructor(
        public confS: ClassroomConferenceService,
        public memberStateS: MemberStateService,
        private shareScreenService: ShareScreenService
    ) {
        this.allItems$ = combineLatest([this.confS.listShowCamera, this.shareScreenService.sharedScreens$]).pipe(
            map(([cams, shareScreens]) => [
                ...cams
                    .filter(
                        userId =>
                            this.memberStateS.memberByUserId$(userId)?.value?.userState.raiseHandStatus != 'PRESENTING'
                    )
                    .map(data => ({ data: data as string, type: 'camera' }) as VideoItem),
                ...shareScreens.map(data => ({ data: data as ShareScreenInfo, type: 'shareScreen' }) as VideoItem),
            ])
        );

        this.visibleItems$ = combineLatest([this.allItems$, this.startIndex$]).pipe(
            map(([items, startIndex]) => {
                return items.slice(startIndex, startIndex + this.maxVisibleItems);
            })
        );

        this.showRightButton$ = combineLatest([this.allItems$, this.startIndex$]).pipe(
            map(([items, startIndex]) => startIndex + this.maxVisibleItems < items.length)
        );

        this.showLeftButton$ = this.startIndex$.pipe(map(startIndex => startIndex > 0));
    }

    slideRight() {
        this.showRightButton$
            .pipe(
                take(1),
                map(canSlideRight => {
                    if (canSlideRight) {
                        this.startIndex$.next(this.startIndex$.value + 1);
                    }
                })
            )
            .subscribe();
    }

    slideLeft() {
        this.showLeftButton$
            .pipe(
                take(1),
                map(canSlideLeft => {
                    if (canSlideLeft) {
                        this.startIndex$.next(this.startIndex$.value - 1);
                    }
                })
            )
            .subscribe();
    }

    @ViewChild('slider') slider!: ElementRef;
    ngOnDestroy(): void {
        this.rsSub.unsubscribe();
    }
    ngAfterViewInit(): void {
        this.rsSub = this.confS.remoteStreams.subscribe(streams => {
            const listShowCamera = this.confS.listShowCamera.value;
            for (const stream of streams) {
                if (listShowCamera.indexOf(stream.userId) >= 0) {
                    if (stream.showVideo.value === false) stream.showVideo.next(true);
                }
            }
        });

        if (this.slider?.nativeElement) {
            this.resizeObserver.observe(this.slider.nativeElement);
        }
    }

    member$(regId): Observable<LSessionRegistrationModel> {
        return this.memberStateS.member$(regId);
    }

    get listShowCameraUserIds$(): Observable<string[]> {
        return this.confS.listShowCamera;
    }

    /**
     * Returns an observable stream of shared screen information.
     */
    get listShareScreen$(): Observable<ShareScreenInfo[]> {
        return this.shareScreenService.sharedScreens$;
    }

    /**
     * Observable that provides the member who is currently presenting.
     * It listens for changes in member states and ensures that the presenter's video is displayed automatically.
     *
     * @returns An Observable that emits the `LSessionRegistrationModel` of the presenting member.
     */
    get presentingMember$(): Observable<LSessionRegistrationModel> {
        return this.memberStateS.members$.pipe(
            map(members => {
                const mP = members.find(m => m.userState?.raiseHandStatus === 'PRESENTING');

                const stream = this.confS.getStreamByVid(mP?.profile?.id);

                // hide video for previous presenter not selected for video show
                if (this.actiVideoForUserId && this.actiVideoForUserId !== mP?.profile?.id) {
                    this.confS.getStreamByVid(this.actiVideoForUserId).showVideo.next(false);
                    this.actiVideoForUserId = null;
                }

                // auto show video for presenter
                if (stream && stream.hasVideo.value && !stream.showVideo.value) {
                    stream.showVideo.next(true);
                    this.actiVideoForUserId = mP?.profile?.id;
                }
                return mP;
            })
        );
    }

    onCloseVideo(stream: Partial<ParticipantStream>) {
        this.confS.hideUserTrack(stream.userId);
    }

    onLargeSize(regId: string, isPresenter: boolean = false) {
        if (isPresenter) {
            this.userRegIdLargeResize = null;
            this.isAutoLargeResizePresenter = true;
        } else {
            if (this.userRegIdLargeResize == regId) this.userRegIdLargeResize = null;
            else this.userRegIdLargeResize = regId;
            this.isAutoLargeResizePresenter = false;
        }
    }
}
