<div class="min-w-[320px] min-h-[100px] max-h-[calc(100vh-120px)] overflow-y-auto overflow-x-hidden">
    <div class="flex gap-3 items-center">
        <button
            [ngClass]="{
                'text-P1': selectedTab === 'settings',
            }"
            (click)="selectedTab = 'settings'"
            class="flex gap-1 items-center"
            #viewSettingEl>
            <span class="vcon vcon-onl vcon_doc_setting"></span>
            <span *ngIf="selectedTab === 'settings'" class="text-[12px] font-[600]">HIỂN THỊ TÀI LIỆU </span>
            <lib-tooltip [toolTipFor]="viewSettingEl" tooltipContent="Hiển thị tài liệu"></lib-tooltip>
        </button>
        <button
            [ngClass]="{
                'text-P1': selectedTab === 'presenter',
            }"
            (click)="selectedTab = 'presenter'"
            class="flex gap-1 items-center"
            #presentingToolEl>
            <span class="vcon vcon-onl vcon_live_setting"></span>
            <span *ngIf="selectedTab === 'presenter'" class="text-[12px] font-[600]">CÔNG CỤ TRÌNH CHIẾU </span>
            <lib-tooltip [toolTipFor]="presentingToolEl" tooltipContent="Công cụ trình chiếu"></lib-tooltip>
        </button>

        <div class="flex-grow"></div>
        <button (click)="onClose.emit(true)">
            <span class="vcon vcon-general vcon_delete text-BW1 text-[13px]"></span>
        </button>
    </div>

    <div class="bg-P1 h-[1px]" style="margin: 0.75rem 0"></div>

    <div *ngIf="selectedTab === 'settings'" class="pt-3 leading-[21px] text-[14px] w-full">
        <div class="flex gap-3 overflow-y-auto overflow-x-hidden h-full w-full" *ngIf="!!setting">
            <div class="flex gap-3 flex-col w-full">
                <lib-setting-tool-switch
                    label="Màu nền"
                    field="background"
                    [disabled]="!(isEditable$ | async)"
                    [value]="{ value: setting.background }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <div class="border border-b !border-black"></div>
                <lib-setting-tool-colors
                    [colorList]="bgColorList"
                    field="backgroundColor"
                    [value]="{ value: setting.backgroundColor }"
                    [disabled]="!setting.background || !(isEditable$ | async)"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-colors>

                <br />

                <lib-setting-tool-switch
                    label="Bóng đổ"
                    field="shadow"
                    [disabled]="!(isEditable$ | async)"
                    [value]="{ value: setting.shadow }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <div class="border border-b !border-black"></div>
                <div class="flex gap-6">
                    <div *ngFor="let shadowType of shadowList" class="flex gap-2 items-start">
                        <input
                            type="radio"
                            name="shadow"
                            [(ngModel)]="setting.shadowType"
                            [value]="shadowType"
                            (ngModelChange)="onShadowChange($event)"
                            [disabled]="!setting.shadow || !(isEditable$ | async)" />
                        <div
                            (click)="changeShadowType(shadowType)"
                            [class]="'w-[20px] h-[20px] cursor-pointer doc-layer-shadow-' + shadowType"
                            [ngClass]="{
                                'opacity-50 pointer-events-none !cursor-not-allowed':
                                    !setting.shadow || !(isEditable$ | async),
                            }"></div>
                    </div>
                </div>

                <br />

                <lib-setting-tool-switch
                    label="Khung viền"
                    field="border"
                    [disabled]="!(isEditable$ | async)"
                    [value]="{ value: setting.border }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <div class="border border-b !border-black"></div>
                <div class="flex gap-6">
                    <div *ngFor="let borderType of borderList" class="flex gap-2 items-start">
                        <input
                            type="radio"
                            name="border"
                            [(ngModel)]="setting.borderType"
                            [value]="borderType"
                            (ngModelChange)="onBorderChange($event)"
                            [disabled]="!setting.border || !(isEditable$ | async)" />
                        <div
                            (click)="changeBorderType(borderType)"
                            [class]="'w-[20px] h-[20px] cursor-pointer !outline-[3px] doc-layer-border-' + borderType"
                            [ngClass]="{
                                'opacity-50 pointer-events-none !cursor-not-allowed':
                                    !setting.border || !(isEditable$ | async),
                            }"></div>
                    </div>
                </div>

                <lib-setting-tool-colors
                    label="Màu"
                    [colorList]="colorList"
                    field="borderColor"
                    [value]="{ value: setting.borderColor }"
                    [disabled]="!setting.border || !(isEditable$ | async)"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-colors>
            </div>
        </div>
    </div>

    <div *ngIf="selectedTab === 'presenter'" class="pt-3 leading-[21px] text-[14px] w-full">
        <div class="flex gap-3 overflow-y-auto overflow-x-hidden h-[350px]" *ngIf="!!setting">
            <div class="flex gap-3 flex-col w-full">
                <lib-setting-tool-switch
                    label="Theo dõi người thuyết trình"
                    field="syncPresenterState"
                    [value]="{ value: presenter.presenterSetting.syncPresenterState }"
                    (onChange)="onPresenterChange($event)"></lib-setting-tool-switch>
                <div class="border border-b !border-black"></div>
                <div>Xem toàn bộ theo:</div>
                <div>
                    <div *ngFor="let fitScreen of fitScreens" class="flex gap-2 items-center">
                        <input
                            type="radio"
                            name="fitScreen"
                            [(ngModel)]="presenter.presenterSetting.fitScreen"
                            field="fitScreen"
                            [value]="fitScreen.value"
                            (click)="onFitScreenTypeChange(fitScreen.value)"
                            [disabled]="!presenter.presenterSetting.syncPresenterState" />
                        <div>
                            <span>{{ fitScreen.name }}</span>
                        </div>
                    </div>
                </div>
                <br />
                <div class="w-full flex justify-start">
                    <button
                        (click)="openCameraSetting()"
                        class="vi-btn vi-btn-small vi-btn-outline inline-block !text-sm !h-[30px] !py-[.5rem]"
                        #cameraSettingEl>
                        <i class="vcon vcon-onl vcon_media"></i>
                        Cài đặt
                        <lib-tooltip
                            [toolTipFor]="cameraSettingEl"
                            tooltipContent="Cài đặt camera và âm thanh"></lib-tooltip>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
