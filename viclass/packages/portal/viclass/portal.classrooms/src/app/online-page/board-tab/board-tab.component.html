<div
    *ngIf="stateObs$ | async as state"
    class="flex flex-col ps-0 pe-0 min-w-[100px] hover:!max-w-none max-w-[175px]"
    [ngClass]="{
        '!max-w-none': (coordStateS.selected$ | async) === state.id || (showMenu | async),
    }">
    <div class="board-tab pointer-events-auto">
        <div
            class="d-flex v-tool-label-btn w-full ms-0 me-0 relative click-indicator"
            [ngClass]="{
                hover: showMenu | async,
                selected: (coordStateS.selected$ | async) === state.id,
            }">
            <ng-container *ngIf="avatarModel$ | async as avatar">
                <div *ngIf="avatar.owner != userId" class="w-[20px] h-[20px] mt-auto mb-auto ml-[-12px] mr-1">
                    <div
                        class="avatar-img w-[20px] h-[20px]"
                        [style.background-image]="avatar?.avatarUrl ? 'url(' + avatar.avatarUrl + ')' : ''"></div>
                </div>
            </ng-container>
            <div
                class="d-flex flex-row w-full"
                (contextmenu)="onRightClick($event)"
                #menuTrigger="matMenuTrigger"
                [matMenuTriggerFor]="menu">
                <span
                    [hidden]="renaming | async"
                    (click)="selectPage(); $event.stopPropagation()"
                    class="w-full overflow-hidden whitespace-nowrap text-ellipsis text-center">
                    {{ state.title }}
                </span>
                <input
                    type="text"
                    tabindex="0"
                    class="z-[1]"
                    [hidden]="!(renaming | async)"
                    #renameInput
                    (keydown)="onKeydown($event)"
                    (click)="$event.stopPropagation()"
                    (focusout)="submitRename(renameInput.value)" />
            </div>
            <div
                *ngIf="!(boardProcessing$ | async)"
                class="board-tab-action !absolute top-0 right-[5px] rounded-[0_15px_15px_0] h-[30px] bg-inherit items-center"
                [ngClass]="{ '!flex': showMenu | async }">
                <button
                    class="vcon vcon-onl vcon_general_action-list pe-0"
                    (click)="$event.stopPropagation()"
                    [matMenuTriggerFor]="menu"
                    (menuClosed)="showMenu.next(false)"
                    (menuOpened)="adjustMenuPosition(); showMenu.next(true)"></button>
            </div>
            <mat-menu #menu="matMenu" yPosition="below" xPosition="before">
                <button mat-menu-item *ngIf="pinnable$ | async" (click)="pin()">Ghim</button>
                <button mat-menu-item *ngIf="requestPinnable$ | async" (click)="requestPin()">Xin ghim</button>
                <button
                    mat-menu-item
                    *ngIf="(requestingPin$ | async) || (pendingPin$ | async)"
                    (click)="cancelRequestPin()">
                    Hủy xin ghim
                </button>
                <button mat-menu-item *ngIf="unpinnable$ | async" (click)="unpin()">Bỏ ghim</button>
                <button mat-menu-item (click)="duplicatePage()">Tạo bản sao</button>
                <button mat-menu-item *ngIf="presentable$ | async" (click)="presentPage()">Trình chiếu</button>
                <button mat-menu-item *ngIf="renamable" (click)="renamePage()">Đổi tên</button>
                <button mat-menu-item *ngIf="removable" (click)="removePage()">Xóa</button>
            </mat-menu>
            <div
                class="!absolute top-0 right-[5px] flex rounded-[0_15px_15px_0] h-[30px] bg-inherit items-center"
                *ngIf="boardProcessing$ | async"
                [spinner]="boardProcessing$"></div>
        </div>
    </div>
    <ng-container
        *ngIf="state.default || state.presenting || state.pinned || (requestingPin$ | async) || (pendingPin$ | async)">
        <div class="flex gap-[5px] h-fit w-full justify-center pointer-events-none">
            <span
                *ngIf="state.default"
                class="w-[16px] h-[16px] leading-[16px] text-[16px] bg-TP3 justify-center vcon-onl vcon_whiteboard_default-tab rounded-b-sm"></span>
            <div
                *ngIf="state.presenting"
                class="w-[16px] h-[16px] bg-TP3 place-content-center place-items-center rounded-b-sm">
                <img class="w-[14px] h-[14px]" ngSrc="assets/img/play-icon.svg" alt="" height="16" width="16" />
            </div>
            <span
                *ngIf="state.pinned"
                class="w-[16px] h-[16px] leading-[16px] text-[16px] bg-TP3 justify-center rounded-b-sm vcon-onl vcon_whiteboard_pinned"></span>
            <div
                *ngIf="(requestingPin$ | async) || (pendingPin$ | async)"
                class="relative flex items-center justify-center w-[16px] h-[16px]">
                <span
                    class="flex items-center justify-center w-full h-full leading-[16px] text-[16px] text-BW4 bg-TP3 rounded-b-sm vcon-onl vcon_whiteboard_pinned">
                </span>
                <div
                    class="absolute inset-0 rounded-full border-2 border-solid border-gray-200 border-t-blue-500 animate-spin"
                    role="status"
                    aria-live="polite">
                    <span class="sr-only">Đang xử lý...</span>
                </div>
            </div>
        </div>
    </ng-container>
</div>
