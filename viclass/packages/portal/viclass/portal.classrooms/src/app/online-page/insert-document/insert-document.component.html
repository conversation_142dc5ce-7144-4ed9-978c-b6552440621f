<div class="bg-BW5 flex items-center justify-center gap-10 py-1">
    <div
        class="click-indicator text-xs hover:bg-P2 flex justify-start items-center p-1 gap-4 rounded-full"
        [ngClass]="{
            'cursor-pointer': (isEditMode$ | async),
            'opacity-50': !(isEditMode$ | async),
        }"
        #fromLibraryEl
        (click)="openInsertFromLibrary()">
        <span class="vcon vcon-onl vcon_library"></span>
    </div>
    <div
        class="click-indicator text-xs hover:bg-P2 flex justify-start items-center p-1 gap-4 rounded-full opacity-50"
        #fromComputerEl>
        <span class="vcon vcon-general vcon_general_upload"></span>
    </div>
</div>
<lib-tooltip [toolTipFor]="fromLibraryEl" [tooltipContent]="'Chèn từ thư viện'"></lib-tooltip>
<lib-tooltip [toolTipFor]="fromComputerEl" [tooltipContent]="'<PERSON><PERSON><PERSON> từ máy tính (đang phát triển)'"></lib-tooltip>
