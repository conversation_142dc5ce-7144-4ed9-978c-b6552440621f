<div class="vi-popup">
    <div class="vi-popup-container">
        <div class="pt-[15px] pb-[15px] gap-[10px] d-flex flex-col">
            <div class="flex justify-center">
                <img class="w-[160px] h-[100px]" ngSrc="assets/img/waiting.svg" height="100" width="160" />
            </div>
            <div class="vi-popup-message">
                <span>Buổi học đã kết thúc</span>
            </div>
            <div *ngIf="isRecorded" class="d-flex flex-row gap-[10px] text-center justify-center">
                <button class="vi-btn vi-btn-small vi-btn-focus" (click)="navigateToRecord()">
                    Xem lại buổi học ngay
                </button>
            </div>
        </div>
        <div class="vi-popup-note">
            <p class="text-center pb-1">
                <PERSON>y<PERSON><PERSON> tới buổi học sau
                <strong>{{ counter$ | async }}</strong>
                s<br />
                Hoặc <br />
                <a class="vi-btn vi-btn-small vi-btn-gradient !inline-block max-w-max" href="/profile/classrooms">
                    Chuyển ngay
                </a>
            </p>
        </div>
    </div>
</div>
