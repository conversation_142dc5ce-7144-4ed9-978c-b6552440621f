import { Injectable } from '@angular/core';
import {
    <PERSON><PERSON><PERSON>,
    ErrorHandlerDecorator,
    isLocalContentDocId,
    MuitlDocInfoCES,
    NewDocChangeCES,
    VEventListener,
    ViewportId,
} from '@viclass/editor.core';
import { UserService } from '@viclass/portal.common';
import { firstValueFrom, take } from 'rxjs';
import { AppStateService } from '../app.state.service';
import { CcsGateway } from '../gateways/ccs.gateway';
import {
    CreateDocumentInfoRequest,
    DocumentInfo,
    DocumentInfoDetail,
    LoadDocumentInfoRequest,
    MarkValidDocument,
    MarkValidMultiDocumentRequest,
} from '../gateways/ccs.model';
import { CoordStatesService } from './coord.state.service';
import { DocinfoStateService } from './docinfo.state.service';
import { classroomErrorHandler } from './error-handler';
import { MemberStateService } from './member.state.service';
import { OnlineStateService } from './online.state.service';
import { ViewportContentEventListener } from './viewport.content.event.listener';

@Injectable()
export class CoordinatorEventListener implements VEventListener<CoordinatorEvent> {
    constructor(
        private readonly appS: AppStateService,
        private readonly uS: UserService,
        private readonly memberStateService: MemberStateService,
        private readonly onlStateS: OnlineStateService,
        private readonly coordStateS: CoordStatesService,
        private readonly docInfoStateS: DocinfoStateService,
        private readonly ccsGateway: CcsGateway,
        private readonly vpContentListener: ViewportContentEventListener
    ) {}

    /**
     * Listen event from coordinator
     * @param event
     */
    @ErrorHandlerDecorator([classroomErrorHandler])
    async onEvent(event: CoordinatorEvent): Promise<CoordinatorEvent> {
        const eventState = event.state;
        const user = await firstValueFrom(this.uS.curUser$);
        const memberInfo = this.memberStateService.getMemberByUserId(user.id);

        switch (event.eventType) {
            case 'viewport-added': {
                break;
            }
            case 'viewport-selected': {
                this.coordStateS.select(eventState.vmId);
                const coord = await firstValueFrom(this.onlStateS.coordinator$);
                coord.getViewportManager(eventState.vmId).registerContentEventListener(this.vpContentListener);
                break;
            }

            case 'viewport-disabled': {
                const coord = await firstValueFrom(this.onlStateS.coordinator$);
                coord.getViewportManager(eventState.vmId).unregisterContentEventListener(this.vpContentListener);
                break;
            }

            case 'viewport-removed': {
                this.coordStateS.remove(eventState.vmId);
                const coord = await firstValueFrom(this.onlStateS.coordinator$);
                this.coordStateS.presenting$.pipe(take(1)).subscribe(id => async () => {
                    const vpMode = await this.onlStateS.calculateViewportMode(id);
                    await coord.switchViewportMode(id, vpMode);
                    await coord.switchViewport(id);
                });
                break;
            }

            case 'new-doc': {
                const coord = await firstValueFrom(this.onlStateS.coordinator$);
                const state = eventState as NewDocChangeCES;
                const docs = state.docs;

                if (state.isReload) {
                    const markValidDocs: MarkValidDocument[] = [];

                    for (const doc of docs) {
                        for (const expChange of doc.expectedChanges) {
                            // TODO: local content
                            if (isLocalContentDocId(expChange.globalId)) continue;

                            markValidDocs.push({
                                docGlobalId: expChange.globalId,
                                isValid: true,
                                editorType: doc.editor.editorType,
                            });
                        }
                    }

                    if (markValidDocs.length > 0) {
                        await this.markDocValid(docs[0].vmId, markValidDocs);
                    }
                    break;
                }

                const request: CreateDocumentInfoRequest = {
                    peerId: coord.peerId,
                    coordStateId: docs[0].vmId,
                    docs: [],
                };

                try {
                    for (const doc of docs) {
                        for (const [index, expChange] of doc.expectedChanges.entries()) {
                            // TODO: local content
                            if (isLocalContentDocId(expChange.globalId)) continue;

                            let docName = 'Untitled Document';
                            if (doc.cmdType == 'insert-doc') {
                                if (doc.original[index]?.docName) {
                                    docName = doc.original[index]?.docName;
                                } else {
                                    let docInfo = this.docInfoStateS.getDocInfo(doc.vmId, doc.original[index].globalId);

                                    if (!docInfo) {
                                        docInfo = (
                                            await firstValueFrom(
                                                this.ccsGateway.loadDocumentInfo({
                                                    peerId: coord.peerId,
                                                    docGlobalId: doc.original[index].globalId,
                                                    coordStateId: doc.vmId,
                                                    editorType: doc.editor.editorType,
                                                })
                                            )
                                        )[0];
                                    }

                                    if (docInfo) docName = docInfo.details.docName + ' copy';
                                }
                            }
                            const docInfoDetail: DocumentInfoDetail = {
                                isValid: true,
                                classroomId: this.appS.lsId,
                                coordStateId: doc.vmId,
                                docGlobalId: expChange.globalId,
                                docLocalId: expChange.localId,
                                docName: docName,
                                ownerUserId: user.id,
                                ownerUserName: user.username,
                                ownerRegId: memberInfo.id,
                            };
                            const docInfo: DocumentInfo = {
                                editorType: doc.editor.editorType,
                                docGlobalId: expChange.globalId,
                                details: docInfoDetail,
                            };
                            request.docs.push(docInfo);
                        }
                    }

                    if (request.docs.length > 0) {
                        await firstValueFrom(this.ccsGateway.createDocumentInfo(request));
                    }
                } catch (err) {
                    console.warn('load original doc failed... ', err);
                }
                break;
            }

            case 'load-doc': {
                const coord = await firstValueFrom(this.onlStateS.coordinator$);
                const originalEventStates = (eventState as MuitlDocInfoCES).docInfos;

                const requests: LoadDocumentInfoRequest[] = [];
                for (const originalEventState of originalEventStates) {
                    // TODO: local content
                    if (isLocalContentDocId(originalEventState.docGlobalId)) continue;

                    requests.push({
                        peerId: coord.peerId,
                        coordStateId: originalEventState.coordStateId,
                        docGlobalId: originalEventState.docGlobalId,
                    });
                }

                if (requests.length > 0) {
                    const docInfos = await firstValueFrom(this.ccsGateway.loadDocumentInfos([...requests]));
                    this.docInfoStateS.addDocInfos(originalEventStates[0].vmId, docInfos);
                }

                break;
            }

            case 'remove-doc': {
                const state = eventState as NewDocChangeCES;
                const docs = state.docs;
                const markValidDocs: MarkValidDocument[] = [];
                for (const doc of docs) {
                    for (const expChange of doc.expectedChanges) {
                        // TODO: local content
                        if (isLocalContentDocId(expChange.globalId)) continue;

                        const req: MarkValidDocument = {
                            docGlobalId: expChange.globalId,
                            isValid: false,
                            editorType: doc.editor.editorType,
                        };
                        markValidDocs.push(req);
                    }
                }

                if (markValidDocs.length > 0) {
                    await this.markDocValid(docs[0].vmId, markValidDocs);
                }

                break;
            }

            case 'remove-doc-state': {
                const originalEventState = eventState as MuitlDocInfoCES;
                const docs = originalEventState.docInfos;
                docs.forEach(doc => {
                    // TODO: local content
                    if (isLocalContentDocId(doc.docGlobalId)) return;
                });
                // TODO: local content
                this.docInfoStateS.removeDocInfos(docs[0].vmId, [
                    ...docs.filter(e => !isLocalContentDocId(e.docGlobalId)).map(e => e.docGlobalId),
                ]);

                break;
            }

            default:
                break;
        }

        return event;
    }
    onUnregister?: () => void;

    @ErrorHandlerDecorator([classroomErrorHandler])
    private async markDocValid(vmId: ViewportId, docs: MarkValidDocument[]) {
        const coord = await firstValueFrom(this.onlStateS.coordinator$);
        const request: MarkValidMultiDocumentRequest = {
            coordStateId: vmId,
            peerId: coord.peerId,
            docs: [...docs],
        };

        const docInfos = await firstValueFrom(this.ccsGateway.markValidDocumentInfos(request));
        this.docInfoStateS.addDocInfos(vmId, [...docInfos]);
    }
}
