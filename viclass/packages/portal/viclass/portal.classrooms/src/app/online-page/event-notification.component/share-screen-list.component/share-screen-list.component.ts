import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { LSessionRegistrationModel, TooltipComponent } from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs';
import { MemberItemEventType } from '../../../model';
import { ShareScreenItemComponent } from './share-screen-item.component/share-screen-item.component';

@Component({
    selector: 'app-share-screen-list',
    standalone: true,
    imports: [CommonModule, ShareScreenItemComponent, TooltipComponent],
    templateUrl: './share-screen-list.component.html',
    styleUrls: ['./share-screen-list.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShareScreenListComponent implements OnInit, OnDestroy {
    private iSub: Subscription;

    showList = false;
    isShowScrollUp = false;
    isShowScrollDown = false;

    @Input() numberColor: string;
    @Input() notification: string;
    @Input() items: BehaviorSubject<LSessionRegistrationModel[]>;
    @Input() notiTitle: string = '';

    @ViewChild('listContainer') listContainer!: ElementRef;
    @ViewChild('scrollableList') scrollableList!: ElementRef;

    constructor(private cdr: ChangeDetectorRef) {}
    ngOnDestroy(): void {
        if (this.iSub) {
            this.iSub.unsubscribe();
        }
    }

    ngOnInit() {
        this.iSub = this.items.subscribe(item => {
            this.cdr.markForCheck();
        });
    }

    get calculatedHeight() {
        const maxItems = Math.min(this.items.value.length, 5);
        return `${maxItems * 45 + (maxItems - 1) * 10}px`;
    }

    get isShowScroll() {
        return this.showList && this.items.value.length > 5;
    }

    private updateScrollButtons() {
        if (this.scrollableList) {
            const element = this.scrollableList.nativeElement;
            this.isShowScrollUp = element.scrollTop > 0;
            this.isShowScrollDown = element.scrollHeight > element.clientHeight + element.scrollTop;
        }
    }

    scrollUp() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop -= 45; // Scroll up by 45px
            this.updateScrollButtons();
        }
    }

    scrollDown() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop += 45; // Scroll down by 45px
            this.updateScrollButtons();
        }
    }

    onScroll() {
        this.updateScrollButtons();
    }

    toggleList() {
        this.showList = !this.showList;
        this.cdr.markForCheck();

        if (this.showList) {
            setTimeout(() => {
                this.updateScrollButtons();
                this.cdr.markForCheck(); // Trigger check after timeout
            }, 500);
        } else {
            this.scrollableList.nativeElement.scrollTop = 0;
        }
    }
}
