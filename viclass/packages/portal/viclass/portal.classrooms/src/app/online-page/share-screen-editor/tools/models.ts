import {
    DocFocused<PERSON>,
    FocusDocEvent,
    MouseEventData,
    NativeEventTarget,
    ToolEventData,
    ToolState,
} from '@viclass/editor.core';

import { ShareScreenDocCtrl } from '../docs/sharescreen.doc.ctrl';
import { ShareScreenToolBar } from './share-screen.toolbar';

export type ShareScreenToolType = 'ShareScreenZoomTool' | 'ShareScreenPanTool';

export type ShareScreenDocFocusedES = DocFocusedES<ShareScreenDocCtrl>;
export type ShareScreenDocEvent = FocusDocEvent<ShareScreenDocCtrl>;
export type ShareScreenToolEventData = ToolEventData<ShareScreenToolBar, ShareScreenToolType>;

export type HeadingWrapperStyle = {
    fontFamily: string;
    fontSize: string;
    fontColor: string;
    backgroundColor: string;
    isBold: boolean;
    isItalic: boolean;
    isUnderline: boolean;
    isStrikethrough: boolean;
};

export class ShareScreenZoomToolState implements ToolState {
    zoomLevel: number;
}
export class ShareScreenPanToolState implements ToolState {
    doc: ShareScreenDocCtrl;
}

export type ShareScreenMouseEvent = MouseEventData<NativeEventTarget<any>>;

export class ShareScreenSettingsState implements ToolState {
    axis: boolean = true;
    grid: boolean = true;
    detailGrid: boolean = false;
    lineStyle: string = '';
    lineWidth: number = 2;
    opacity: number = 1;
}
