import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    Input,
    On<PERSON><PERSON><PERSON>,
    QueryList,
    ViewChildren,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { ClassroomConferenceService, ParticipantStream } from '../classroom.conference.service';

/**
 * The component that manage a video element
 */
@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'audio-tile',
    templateUrl: './audio-tile.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AudioTileComponent implements AfterViewInit, OnDestroy {
    @Input()
    stream: ParticipantStream;

    @ViewChildren('audioEl')
    audioQL: QueryList<ElementRef<HTMLAudioElement>>;

    audioSubscription: Subscription;

    private currentAudioElement?: HTMLAudioElement;
    constructor(public confS: ClassroomConferenceService) {}

    ngOnDestroy(): void {
        this.audioTrackDetach();
    }

    ngAfterViewInit() {
        this.audioTrackAttach();
        this.audioSubscription = this.audioQL.changes.subscribe(() => this.audioTrackAttach());
    }

    private audioTrackAttach() {
        if (this.audioQL.length > 0) {
            const item = this.audioQL.last;
            if (this.currentAudioElement) {
                console.log('Detach audio item!!!');
                this.stream?.audioTrack.value.detach(this.currentAudioElement);
                delete this.currentAudioElement;
            }

            setTimeout(() => {
                this.currentAudioElement = item.nativeElement;
                this.stream?.audioTrack.value.attach(item.nativeElement);
            }, 1000);
        }
    }

    private audioTrackDetach() {
        this.audioQL.map(item => {
            console.log('---- detach audio');
            this.stream?.audioTrack.value?.detach(item.nativeElement);
        });

        if (this.audioSubscription) this.audioSubscription.unsubscribe();
    }
}
