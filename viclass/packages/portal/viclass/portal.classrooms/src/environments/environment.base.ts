import { MFEConfRequest } from '@viclass/config.server';
import editorConfSpecs from './module.conf.spec';

export type EnvironmentType = {
    production: boolean;
    confEnv: string; // the environment key to use with configuration service
    idleTimeout: number;
    awayTimeOut: number;
    reportUserStatusInterval: number;
    authflowConfig: {
        defaultReturnUrl: string;
    };
    apiUrl: string;
    confSpecs: MFEConfRequest;
    callConfig: {
        hosts: {
            domain: string; // this is the default from jitsi, needs to find way for proper configuration
            muc: string;
        };
        serviceUrl: string;
    };
    enableCaptcha: boolean;
    domain: string;
};

export function envBase(domain: string): EnvironmentType {
    const base = {
        production: false,
        confEnv: 'dev', // the environment key to use with configuration service
        idleTimeout: 10000,
        awayTimeOut: 10000,
        reportUserStatusInterval: 10000,
        authflowConfig: {
            defaultReturnUrl: '/',
        },
        apiUrl: '/api/jsRoutes',
        confSpecs: editorConfSpecs,
        callConfig: {
            hosts: {
                domain: 'meet.jitsi', // this is the default from jitsi, needs to find way for proper configuration
                muc: 'muc.meet.jitsi',
            },
            serviceUrl: 'wss://meet.viclass.vn:8443/xmpp-websocket',
        },
        enableCaptcha: true,
        domain: domain,
    };

    return base;
}
