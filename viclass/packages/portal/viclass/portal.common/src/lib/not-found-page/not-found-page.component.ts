import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';

@Component({
    selector: 'viclass-portal-not-found-page',
    templateUrl: './not-found-page.component.html',
    styleUrls: ['./not-found-page.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        MatButtonModule, // Needed for mat-button
    ],
})
export class NotFoundPageComponent {
    constructor() {}

    retry(): void {
        window.location.reload();
    }
}
