import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import Cropper from 'cropperjs';
import { BehaviorSubject } from 'rxjs';

export type ImageCropperResult = {
    imageData: Cropper.ImageData;
    cropData: Cropper.CropBoxData;
    blob?: Blob;
};

export type ImageCropperOptions = Cropper.Options<HTMLImageElement> & {
    roundedPreview?: boolean;
    resultType?: 'image/jpeg' | 'image/png';
    resultQuality?: number;
    maxContainerHeight?: number;
};

export type ImageResizeOptions = {
    width: number;
    height: number;
};

@Component({
    selector: 'lib-cropper',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './cropper.component.html',
    styleUrls: ['./cropper.min.css', './cropper.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None,
})
export class CropperComponent implements OnDestroy {
    @ViewChild('image', { static: true }) image: ElementRef;

    @Input() cropperOptions: ImageCropperOptions = {};

    @Input() imageUrl: string;
    @Input() loadImageErrorText: string;

    @Output() ready = new EventEmitter<void>();
    @Output() loadError = new EventEmitter<ErrorEvent>();

    readonly isLoading$ = new BehaviorSubject<boolean>(true);
    readonly loadError$ = new BehaviorSubject<boolean>(false);

    private _internalCropper: Cropper;

    get cropper() {
        return this._internalCropper;
    }

    constructor() {}

    ngOnDestroy() {
        if (this._internalCropper) {
            this._internalCropper.destroy();
            this._internalCropper = null;
        }
    }

    /**
     * Image loaded, triggered by (load) event of img element
     */
    imageLoaded(ev: Event) {
        // Unset load error state
        this.loadError$.next(false);

        // Setup image element
        const image = ev.target as HTMLImageElement;

        // Add crossOrigin?
        if (this.cropperOptions.checkCrossOrigin) image.crossOrigin = 'anonymous';

        image.addEventListener('ready', () => {
            this.isLoading$.next(false);
        });

        // Set cropperjs
        if (this._internalCropper) {
            this._internalCropper.destroy();
            this._internalCropper = null;
        }
        this._internalCropper = new Cropper(image, this.cropperOptions);
    }

    /**
     * Handle image load error
     */
    imageLoadError(event: ErrorEvent) {
        this.loadError$.next(true);
        this.isLoading$.next(false);
        this.loadError.emit(event);
    }

    /**
     *  export canvas to blob
     */
    exportCanvas(resize?: ImageResizeOptions): Promise<ImageCropperResult> {
        if (!this._internalCropper) {
            return Promise.reject('Cropper not initialized');
        }

        // Get and set image, crop and canvas data
        const imageData = this._internalCropper.getImageData();
        const cropData = this._internalCropper.getCropBoxData();
        const canvas = this._internalCropper.getCroppedCanvas();
        const data = { imageData, cropData };

        return new Promise(resolve => {
            this.canvasToBlob(canvas, blob => {
                if (resize) {
                    this.resizeBlob(blob, resize).then(resizedBlob => {
                        resolve({ ...data, blob: resizedBlob });
                    });
                } else {
                    resolve({ ...data, blob });
                }
            });
        });
    }

    private resizeBlob(blob: Blob, resize: ImageResizeOptions): Promise<Blob> {
        return new Promise(resolve => {
            const img = new Image();
            img.src = URL.createObjectURL(blob);
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = resize.width;
                canvas.height = resize.height;
                ctx.drawImage(img, 0, 0, resize.width, resize.height);
                this.canvasToBlob(canvas, resolve);
            };
        });
    }

    private canvasToBlob(canvas: HTMLCanvasElement, callback: BlobCallback) {
        canvas.toBlob(
            callback,
            this.cropperOptions?.resultType || 'image/jpeg',
            this.cropperOptions?.resultQuality || 1
        );
    }
}
