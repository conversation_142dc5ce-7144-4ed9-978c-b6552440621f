import { NgModule } from '@angular/core';
import { MatDialogModule } from '@angular/material/dialog';
import { ErrorAlertComponent } from '../error-alert';
import { CommonErrorHandlerListener } from './common.error-handler.listener';

@NgModule({
    imports: [ErrorAlertComponent, MatDialogModule],
    providers: [CommonErrorHandlerListener, ErrorAlertComponent],
})
export class CommonErrorHandlerModule {}
