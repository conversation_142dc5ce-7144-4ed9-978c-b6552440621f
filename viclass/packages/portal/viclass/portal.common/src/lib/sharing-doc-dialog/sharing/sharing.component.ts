import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Inject,
    Injector,
    Input,
    OnDestroy,
    OnInit,
    Output,
    QueryList,
    ViewChildren,
    ViewContainerRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';
import { BaseCoordinator } from '@viclass/editor.coordinator/common';
import { ViewportManager } from '@viclass/editor.core';
import { BehaviorSubject, Observable, Subscription, map } from 'rxjs';
import { ButtonClickWaitDirective } from '../../button-click-wait-directive/click-wait.directive';
import { ENVIRONMENT } from '../../environment';
import { FormUtilModule } from '../../formutils';
import { NotificationModule, NotificationService } from '../../notification';
import { DocInProfileMetadataService, UserService } from '../../services';
import { SharingFooterSocialComponent } from '../../sharing-dialog/sharing-dialog.component';
import { SpinnerLabelComponent } from '../../spin.label/spinner.label.component';
import { SharingDialogConfig } from '../model';

@Component({
    selector: 'app-sharing',
    standalone: true,
    imports: [
        CommonModule,
        FormUtilModule,
        ReactiveFormsModule,
        NotificationModule,
        RouterModule,
        ButtonClickWaitDirective,
        MatSlideToggleModule,
        FormsModule,
        SharingFooterSocialComponent,
        SpinnerLabelComponent,
    ],
    templateUrl: './sharing.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharingComponent implements OnInit, OnDestroy, AfterViewInit {
    @Input() previewBlob: Blob | null = null;
    @Input() inputData: SharingDialogConfig;
    @Output() redirectSaveDocument = new EventEmitter();

    shareLink: string;

    isShared$: Observable<boolean>;
    isSaved$: Observable<boolean>;
    readonly isPreviewing$ = new BehaviorSubject<boolean>(false);

    readonly previewDocLoaded$ = new BehaviorSubject<boolean>(false);
    readonly processingCopy$ = new BehaviorSubject<boolean>(false);
    readonly processingPdf$ = new BehaviorSubject<boolean>(false);

    @ViewChildren('previewArea', { read: ViewContainerRef })
    previewArea: QueryList<ViewContainerRef>;
    private previewAreaSubscription: Subscription;

    private coord?: BaseCoordinator;
    private viewport?: ViewportManager;

    /**
     * We need the coord and viewport to RENDERED in the page AT THE TIME we create preview image/print PDF.
     * If there is initially no coord and viewport then we need the preview page to be rendered
     */
    protected needPreviewViewport: boolean = false;

    get docGlobalId() {
        return this.inputData.docGlobalId;
    }

    constructor(
        public userService: UserService,
        public fb: UntypedFormBuilder,
        private metadataService: DocInProfileMetadataService,
        private notificationService: NotificationService,
        private injector: Injector,
        @Inject(ENVIRONMENT) private env: any
    ) {}

    ngOnInit() {
        this.shareLink = `https://${this.env.domain}/user/doc/${this.inputData.edType}/${this.docGlobalId}`;

        this.isSaved$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.savedToProfile));
        this.isShared$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.shared));

        if (this.inputData.docDisplay) {
            this.coord = this.inputData.docDisplay.coord;
            this.viewport = this.inputData.docDisplay.viewport;
        }

        this.needPreviewViewport = !this.coord || !this.viewport;
    }

    ngAfterViewInit(): void {
        this.previewArea.changes.subscribe(list => {
            this.previewArea.map(async (vref, index, containers) => {
                this.previewDocLoaded$.next(false);

                await this.createPreviewInArea(vref);
                this.previewDocLoaded$.next(true);
            });
        });
    }

    ngOnDestroy(): void {
        if (this.previewAreaSubscription) this.previewAreaSubscription.unsubscribe();
    }

    async onToggleChange(event: MatSlideToggleChange) {
        const docInfo = await this.metadataService.toggleSharing(this.inputData.edType, this.docGlobalId);
        this.inputData.docInfo.next(docInfo);
    }

    async _copyImageToClipboard(blob) {
        // Kiểm tra quyền ghi clipboard
        const permission = await navigator.permissions.query({ name: 'clipboard-write' as any });
        if (permission.state === 'granted' || permission.state === 'prompt') {
            const item = new ClipboardItem({ 'image/png': blob });
            await navigator.clipboard.write([item]);
        } else {
            throw new Error(
                'Clipboard write permission denied. Please allow clipboard access in your browser settings.'
            );
        }
    }

    async shareImage() {
        if (this.processingCopy$.value) return;

        if (this.needPreviewViewport && !this.isPreviewing$.value) {
            this.isPreviewing$.next(true);
            return;
        }

        this.processingCopy$.next(true);
        let previewBlob = this.previewBlob;

        try {
            if (!previewBlob) {
                switch (this.inputData.captureType) {
                    case 'document':
                        previewBlob = await this.coord.captureDocumentPreview(
                            this.inputData.edType,
                            this.viewport.id,
                            this.inputData.docGlobalId
                        );
                        break;
                    case 'viewport':
                        previewBlob = await this.coord.captureViewportPreview(this.viewport.id);
                        break;
                }
            }

            await this._copyImageToClipboard(previewBlob);
            this.notificationService.showNotification({
                message: 'Sao chép hình ảnh thành công',
                status: 'success',
            });
        } catch (error: any) {
            console.error('Failed to copy image to clipboard:', error);
            const isLoseFocusError = error?.name === 'NotAllowedError' && error?.message?.includes('focus');
            const isPermissionError = error?.message?.includes('permission denied');
            const msg = isLoseFocusError
                ? 'Vui lòng giữ tab đang mở để thực hiện thao tác này.'
                : isPermissionError
                  ? 'Vui lòng cấp quyền truy cập clipboard cho trình duyệt.'
                  : 'Sao chép thất bại, xin vui lòng thử lại';
            this.notificationService.showNotification({
                message: msg,
                status: 'error',
            });
        } finally {
            this.processingCopy$.next(false);
        }
    }

    async printPDF() {
        if (this.needPreviewViewport && !this.isPreviewing$.value) {
            this.isPreviewing$.next(true);
            return;
        }

        if (!this.docGlobalId) await this.coord.printPDF(this.viewport.id);
        else await this.coord.printDocPDF(this.inputData.edType, this.viewport.id, this.docGlobalId);
    }

    async copyToClipboard(text: string) {
        try {
            await navigator.clipboard.writeText(text);
            this.notificationService.showNotification({
                message: 'Sao chép liên kết thành công',
                status: 'success',
            });
        } catch (error) {
            console.error('Async: Could not copy text: ', error);
        }
    }

    /**
     *
     * Create the preview in the preview area through calling back
     */
    private async createPreviewInArea(vref: ViewContainerRef) {
        if (this.inputData.previewCb) {
            const { viewport, coord } = await this.inputData.previewCb(this.inputData, vref, this.injector);
            this.viewport = viewport;
            this.coord = coord;
        }
    }
}
