import { Params } from '@angular/router';
import { enc } from 'crypto-js';
import { PKEY_RURL } from '../auth';

export type ReturnUrlDto = {
    rURL: string;
    actionName: string;
};

/**
 * Decode the base64 encoded string of the return URL
 *
 * @param str the base64 encoded return URL
 * @returns the original return URL
 */
const b64DecodeUnicode = (str: string): string => {
    // decode for Vietnamese
    return decodeURIComponent(
        atob(str)
            .split('')
            .map(function (c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            })
            .join('')
    );
};

/**
 * Decode the base64 encoded string of the return URL from the query parameters
 *
 * @returns the original return URL and action name, or null if invalid or no return URL
 */
export function decodeReturnUrlFromParams(params: Params): ReturnUrlDto | null {
    if (params && params[PKEY_RURL]) {
        try {
            return JSON.parse(b64DecodeUnicode(decodeURIComponent(params[PKEY_RURL])));
        } catch (err) {
            console.warn('Invalid return URL');
            return null;
        }
    }

    return null;
}

/**
 * Encode the data into a base64 encoded string that can be used in the query params
 *
 * @param rInfo the dto for return url and action name
 * @returns the encodeded return url
 */
export function encodeReturnUrl(rInfo: ReturnUrlDto): string {
    return encodeURIComponent(enc.Base64.stringify(enc.Utf8.parse(JSON.stringify(rInfo))));
}
