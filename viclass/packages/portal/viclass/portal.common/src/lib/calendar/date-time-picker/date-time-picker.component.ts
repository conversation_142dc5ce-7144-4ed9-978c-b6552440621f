import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    Component,
    ContentChildren,
    EventEmitter,
    Inject,
    Input,
    OnInit,
    Output,
    QueryList,
    TemplateRef,
    ViewChild,
    ViewChildren,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { DateAdapter, MatDateFormats, MAT_DATE_FORMATS } from '@angular/material/core';
import {
    MatCalendarUserEvent,
    MatDatepicker,
    MatDatepickerContent,
    MatDatepickerModule,
} from '@angular/material/datepicker';
import * as moment from 'moment';

export interface DateTimeSelected {
    day: number;
    month: number;
    year: number;
    hour: number;
    minute: number;
    formattedString: string;
}

@Component({
    selector: 'common-date-time-picker',
    templateUrl: './date-time-picker.component.html',
    styleUrls: ['./date-time-picker.component.sass'],
    imports: [CommonModule, FormsModule, MatMomentDateModule, MatDatepickerModule],
    standalone: true,
})
export class DateTimePickerComponent<D> implements OnInit, AfterViewInit {
    @Input()
    initial: DateTimeSelected;

    hour: number = 12;
    minute: number = 30;
    selectedDate: D;

    selectedString: string;

    @Output()
    dateTimePicked: EventEmitter<DateTimeSelected> = new EventEmitter<DateTimeSelected>();

    @ViewChild('picker')
    matDatePicker: MatDatepicker<D>;

    origHandle: (data: MatCalendarUserEvent<D>) => void;

    constructor(
        private _dateAdapter: DateAdapter<D>,
        @Inject(MAT_DATE_FORMATS) private dateFormat: MatDateFormats
    ) {}

    ngOnInit(): void {
        if (this.initial) {
            this.hour = this.initial.hour;
            this.minute = this.initial.minute;
            this.selectedDate = this._dateAdapter.createDate(this.initial.year, this.initial.month, this.initial.day);
            this.createFormattedStringFromData();
        }
    }

    ngAfterViewInit(): void {
        this.matDatePicker.openedStream.subscribe(x => {
            const content: MatDatepickerContent<any, D> = (this.matDatePicker as any)._componentRef.instance;

            this.origHandle = content._handleUserSelection.bind(content);
            content._handleUserSelection = this.handleUserSelection.bind(this); // hack so that we can obtain the user selection event
        });
    }

    handleUserSelection(data: MatCalendarUserEvent<D>) {
        this.origHandle(data);
        this.selectedDate = data.value;
        this.dateTimePicked.emit(this.combineDateWithTime());
    }

    userSelect(data: any) {}

    ensureRange(el: HTMLInputElement, val: number, oldVal: number) {
        const max = parseInt(el.max);
        const min = parseInt(el.min);
        if (val > max || val < min) val = oldVal;
        else if (val == null || val == undefined) {
            val = 0;
        }

        if (val > 0) el.value = val.toString();

        return val;
    }

    setHour(element: HTMLInputElement, event: any) {
        const h = this.hour;
        this.hour = this.ensureRange(element, event as number, this.hour);

        if (h != this.hour) this.dateTimePicked.emit(this.combineDateWithTime());
    }

    setMinute(element: HTMLInputElement, event: any) {
        const m = this.minute;
        this.minute = this.ensureRange(element, event as number, this.minute);

        if (m != this.minute) this.dateTimePicked.emit(this.combineDateWithTime());
    }

    combineDateWithTime(): DateTimeSelected {
        const day = this._dateAdapter.getDate(this.selectedDate);
        const month = this._dateAdapter.getMonth(this.selectedDate);
        const year = this._dateAdapter.getYear(this.selectedDate);

        this.createFormattedStringFromData();

        const result: DateTimeSelected = {
            day: day,
            month: month,
            year: year,
            hour: this.hour,
            minute: this.minute,
            formattedString: this.selectedString,
        };

        return result;
    }

    createFormattedStringFromData() {
        this.selectedString =
            this._dateAdapter.format(this.selectedDate, this.dateFormat.display.dateInput) +
            ' ' +
            moment().hour(this.hour).minute(this.minute).format('HH : mm');
    }
}
