<div class="relative">
    <input
        matInput
        placeholder="Chọn ngày"
        class="cursor-pointer {{ inputClasses }}"
        [matDatepicker]="picker"
        (focus)="focus($event)"
        (click)="focus($event)"
        (dateChange)="change($event)"
        [attr.disabled]="isDisabled ? 'disabled' : null"
        #input />
    <mat-datepicker #picker class="absolute" [calendarHeaderComponent]="calendarHeader" [dateClass]="dateClass">
        <mat-datepicker-actions>
            <div class="flex gap-[5px] w-[100%] items-center justify-center">
                <button class="vi-btn vi-btn-normal vi-btn-focus" title="Xác nhận" matDatepickerApply>
                    <span class="vcon-general vcon_general_yes"></span>
                </button>
                <button class="vi-btn vi-btn-normal vi-btn-outline bg-BW7 text-BW1" title="Đặt lại" (click)="reset()">
                    <span class="vcon-general vcon_general_reset"></span>
                </button>
            </div>
        </mat-datepicker-actions>
    </mat-datepicker>
    <ng-content></ng-content>
</div>
