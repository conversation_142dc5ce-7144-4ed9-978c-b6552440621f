import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatMomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatCalendarCellClassFunction, MatDateRangePicker, MatDatepickerModule } from '@angular/material/datepicker';
import moment from 'moment';
import { Subscription } from 'rxjs';
import { CalendarHeaderComponent } from '../calendar-header/calendar-header.component';

export type DateRange = {
    startDate?: Date;
    endDate?: Date;
};

@Component({
    selector: 'common-date-range-picker',
    templateUrl: './date-range-picker.component.html',
    styleUrls: ['../custom-date-picker.component.scss'],
    imports: [CommonModule, ReactiveFormsModule, MatMomentDateModule, MatDatepickerModule, CalendarHeaderComponent],
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: MAT_DATE_LOCALE,
            useValue: 'vi',
        },
        {
            provide: DateAdapter,
            useClass: MomentDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
    ],
    encapsulation: ViewEncapsulation.None,
})
export class DateRangePickerComponent implements OnInit, OnDestroy {
    calendarHeader = CalendarHeaderComponent;

    startDate: Date = null;
    endDate: Date = null;

    @Input()
    containerClasses: string = '';

    /**
     *  In trigger button mode, the input field will be hidden by default,
     *  and only show after the data was selected
     */
    @Input()
    useTriggerButton = true;

    @ViewChild('picker', { read: MatDateRangePicker<any> })
    picker: MatDateRangePicker<any>;

    @Output()
    dateRangeChange = new EventEmitter<DateRange>();

    range = new FormGroup({
        start: new FormControl<Date | null>(null),
        end: new FormControl<Date | null>(null),
    });

    formSubscription: Subscription;

    get shouldShowInput() {
        return !this.useTriggerButton || !!this.startDate || !!this.endDate;
    }

    constructor() {}

    ngOnInit(): void {
        this.formSubscription = this.range.valueChanges.subscribe(value => {
            this.startDate = value?.start ? moment(value.start).startOf('day').toDate() : null;
            this.endDate = value?.end ? moment(value.end).endOf('day').toDate() : null;

            this.triggerOnChange();
        });
    }

    ngOnDestroy(): void {
        this.formSubscription?.unsubscribe();
    }

    /**
     *  Trigger the `dateRangeChange` event
     */
    private triggerOnChange() {
        this.dateRangeChange.next({
            startDate: this.startDate || null,
            endDate: this.endDate || null,
        });
    }

    /**
     *  Reset the date range and close the picker
     */
    resetRange() {
        this.range.patchValue({
            start: null,
            end: null,
        });

        this.picker?.close();
    }

    /**
     *  Custom class for the date. Currently use to mark the weekend
     */
    dateClass: MatCalendarCellClassFunction<moment.Moment> = (cellDate, view) => {
        // Only highligh dates inside the month view.
        if (view === 'month') {
            // for locale `vi`: Monday is 0 and Sunday is 6
            const isWeekend = cellDate.weekday() >= 5;

            return isWeekend ? 'weekend' : '';
        }

        return '';
    };
}
