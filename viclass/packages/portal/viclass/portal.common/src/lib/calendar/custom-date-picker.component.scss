.mat-datepicker-content {
    background: rgb(var(--BW1));
    color: rgb(var(--BW7));
    --mat-datepicker-calendar-text-font: Montserrat;
    --mat-datepicker-calendar-date-focus-state-background-color: rgb(var(--P2) / 0.3);

    .mat-calendar {
        .mat-calendar-table {
            // override general color on the header and body
            .mat-calendar-table-header,
            .mat-calendar-body-label {
                color: rgb(var(--BW7));
            }

            .mat-calendar-body-label {
                height: 0;
                padding: 0 !important;
            }

            .mat-calendar-table-header-divider::after {
                background-color: rgb(var(--BW7)) !important;
            }

            // override general color on the cells in preview/selection range
            .mat-calendar-body-in-range::before,
            .mat-calendar-body-in-preview {
                background-color: inherit !important;
            }

            .mat-calendar-body-in-range,
            .mat-calendar-body-in-preview {
                color: rgb(var(--P3)) !important;

                .mat-calendar-body-cell-content {
                    background-color: rgb(var(--P3));
                }
            }

            // override general color on the cells
            .mat-calendar-body-cell-content {
                border: 1px solid rgb(var(--BW5)) !important;
                border-radius: 5px !important;
                background: #fff;
                color: rgb(var(--BW1));
            }

            .mat-calendar-body-cell-preview {
                border-radius: 5px !important;
            }

            .mat-calendar-body-selected {
                background-color: rgb(var(--P2)) !important;
            }

            .mat-calendar-body-cell.weekend {
                .mat-calendar-body-cell-content {
                    color: rgb(var(--SC1)) !important;
                }
            }

            // override other elements cause problems
            .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover
                > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(
                    .mat-calendar-body-comparison-identical
                ) {
                background-color: rgb(var(--P2)) !important;
            }
        }

        button.mat-icon-button {
            color: rgb(var(--BW7)) !important;
        }

        svg.mat-calendar-arrow {
            fill: rgb(var(--BW7));
        }
    }
}

.vi-input {
    .mat-date-range-input {
        padding-left: 5px;
        width: 185px;

        &.minimized {
            width: 0px;
            min-width: auto;
            padding-left: 0px;
        }
    }
}
