import {
    ChangeDetectorRef,
    Directive,
    ElementRef,
    HostB<PERSON>ing,
    HostListener,
    Input,
    On<PERSON><PERSON>roy,
    OnInit,
    Renderer2,
} from '@angular/core';
import { delay, from, Observable, of, Subject, switchMap, take, takeUntil } from 'rxjs';

/**
 * The type of clickWait can be:
 * - number: delay in milliseconds
 * - function: returns an Observable or Promise
 * - Observable<boolean>: emits true/false to control waiting state
 * - boolean: true to enable waiting, false to disable
 */
type SupportedClickWaitTypes = number | (() => Observable<any> | Promise<any>) | Observable<boolean> | boolean;

@Directive({
    standalone: true,
    selector: 'button[clickWait]',
})
export class ButtonClickWaitDirective implements OnInit, OnDestroy {
    @HostBinding('disabled')
    public waiting = false;

    private _clickWait: SupportedClickWaitTypes;
    private clickWaitChange$ = new Subject<SupportedClickWaitTypes>();

    @Input()
    set clickWait(value: SupportedClickWaitTypes) {
        this._clickWait = value;
        this.clickWaitChange$.next(value);
    }

    get clickWait(): SupportedClickWaitTypes {
        return this._clickWait;
    }

    @Input()
    waitWithSpinner?: boolean;

    private destroy$ = new Subject<void>();
    private spinnerElement?: HTMLElement;

    constructor(
        private cdRef: ChangeDetectorRef,
        private elementRef: ElementRef<HTMLElement>,
        private renderer: Renderer2
    ) {}

    ngOnInit() {
        // Set default waitWithSpinner value
        if (this.waitWithSpinner === undefined) {
            const tagName = this.elementRef.nativeElement.tagName.toLowerCase();
            this.waitWithSpinner = tagName === 'button' || tagName === 'input';
        }

        // Handle clickWait changes with switchMap to cancel previous subscriptions
        this.clickWaitChange$
            .pipe(
                switchMap(value => {
                    // Handle boolean case
                    if (typeof value === 'boolean') {
                        this.setWaitingState(value);
                        return of(null); // Return empty observable
                    }
                    // Handle Observable<boolean> case
                    else if (value instanceof Observable) {
                        return value;
                    }
                    // For other cases, return empty observable
                    return of(null);
                }),
                takeUntil(this.destroy$)
            )
            .subscribe(waiting => {
                // Only handle Observable<boolean> emissions here
                if (typeof waiting === 'boolean') {
                    this.setWaitingState(waiting);
                }
            });
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
        this.clickWaitChange$.complete();
        this.removeSpinner();
    }

    @HostListener('click')
    clickEvent() {
        // Skip click handling if clickWait is Observable<boolean> or boolean as already handled in ngOnInit
        if (this.clickWait instanceof Observable || typeof this.clickWait === 'boolean') {
            return;
        }

        this.setWaitingState(true);

        if (typeof this.clickWait === 'number') {
            of(true)
                .pipe(delay(this.clickWait), take(1))
                .subscribe(() => {
                    this.setWaitingState(false);
                });
        } else if (typeof this.clickWait === 'function') {
            from(this.clickWait())
                .pipe(take(1))
                .subscribe({
                    next: () => {
                        this.setWaitingState(false);
                    },
                    complete: () => {
                        this.setWaitingState(false);
                    },
                    error: e => {
                        this.setWaitingState(false);
                    },
                });
        }
    }

    private setWaitingState(waiting: boolean) {
        this.waiting = waiting;

        if (this.waitWithSpinner) {
            if (waiting) {
                this.addSpinner();
            } else {
                this.removeSpinner();
            }
        }

        this.cdRef.markForCheck();
    }

    private addSpinner() {
        if (this.spinnerElement) return;

        const element = this.elementRef.nativeElement;

        // Create spinner element
        this.spinnerElement = this.renderer.createElement('img');
        this.renderer.addClass(this.spinnerElement, 'object-cover');
        this.renderer.setStyle(this.spinnerElement, 'height', '1.15rem');
        this.renderer.setStyle(this.spinnerElement, 'width', '1.15rem');
        this.renderer.setStyle(this.spinnerElement, 'margin-right', '0.25rem');
        this.renderer.setAttribute(this.spinnerElement, 'src', '/assets/img/mini-spinner.svg');

        // Insert spinner at the beginning
        this.renderer.insertBefore(element, this.spinnerElement, element.firstChild);
    }

    private removeSpinner() {
        if (!this.spinnerElement) return;

        const element = this.elementRef.nativeElement;

        // Remove spinner
        this.renderer.removeChild(element, this.spinnerElement);
        this.spinnerElement = undefined;
    }
}
