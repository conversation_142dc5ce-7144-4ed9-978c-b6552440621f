import { Overlay, OverlayConfig, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Injectable } from '@angular/core';
import { NotificationComponent } from './notification.component';

export type NotificationStatus = 'success' | 'info' | 'error' | 'warning';

@Injectable({
    providedIn: 'root',
})
export class NotificationService {
    constructor(private overlay: Overlay) {}

    private currentNotiId: number | undefined;

    private createOverlay() {
        const positionStrategy = this.overlay.position().global().centerHorizontally().top('65px'); // Adjust position as needed

        const overlayConfig = new OverlayConfig({
            hasBackdrop: false,
            positionStrategy,
            scrollStrategy: this.overlay.scrollStrategies.noop(),
        });

        return this.overlay.create(overlayConfig);
    }

    private overlayRef: OverlayRef;

    showNotification({
        message,
        status = 'info',
        duration = 4000,
    }: {
        message: string;
        status?: NotificationStatus;
        duration?: number; // ms
    }) {
        if (!this.overlayRef) {
            this.overlayRef = this.createOverlay();
        } else {
            this.overlayRef.detach();
        }

        const notificationPortal = new ComponentPortal(NotificationComponent);

        // Pass data to the notification component
        const notificationComponentRef = this.overlayRef.attach(notificationPortal);

        notificationComponentRef.instance.setMessage(message);
        notificationComponentRef.instance.setStatus(status);

        const randomNotiId = Math.floor(Math.random() * 1000) + 1;
        notificationComponentRef.instance.setCloseCallback(() => this.closeNotification(randomNotiId));

        this.currentNotiId = randomNotiId;
        if (duration > 0) setTimeout(() => this.closeNotification(randomNotiId), duration);
    }

    closeNotification(notiId?: number) {
        if (this.overlayRef && (notiId === undefined || notiId === this.currentNotiId)) {
            this.currentNotiId = 0;
            this.overlayRef.detach();
            this.overlayRef.dispose();
            this.overlayRef = null;
        }
    }
}
