import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    SupportService,
    UserService,
    CommonModule as viCommon,
    CaptchaSiteKey,
    SpinnerLabelComponent,
} from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom, Observable, take, map } from 'rxjs';
import { ContactModel } from './contact.model';
import { NgHcaptchaComponent, NgHcaptchaModule } from 'ng-hcaptcha';

@Component({
    standalone: true,
    imports: [viCommon, ReactiveFormsModule, CommonModule, NgHcaptchaModule, SpinnerLabelComponent],
    selector: '[app-contact]',
    templateUrl: './contact.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContactComponent implements OnInit {
    @ViewChild('hcaptchaElement') hcaptchaElement: NgHcaptchaComponent;
    form: UntypedFormGroup;
    emailError: ErrorModel;
    messageError: ErrorModel;
    phoneError: ErrorModel;
    sitekey: Observable<string | undefined> = this.us.siteKeyOfCaptcha().pipe(
        take(1),
        map((result: CaptchaSiteKey) => result?.sitekey)
    );
    processSubmitContactForm$ = new BehaviorSubject(false);
    initialValues = {
        name: '',
        email: '',
        phone: '',
        userType: 'Student',
        message: '',
        captchaToken: '',
    };
    alert: BehaviorSubject<{
        type: 'success' | 'error';
        content: string;
    }> = new BehaviorSubject(null);

    constructor(
        private fb: UntypedFormBuilder,
        private us: UserService,
        private cdr: ChangeDetectorRef,
        private sc: SupportService
    ) {}

    async ngOnInit() {}

    submitContact = async (data: FormFlowSubmitEvent) => {
        if (this.processSubmitContactForm$.value) return;
        this.processSubmitContactForm$.next(true);

        try {
            if (!this.form.get('captchaToken')?.value?.length)
                this.alert.next({
                    type: 'error',
                    content: 'Vui lòng hoàn thành captcha',
                });
            else {
                await firstValueFrom(this.sc.homepageFeedback(this.form.value));

                this.alert.next({
                    type: 'success',
                    content: 'Đăng ký thành công!',
                });

                // reset
                this.form.reset(this.initialValues);
                this.emailError = null;
                this.messageError = null;
                this.phoneError = null;
                this.hcaptchaElement.reset();
                this.cdr.detectChanges();
            }
        } catch (e) {
            this.alert.next({
                type: 'error',
                content: 'Có lỗi xảy ra!',
            });
        }

        this.processSubmitContactForm$.next(false);
    };

    buildForm = (data?: ContactModel): FormBuildingResult => {
        data = data || (this.initialValues as ContactModel);

        const result = new FormCreator(this.fb, data)
            .validators({
                __self__: [],
                __fields__: {
                    email: [Validators.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/), Validators.required],
                    message: [Validators.minLength(20), Validators.maxLength(1000), Validators.required],
                    phone: [Validators.pattern(/^(0?)(3[2-9]|5[6|8|9]|7[0|6-9]|8[0-6|8|9]|9[0-4|6-9])[0-9]{7}$/)],
                    captchaToken: [Validators.required],
                },
            })
            .validatorMessages({
                __self__: {},
                __fields__: {
                    email: {
                        required: 'Bạn hãy cung cấp email',
                        pattern: 'Email chưa đúng định dạng',
                    },
                    message: {
                        required: 'Bạn hãy ghi lời nhắn',
                        minlength: 'Độ dài tối thiểu 20 ký tự',
                        maxlength: 'Độ dài tối đa 1000 ký tự',
                    },
                    phone: {
                        pattern: 'Số di động có định dạng chưa đúng',
                    },
                    captchaToken: {
                        required: 'Vui lòng nhập captcha',
                    },
                },
            })
            .build();

        this.form = result.control as UntypedFormGroup;

        return result;
    };

    get message$(): Observable<string> {
        if (!this.form || !this.form.get('message')) return null;
        return this.form.get('message').valueChanges;
    }

    async onVerifyCaptcha(token) {
        this.form.setValue({ ...this.form.value, captchaToken: token });
        this.cdr.detectChanges();
    }
}
