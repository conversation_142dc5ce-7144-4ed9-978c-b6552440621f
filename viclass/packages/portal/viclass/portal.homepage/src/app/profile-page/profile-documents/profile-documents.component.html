<form
    class="vi-form sticky top-[50px] md:top-0 z-[2] p-[20px] bg-white"
    *fflow="let fum; by: buildForm; fflow as f; noNav: true"
    [formGroup]="fum"
    [ferrcoord]="f">
    <div class="flex flex-col sm:flex-row gap-[5px] items-center justify-center">
        <div class="grow max-w-[720px] min-w-[320px] flex flex-col sm:flex-row gap-[5px] sm:gap-0">
            <input
                class="vi-input w-full sm:!rounded-r-none"
                formControlName="textSearch"
                placeholder="Nhập từ khóa tìm kiếm" />
            <div class="flex flex-row justify-between">
                <common-date-range-picker
                    #dateRangePicker
                    (dateRangeChange)="onDateRangeChange($event)"
                    containerClasses="sm:!rounded-l-none sm:!rounded-r-none click-indicator"
                    [useTriggerButton]="!(smallScreen$ | async)"></common-date-range-picker>
                <button [matMenuTriggerFor]="editorOptions" class="vi-btn vi-btn-input sm:!rounded-l-none">
                    <span
                        class="vcon-general vcon_general_filter rounded-full p-[3px]"
                        [ngClass]="{
                            'bg-P2': fromEditorType !== '',
                        }"></span>
                </button>
                <div class="sm:hidden">
                    <button
                        class="vi-btn vi-icon-btn vi-btn-normal vi-btn-focus"
                        [disabled]="creatingNewDoc$ | async"
                        (click)="onCreateDoc()">
                        <span class="vcon-general vcon_page-bar_ad"></span>
                    </button>
                </div>
            </div>
        </div>
        <div class="hidden sm:block">
            <button
                class="vi-btn vi-btn-small vi-btn-focus"
                [disabled]="creatingNewDoc$ | async"
                (click)="onCreateDoc()">
                Tạo mới
            </button>
        </div>
    </div>
    <span class="vi-text-error block mt-3" *ngIf="formError$ | async">! {{ (formError$ | async).msg }}</span>
</form>

<div class="flex flex-row justify-center items-center py-[30px]">
    <div
        *ngFor="let docOption of docOptions; let i = index"
        class="flex flex-col sm:flex-row px-[15px] gap-[10px] justify-center items-center text-center"
        [ngClass]="{
            'border-l-[1px] border-BW4': i !== 0,
        }">
        <button
            class="!h-[50px] p-[10px] text-bw3"
            [ngClass]="{
                'border-b-[2px] border-solid border-black font-bold': docOption.docType === (curDocType$ | async),
            }"
            (click)="onSelectDocOption(docOption.docType)">
            <div>{{ docOption.label }} {{ countByDocType(docOption.docType) }}</div>
        </button>
    </div>
</div>

<div
    *ngIf="(curDocType$ | async) === 'doc-in-profile'"
    class="p-[20px]"
    scroll-near-end
    (nearEnd)="onScrollNearEnd()"
    [threshold]="200">
    <ng-template [ngIf]="savedDocs$ | async" [ngIfElse]="loading" let-savedDocs="ngIf">
        <div
            class="grid xl:grid-cols-5 lg:grid-cols-4 sm:grid-cols-3 xs:grid-cols-2 gap-[30px]"
            *ngIf="(accumulatedDocs$ | async)?.length > 0; else noDocument">
            <ng-container *ngFor="let doc of accumulatedDocs$ | async">
                <profile-document-item [doc]="doc" (delete)="onDeleteDoc($event)"></profile-document-item>
            </ng-container>
        </div>
    </ng-template>
    <div class="text-center italic text-BW4 p-[20px]" *ngIf="loadingMore$ | async">Đang tải thêm tài liệu...</div>
</div>
<div
    *ngIf="(curDocType$ | async) === 'doc-share-with-me'"
    class="p-[20px]"
    scroll-near-end
    (nearEnd)="onScrollNearEndForShareWithMe()"
    [threshold]="200">
    <ng-template [ngIf]="shareWithMeDocs$ | async" [ngIfElse]="loading" let-shareWithMeDocs="ngIf">
        <div
            class="grid xl:grid-cols-5 lg:grid-cols-4 sm:grid-cols-3 xs:grid-cols-2 gap-[30px]"
            *ngIf="(accumulatedDocsForShare$ | async)?.length > 0; else noDocument">
            <ng-container *ngFor="let doc of accumulatedDocsForShare$ | async">
                <profile-document-item [doc]="doc" (delete)="onDeleteDoc($event)"></profile-document-item>
            </ng-container>
        </div>
    </ng-template>
    <div class="text-center italic text-BW4 p-[20px]" *ngIf="loadingMore$ | async">Đang tải thêm tài liệu...</div>
</div>

<ng-template #noDocument>
    <div class="w-full h-ful flex flex-col gap-2 m-auto py-[30px]">
        <div class="text-P2 ml-auto mr-auto flex justify-center">
            <span class="!text-[90px] !leading-[90px] vcon vcon-general vcon_empty-data"></span>
        </div>
        <div class="text-center p-[10px]">Chưa có tài liệu</div>
    </div>
</ng-template>

<ng-template #loading>
    <div class="text-center italic text-BW4 p-[20px]">Đang tải tài liệu...</div>
</ng-template>

<ng-template #confirmDeletePopup>
    <div class="vi-popup-container">
        <div class="pt-[15px] pb-[15px] gap-[10px] flex flex-col">
            <div class="flex justify-center">
                <img class="w-[160px] h-[100px]" ngSrc="/assets/img/recycle-bin.svg" height="100" width="160" />
            </div>
            <div class="vi-popup-message text-center">
                <div>Tài liệu hiện tại sẽ bị xóa</div>
                <div>và không thể khôi phục</div>
            </div>
            <div class="flex flex-row gap-[10px] text-center justify-center">
                <button class="vi-btn vi-btn-small vi-btn-focus" [mat-dialog-close]="true">Xác nhận</button>
                <button class="vi-btn vi-btn-small vi-btn-outline" [mat-dialog-close]="false">Hủy</button>
            </div>
        </div>
    </div>
</ng-template>

<mat-menu #editorOptions>
    <div *ngFor="let edOption of editorTypeOptions" class="px-[10px] py-[5px]">
        <button
            mat-menu-item
            (click)="onEditorTypeChanges(edOption.key)"
            [ngClass]="{
                '!bg-P2': fromEditorType === edOption.key,
            }"
            class="vi-btn vi-btn-normal vi-btn-menu-item">
            <span *ngIf="edOption.icon" [class]="'vcon vcon-general ' + edOption.icon"></span>
            {{ edOption.label }}
        </button>
    </div>
</mat-menu>

<ng-template #createDocPopup>
    <profile-create-document (closeCreateDoc)="onCreateDocPopupClose($event)"></profile-create-document>
</ng-template>
