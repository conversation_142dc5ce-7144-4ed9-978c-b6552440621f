import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, UntypedFormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
    AddressItem,
    AddressService,
    Gender,
    LoginInfoResponse,
    NotificationService,
    Social,
    UserService,
    formatUnixToDDMMYYYY,
    fromUnixToDate,
    fullNameValidator,
    phoneValidator,
} from '@viclass/portal.common';
import {
    BehaviorSubject,
    Observable,
    Subscription,
    catchError,
    debounceTime,
    finalize,
    firstValueFrom,
    of,
    take,
    tap,
} from 'rxjs';
import { ProfileUnlinkSocialComponent } from '../profile-unlink-social/profile-unlink-social.component';
import { StillEditWarningComponent } from '../still-edit-warning/still-edit-warning.component';

type SocialRegistration = { [key in Social]: boolean };

@Component({
    selector: 'profile-info',
    templateUrl: './profile-info.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    styleUrls: ['./profile-info.component.scss'],
})
export class ProfileInfoComponent implements OnInit, OnDestroy {
    formatUnixToDDMMYYYY = formatUnixToDDMMYYYY;
    fromUnixToDate = fromUnixToDate;
    Number = Number;
    Object = Object;
    Gender = Gender;
    Social = Social;

    // Loading
    isSubmitting: BehaviorSubject<boolean>;
    isLoadingSocialRegistration: BehaviorSubject<boolean>;

    // Social registration
    socialRegistration: SocialRegistration;

    // Login info include created date and last login
    readonly loginInfo$ = new BehaviorSubject<LoginInfoResponse>(null);

    // Form
    isEdit: Boolean;
    form: UntypedFormGroup;
    isFormChanged: boolean = false; // Use this state becasue this.form.pristine not work on setting field value manually
    formChangeSub: Subscription;
    debounceFullnameSub: Subscription;

    // Address
    countries: AddressItem[];
    cities: AddressItem[];
    districts: AddressItem[];
    wards: AddressItem[];
    addressLoadingState: boolean[];
    addressLevelNameMapping: string[];
    isFetchAllAddressLevel: boolean[];
    addressPlaceholderMapping: string[];
    get addressLevelListMapping() {
        return [this.countries, this.cities, this.districts, this.wards];
    }

    // Gender
    GenderMapping: { [key in Gender]: string };

    constructor(
        private dialog: MatDialog,
        public cdr: ChangeDetectorRef,
        public userService: UserService,
        public addressService: AddressService,
        private notificationService: NotificationService
    ) {
        this.isSubmitting = new BehaviorSubject<boolean>(false);
        this.isLoadingSocialRegistration = new BehaviorSubject<boolean>(false);

        this.socialRegistration = {
            [Social.GOOGLE]: false,
            [Social.FACEBOOK]: false,
        };

        this.addressLoadingState = [false, false, false, false];
        this.addressLevelNameMapping = ['country', 'city', 'district', 'ward'];
        this.isFetchAllAddressLevel = [false, false, false, false];
        this.addressPlaceholderMapping = ['Quốc gia', 'Tỉnh/Thành phố', 'Quận/Huyện', 'Phường/Xã'];
        this.countries = [];
        this.cities = [];
        this.districts = [];
        this.wards = [];

        this.GenderMapping = {
            [Gender.MALE]: 'Nam',
            [Gender.FEMALE]: 'Nữ',
            [Gender.OTHER]: 'Khác',
        };
    }

    get phoneField() {
        return this.form.get('phone');
    }

    get nameField() {
        return this.form.get('name');
    }

    async ngOnInit() {
        this.resetForm();
        this.fetchSocialRegistrations();
        this.fetchLoginInformation();
    }

    /**
     * Fetch the list of linked social registrations
     */
    async fetchSocialRegistrations() {
        this.isLoadingSocialRegistration.next(true);
        this.cdr.detectChanges();

        this.socialRegistration = (await firstValueFrom(this.userService.linkedRegistrations())).reduce((acc, item) => {
            if (Object.keys(Social).includes(item)) acc[item as Social] = true;
            return acc;
        }, {} as SocialRegistration);

        this.isLoadingSocialRegistration.next(false);
        this.cdr.detectChanges();
    }

    /**
     * Fetch login information (last login time and created date)
     */
    async fetchLoginInformation() {
        const loginInfo = await firstValueFrom(this.userService.getUserLoginInformation());
        this.loginInfo$.next(loginInfo);
    }

    ngOnDestroy(): void {
        this.formChangeSub?.unsubscribe();
        this.debounceFullnameSub?.unsubscribe();
    }

    /**
     * Setup the form to edit user info
     */
    async setupForm() {
        const _profile = this.userService.curUser$.getValue();

        this.form = new FormGroup({
            phone: new FormControl(_profile.phone, [phoneValidator]),
            name: new FormControl(_profile.name, [fullNameValidator]),
            dob: new FormControl<string>(new Date(_profile.dateOfBirth).toISOString().split('T')[0], []),
            gender: new FormControl<string>(_profile.gender, []),
            address: new FormGroup({
                country: new FormControl(_profile.address.country, []),
                city: new FormControl(_profile.address.city, []),
                district: new FormControl(_profile.address.district, []),
                ward: new FormControl(_profile.address.ward, []),
                street: new FormControl(_profile.address.street, []),
            }),
        });

        this.formChangeSub?.unsubscribe();
        this.formChangeSub = this.form.valueChanges.subscribe(() => (this.isFormChanged = true));
        this.isFormChanged = false;

        // guard again the behavior of Unikey to add a temp dot symbol when typing accented character (ex: 'Phan thi·' )
        this.debounceFullnameSub = this.form
            .get('name')
            .valueChanges.pipe(debounceTime(1000))
            .subscribe(() => this.form?.get('name')?.updateValueAndValidity({ emitEvent: false }));

        let fetchAddress: Promise<unknown> = new Promise(resolve => resolve(null));
        for (let i = 0; i <= 3; i++) {
            const addressLevelList = this.addressLevelListMapping[i];
            const addressLevelName = this.addressLevelNameMapping[i];

            // Reset address level dropdown options
            addressLevelList.splice(0, addressLevelList.length);

            // Add value of form address level to dropdown value by default
            const addressLevelValue = this.form.get('address.' + addressLevelName).value;
            if (addressLevelValue?.length) {
                addressLevelList.push({
                    wdCode: '',
                    label: addressLevelValue,
                });
                fetchAddress = fetchAddress.then(() => this.fetchAddressLevel(i));
            }

            fetchAddress.then();

            this.isFetchAllAddressLevel[i] = false;
        }

        this.cdr.detectChanges();
    }

    /**
     * Reset the user info form
     */
    resetForm() {
        this.form = new FormGroup({});

        this.countries = [];
        this.cities = [];
        this.districts = [];
        this.wards = [];
        this.isFetchAllAddressLevel = [false, false, false, false];
    }

    /**
     * Enable edit mode on the user info form
     */
    enableEdit() {
        this.setupForm();
        this.isEdit = true;
        this.cdr.detectChanges();
    }

    /**
     * Cancel edit on the user info form. Go back to the read-only view
     */
    disableEdit(force = false) {
        if (force || !this.isFormChanged) {
            this.isEdit = false;
            this.resetForm();
            this.cdr.detectChanges();
        } else {
            const dialog = this.dialog.open(StillEditWarningComponent, {
                width: '350px',
                height: '250px',
            });
            dialog
                .afterClosed()
                .pipe(take(1))
                .subscribe((params: any) => {
                    if (!params?.stillEdit) this.disableEdit(true);
                });
        }
    }

    /**
     * Submit the form and update the user info
     */
    onSubmit() {
        if (!this.form.valid) return;

        const values = this.form.value;
        values.dateOfBirth = new Date(values.dob).getTime();
        delete values.dob;

        values.name = values.name?.trim();

        this.isSubmitting.next(true);
        this.cdr.detectChanges();
        this.userService.updateProfile(this.form.value).subscribe(() => {
            this.isSubmitting.next(false);
            this.cdr.detectChanges();
            this.disableEdit(true);

            this.notificationService.showNotification({
                message: 'Cập nhật thông tin thành công',
                status: 'success',
            });
        });
    }

    /**
     * Check if the address is in the loading state
     */
    get isAddressLoading(): boolean {
        return this.addressLoadingState.some(item => item);
    }

    /**
     * Get the address of the user
     */
    get address(): string {
        const pa = this.userService.curUser$.getValue().address;
        const addressMap = [pa?.street, pa?.ward, pa?.district, pa?.city, pa?.country];

        if (!addressMap.join('').trim().length) return '_';

        return addressMap
            .filter(i => i?.length)
            .join(', ')
            .trim();
    }

    /**
     * Reset the child address level when the parent address level is changed
     * i.g. when the country is changed, the city, district, ward, street will be reset
     */
    resetChildAddressLevel(level: number) {
        for (let i = level + 1; i <= 3; i++) {
            const addressLevelName = this.addressLevelNameMapping[i];
            const addressLevelList = this.addressLevelListMapping[i];

            this.form.get('address.' + addressLevelName)?.setValue('');
            if (addressLevelList.length) addressLevelList.length = 0;
            this.isFetchAllAddressLevel[i] = false;
        }
    }

    /**
     * Fetch address of a certain level (country, city, district...) from the wikipedia api.
     *
     * TODO: add responsed data to cache with expire around 1 month for faster response (API)
     *
     * @param level
     * @returns
     */
    fetchAddressLevel(level: number) {
        const emptyPromise = new Promise(() => {});

        if (this.isFetchAllAddressLevel[level] || (level < 0 && level > 3)) return emptyPromise;

        let fetchObservable: Observable<AddressItem[]>;

        if (level > 0) {
            if (!this.isFetchAllAddressLevel[level - 1]) return emptyPromise;

            // Check parent address value
            const parentLevelName = this.addressLevelNameMapping[level - 1];
            const parentValue = this.form.get('address.' + parentLevelName)?.value;
            if (!parentValue?.length) return emptyPromise; // Return if parent address value empty

            // Get parent wdCode
            const parentList = this.addressLevelListMapping[level - 1];
            const parentWdCode = parentList.find(item => item.label == parentValue)?.wdCode;
            if (!parentWdCode) return emptyPromise;

            fetchObservable = this.addressService.getAddressLevelListByParentWdCode(parentWdCode, level as 1 | 2 | 3);
        } else {
            fetchObservable = this.addressService.getCountries();
        }

        // Fetch data
        {
            const levelList = this.addressLevelListMapping[level];

            levelList.splice(0, levelList.length);

            this.addressLoadingState[level] = true;
            this.cdr.detectChanges();

            return new Promise((resolve, reject) => {
                try {
                    fetchObservable.subscribe((result: AddressItem[]) => {
                        // Replace data without lose reference
                        levelList.splice(0, levelList.length);
                        levelList.push(...result);

                        this.isFetchAllAddressLevel[level] = true;

                        resolve(null);
                    });
                } catch (error) {
                    reject(error);
                }
            }).finally(() => {
                this.addressLoadingState[level] = false;
                this.cdr.detectChanges();
            });
        }
    }

    /**
     * Handle address level change. To be called from the UI
     */
    onAddressLevelChange(addressLevelInput: HTMLSelectElement, level: number) {
        this.form.get('address.' + this.addressLevelNameMapping[level]).setValue(addressLevelInput.value);
        this.resetChildAddressLevel(level);
        this.fetchAddressLevel(level + 1);
        this.cdr.detectChanges();
    }

    /**
     * Set value to the form when the date of birth field is changed
     */
    onDateRangeChange(date: any) {
        this.form.get('dob').setValue(date);
    }

    get name(): string {
        const name = this.userService.curUser$.getValue().name.trim();
        return name.length ? name : '_';
    }

    get nameOrUsername(): string {
        const name = this.userService.curUser$.getValue().name.trim();
        const username = this.userService.curUser$.getValue().username.trim();

        return name.length ? name : username;
    }

    get isSocialExists(): boolean {
        return Object.values(this.socialRegistration).some(item => item);
    }

    get canUnlinkSocial(): boolean {
        return Object.values(this.socialRegistration).length > 1;
    }

    /**
     * Unlink a social registration from the current user
     */
    unlinkSocial(social: Social) {
        const dialog = this.dialog.open(ProfileUnlinkSocialComponent, {
            width: '350px',
            height: '250px',
            data: { social },
        });
        dialog.afterClosed().subscribe((result: any) => {
            if (!result?.confirmed) return;

            this.isLoadingSocialRegistration.next(true);
            this.cdr.detectChanges();
            this.userService
                .unlinkSocial(social.toString())
                .pipe(
                    tap((response: any) => {
                        this.disableEdit(true);
                    }),
                    catchError((err: any) => {
                        this.notificationService.showNotification({
                            message: 'Internal server error',
                            status: 'error',
                        });
                        return of(null);
                    }),
                    finalize(() => {
                        this.isLoadingSocialRegistration.next(false);
                        this.cdr.detectChanges();
                    })
                )
                .subscribe();
        });
    }
}
