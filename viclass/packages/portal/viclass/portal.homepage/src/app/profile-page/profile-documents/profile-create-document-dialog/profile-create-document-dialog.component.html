<div
    class="max-h-[90vh] m-[.5rem] flex flex-col relative"
    [ngClass]="{
        'h-[90vh]': (showEditor | async),
    }">
    <button (click)="closeCreateDoc.emit(false)" class="absolute top-0 right-0 p-[5px] w-[50px] text-center">
        <span class="vcon-general vcon_delete align-middle text-black"></span>
    </button>
    <div
        [ngClass]="{
            'flex flex-row justify-center items-center': true,
            'py-[40px]': !(showEditor | async),
            'py-[15px]': (showEditor | async),
        }">
        <div
            *ngFor="let docOption of createDocOptions; let i = index"
            class="flex flex-col sm:flex-row px-[15px] gap-[10px] justify-center items-center text-center"
            [ngClass]="{
                'border-l-[1px] border-BW4': i !== 0,
            }">
            <button
                [ngClass]="{
                    'bg-P2': docOption.editor === (showEditor | async),
                    'vi-btn vi-btn-normal shadow-[0px_5px_20px_0_rgb(var(--BW1)/0.25)] p-[10px]': true,
                    '!h-[50px]': !(showEditor | async),
                    '!h-[40px] !w-[40px] !rounded-xl': (showEditor | async),
                }"
                (click)="onSelectEditor(docOption.editor)">
                <span
                    [ngClass]="{
                        '!text-3xl': !(showEditor | async),
                        '!text-md': (showEditor | async),
                    }"
                    [class]="'vcon vcon-general ' + docOption.icon"></span>
            </button>
            <div>{{ docOption.label }}</div>
        </div>
    </div>

    <div
        class="px-[20px]"
        [ngClass]="{
            hidden: !(showEditor | async),
        }">
        <div class="pb-[10px] font-bold">Tài liệu:</div>
        <form
            class="vi-form"
            *fflow="let fum; by: buildForm; fflow as f; submit: submitCreateDoc; noNav: true"
            [formGroup]="fum"
            [ferrcoord]="f">
            <div class="form-control flex flex-col sm:flex-row gap-[20px] sm:gap-[10px] items-center justify-center">
                <input
                    class="vi-input w-full"
                    formControlName="docName"
                    placeholder="Tài liệu 1"
                    [(ferror)]="docNameError" />
                <div class="flex flex-row justify-center items-center gap-[10px]">
                    <button
                        type="button"
                        class="vi-btn vi-btn-normal vi-btn-focus w-[3.5rem]"
                        [disabled]="!f.canSubmit() || (isSaving$ | async)"
                        [form-flow-submit]="f"
                        [spinner]="isSaving$">
                        Lưu
                    </button>
                    <button
                        type="button"
                        class="vi-btn vi-btn-normal vi-btn-outline"
                        (click)="closeCreateDoc.emit(false)">
                        Hủy
                    </button>
                </div>
            </div>
            <span class="vi-text-error block mt-3" *ngIf="docNameError"> ! {{ docNameError.msg }} </span>
            <span class="vi-text-error block mt-3" *ngIf="formError$ | async"> ! {{ (formError$ | async).msg }} </span>
        </form>
    </div>

    <ng-template [ngIf]="loadingViewport.size > 0">
        <div class="w-[calc(80vw-40px-1rem)] h-[calc(90vh-100px)] flex items-center justify-center">
            <div>
                <img src="assets/img/loading-icon.svg" class="w-[90%] max-w-[200px]" alt="loading" />
                <div class="text-center text-BW7">
                    <span class="text-[16px]">Loading...</span>
                </div>
            </div>
        </div>
    </ng-template>

    <div class="border border-BW4 hidden"></div>
    <div *ngIf="showEditor | async as curEd" class="px-[20px] w-[calc(80vw-40px-1rem)]">
        <div *ngFor="let edType of createdViewports">
            <ng-container *ngTemplateOutlet="viewportComponent; context: { edType, curEd }"></ng-container>
        </div>
    </div>
</div>

<ng-template #viewportComponent let-edType="edType" let-curEd="curEd">
    <section
        class="h-[calc(90vh-200px)] my-[10px] relative overflow-hidden mx-auto hidden"
        [ngClass]="{
            '!block': curEd === edType,
        }"
        *ngIf="!loadingViewport.has(edType)">
        <div class="profilepage-viewport">
            <div
                app-viewport-manager
                class="viewport-container absolute w-full h-full"
                [edType]="edType"
                [vpToolBar]="false"
                (onCmd)="onViewportCmd($event, edType)"
                [coord]="documentService.coord"
                [uiModules]="uiModules(edType)"></div>
        </div>
    </section>
</ng-template>
