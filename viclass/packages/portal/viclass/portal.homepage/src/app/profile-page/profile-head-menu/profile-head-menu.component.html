<div class="lg:h-[80px] lg:px-largePageMargin md:h-[70px] h-[50px] px-4 flex flex-row">
    <div class="self-center w-full flex flex-row">
        <!-- logo -->
        <div class="grow-0 flex align-items-center">
            <a routerLink="/">
                <img src="assets/img/vi-logo.svg" class="lg:h-[35px] md:h-[30px] xs:h-[20px]" />
            </a>
        </div>

        <div class="lg:hidden grow"></div>

        <ng-template [ngIf]="userService.curUser$ | async" let-profile="ngIf">
            <!-- Clicking on this div is switching the menu too-->
            <div
                class="flex grow-0 flex-row justify-center items-center cursor-pointer pr-3"
                (click)="menuSwitch.switch()">
                <div
                    class="w-[24px] h-[24px] avatar-img border-BW7"
                    [ngStyle]="
                        profile.avatarUrl && {
                            'background-image': 'url(' + profile.avatarUrl + ')',
                        }
                    "></div>
                <div class="max-lg:hidden ml-3">
                    Xin chào
                    <strong>{{ profile.name.trim().length ? profile.name : profile.username }}</strong>
                </div>
            </div>
        </ng-template>

        <!--Hamburger menu, hidden from sm onward -->
        <div class="lg:hidden flex items-center hamburger-menu">
            <input
                class="hamburger-btn"
                type="checkbox"
                id="hamburger-btn"
                #menuSwitch
                app-menu-switch
                [show-menu]="profilePageMenu" />
            <label for="hamburger-btn" class="h-[20px] z-[1001] cursor-pointer flex items-center">
                <span class="hamburger-icon"></span>
            </label>
        </div>
    </div>
    <!-- End navigation -->

    <app-menu class="absolute right-0 bottom-0" #profilePageMenu>
        <div class="absolute left-0 bg-white w-screen h-screen overflow-y-scroll overflow-x-hidden z-[1002] md:hidden">
            <ng-template [ngIf]="userService.curUser$ | async" let-profile="ngIf">
                <div>
                    <a
                        class="block"
                        [routerLink]="'../' + ProfilePageRouteKey.INFO"
                        [ngClass]="{
                            'py-[18px] px-[30px] hover:bg-P3': true,
                            'bg-P3': currentTabKey === ProfilePageRouteKey.INFO,
                        }">
                        <div class="hover:text-inherit flex items-center">
                            <i class="vcon-general vcon_name mr-2"></i>
                            <span>Thông tin cá nhân</span>
                        </div>
                    </a>
                    <a
                        class="block"
                        [routerLink]="'../' + ProfilePageRouteKey.SECURITY"
                        [ngClass]="{
                            'py-[18px] px-[30px] hover:bg-P3': true,
                            'bg-P3': currentTabKey === ProfilePageRouteKey.SECURITY,
                        }">
                        <div class="hover:text-inherit flex items-center">
                            <i class="vcon-general vcon_key mr-2"></i>
                            <span>Bảo mật</span>
                        </div>
                    </a>
                    <!-- <a
                        class="block"
                        [routerLink]="'../' + ProfilePageRouteKey.SETTINGS"
                        [ngClass]="{
                            'py-[18px] px-[30px] hover:bg-P3': true,
                            'bg-P3': currentTabKey === ProfilePageRouteKey.SETTINGS,
                        }">
                        <div class="hover:text-inherit flex items-center">
                            <i class="vcon-general vcon_sidebar-setting mr-2"></i>
                            <span>Cài đặt</span>
                        </div>
                    </a> -->
                    <hr class="lg:hidden border-BW4 opacity-100 my-[10px]" />
                    <a
                        class="block"
                        [routerLink]="'../' + ProfilePageRouteKey.CLASSROOMS"
                        [ngClass]="{
                            'py-[18px] px-[30px] hover:bg-P3': true,
                            'bg-P3': currentTabKey === ProfilePageRouteKey.CLASSROOMS,
                        }">
                        <div class="hover:text-inherit flex items-center">
                            <i class="vcon-general vcon_name mr-2"></i>
                            <span>Buổi học</span>
                        </div>
                    </a>
                    <a
                        class="block"
                        [routerLink]="'../' + ProfilePageRouteKey.DOCUMENTS"
                        [ngClass]="{
                            'py-[18px] px-[30px] hover:bg-P3': true,
                            'bg-P3': currentTabKey === ProfilePageRouteKey.DOCUMENTS,
                        }">
                        <div class="hover:text-inherit flex items-center">
                            <i class="vcon-general vcon_key mr-2"></i>
                            <span>Thư viện</span>
                        </div>
                    </a>
                </div>
            </ng-template>
            <hr class="lg:hidden border-BW4 opacity-100 my-[10px]" />
            <div>
                <a class="block" [routerLink]="'/features'" [ngClass]="{ 'py-[18px] px-[30px] hover:bg-P3': true }">
                    <div class="hover:text-inherit flex items-center">
                        <span>Tính năng</span>
                    </div>
                </a>
                <a class="block" [routerLink]="'/hiring'" [ngClass]="{ 'py-[18px] px-[30px] hover:bg-P3': true }">
                    <div class="hover:text-inherit flex items-center">
                        <i class="vcon-general vcon_footer_email mr-2"></i>
                        <span>Ứng tuyển</span>
                    </div>
                </a>
                <a
                    class="block"
                    [routerLink]="'/support'"
                    target="_blank"
                    [ngClass]="{ 'py-[18px] px-[30px] hover:bg-P3': true }">
                    <div class="hover:text-inherit flex items-center">
                        <i class="vcon-general vcon_help mr-2"></i>
                        <span>Trợ giúp</span>
                    </div>
                </a>
                <button type="button" (click)="logout()" class="mt-4 ml-8 vi-btn vi-btn-normal vi-btn-outline">
                    Đăng xuất
                </button>
            </div>
        </div>
    </app-menu>
</div>
