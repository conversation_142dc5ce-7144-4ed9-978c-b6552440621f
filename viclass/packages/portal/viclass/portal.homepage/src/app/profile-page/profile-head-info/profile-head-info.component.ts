import { HttpResponse, HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, ElementRef, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import {
    CropperComponent,
    FileStoreService,
    ImageCropperOptions,
    UploadResponse,
    UserProfile,
    UserService,
} from '@viclass/portal.common';
import { BehaviorSubject, combineLatest, firstValueFrom, map } from 'rxjs';

@Component({
    selector: 'head-info',
    templateUrl: './profile-head-info.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileHeadInfoComponent {
    @ViewChild('uploadAvatarInput') uploadInput: ElementRef;

    @ViewChild('editAvatarDialog', { read: TemplateRef })
    editAvatarDialogRef: TemplateRef<any>;

    @ViewChild('cropper', { read: CropperComponent })
    public cropperElement: CropperComponent;

    readonly cropConfig: ImageCropperOptions = {
        aspectRatio: 1 / 1,
        roundedPreview: true,
        viewMode: 3, // fit the crop container
        dragMode: 'move', // allow to move the image
        autoCropArea: 1, // always auto crop the whole image
        resultType: 'image/jpeg', // save as jpeg to reduce size
    };

    readonly imgUrl$ = new BehaviorSubject<string>(null);

    readonly cropperReady$ = new BehaviorSubject<boolean>(false);
    readonly loading$ = new BehaviorSubject<boolean>(false);

    readonly shouldDisableOkButton$ = combineLatest([this.cropperReady$, this.loading$]).pipe(
        map(([ready, loading]) => !ready || loading)
    );

    private dialogRef: MatDialogRef<any, any> | null = null;

    readonly errorMessage$ = new BehaviorSubject<string>(null);

    constructor(
        public userService: UserService,
        private dialog: MatDialog,
        private sanitizer: DomSanitizer,
        private fileStoreService: FileStoreService
    ) {}

    uploadAvatar() {
        this.uploadInput.nativeElement.click();
    }

    onFileInput($event: Event) {
        const file: File = ($event.target as HTMLInputElement)?.files[0];
        if (!file) return;

        const url = URL.createObjectURL(file);
        this.openEditAvatarDialog(url);
    }

    onLoadCropperError($event: ErrorEvent) {
        console.error('Failed to load cropper', $event);
        this.errorMessage$.next('Định dạng ảnh không hợp lệ');
    }

    private openEditAvatarDialog(url: string) {
        this.cropperReady$.next(false);
        this.errorMessage$.next(null);

        const safeUrl = this.sanitizer.bypassSecurityTrustUrl(url);
        this.imgUrl$.next(safeUrl as string);

        this.dialogRef = this.dialog.open(this.editAvatarDialogRef);

        // cleanup after dialog closed
        this.dialogRef.afterClosed().subscribe(() => {
            this.cropperReady$.next(false);
            this.imgUrl$.next(null);
            URL.revokeObjectURL(url);
        });
    }

    async confirmCrop() {
        this.loading$.next(true);
        this.errorMessage$.next(null);

        try {
            const data = await this.cropperElement.exportCanvas({
                width: 500,
                height: 500,
            });

            const token = await this.getUploadToken();
            const uploadResult = await this.uploadAvatarToServer(data.blob, token);

            await this.updateAvatarUrl(uploadResult.fileUrl);

            this.dialogRef.close();
            await this.reloadUserProfile();
        } catch (error) {
            console.error('Failed to upload avatar', error);
            this.errorMessage$.next('Không thể tải ảnh lên máy chủ');
        } finally {
            this.loading$.next(false);
        }
    }

    private getUploadToken(): Promise<string> {
        return firstValueFrom(
            this.fileStoreService
                .getUploadToken({
                    allowFileTypes: ['.jpg'], // from canvas.toBlob(), so the original file type is not important
                    maxFileSize: 10 * 1024 * 1024, // 10MB
                })
                .pipe(map(res => res.uploadToken))
        );
    }

    private uploadAvatarToServer(blob: Blob, uploadToken: string): Promise<UploadResponse> {
        const formData = new FormData();

        const fileName = `avatar_${this.userService.curUser$.value.id}.jpg`;
        formData.set('file', blob, fileName);
        formData.set('uploadToken', uploadToken);

        return firstValueFrom(this.fileStoreService.uploadFile(formData));
    }

    private updateAvatarUrl(fileUrl: string): Promise<void> {
        return new Promise((resolve, reject) => {
            this.userService.updateAvatar(fileUrl).subscribe({
                next: (res: HttpResponse<any>) => {
                    if (res.status === HttpStatusCode.Ok) resolve();
                    else reject(res);
                },
                error: error => reject(error),
            });
        });
    }

    private reloadUserProfile(): Promise<UserProfile> {
        return firstValueFrom(this.userService.profile());
    }
}
