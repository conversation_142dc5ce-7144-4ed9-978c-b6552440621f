<div class="flex flex-row w-full justify-center pb-[30px]">
    <div [class.vi-arrow-bottom]="tab === 'creator'" class="vi-arrow-pos-center">
        <div
            (click)="switchTab('creator')"
            [class.font-bold]="tab === 'creator'"
            [class.border-b-[2px]]="tab === 'creator'"
            class="cursor-pointer p-[5px] pe-[10px] border-BW1 border-solid">
            <span class="pr-[5px]"> Bạn đã tạo </span><span>{{ (createdSummaries$ | async).length }}</span>
        </div>
    </div>
    <span class="border-s-[0.5px] border-solid border-BW1 h-[25px] items-center"></span>
    <div [class.vi-arrow-bottom]="tab === 'registered'" class="vi-arrow-pos-center">
        <div
            (click)="switchTab('registered')"
            [class.font-bold]="tab === 'registered'"
            [class.border-b-[2px]]="tab === 'registered'"
            class="cursor-pointer p-[5px] ps-[10px] border-BW1 border-solid">
            <span class="pr-[5px]"> Bạn đã tham gia </span>
            <span>{{ (registeredSummaries$ | async).length }}</span>
        </div>
    </div>
</div>
<ng-template [ngIf]="tab === 'creator'">
    <div class="grid xl:grid-cols-4 lg:grid-cols-3 sm:grid-cols-2 xs:grid-cols-2 gap-[30px]">
        <div class="w-full h-ful flex flex-col gap-2 m-auto">
            <div class="text-P2 ml-auto mr-auto flex justify-center">
                <span class="!text-[90px] !leading-[90px] vcon vcon-general vcon_empty-data"></span>
            </div>
            <button class="vi-btn vi-btn-small vi-btn-gradient ml-auto mr-auto" (click)="createNewLsession()">
                Tạo mới
            </button>
        </div>
        <ng-container *ngFor="let model of createdSummaries$ | async">
            <lsession-summary [model]="model"></lsession-summary>
        </ng-container>
    </div>
</ng-template>
<ng-template [ngIf]="tab === 'registered'">
    <div class="grid xl:grid-cols-4 lg:grid-cols-3 sm:grid-cols-2 xs:grid-cols-2 gap-[30px]">
        <ng-container *ngFor="let model of registeredSummaries$ | async">
            <lsession-summary [model]="model"></lsession-summary>
        </ng-container>
    </div>
</ng-template>
