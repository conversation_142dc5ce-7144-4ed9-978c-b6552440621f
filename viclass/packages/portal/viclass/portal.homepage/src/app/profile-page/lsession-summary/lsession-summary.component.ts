import { Component, Input } from '@angular/core';
import { defaultLsessionSummary, LsessionSummaryModel } from './model';
import { NgIf } from '@angular/common';
import * as moment from 'moment';
import { ViCardBodyComponent, ViCardComponent, ViCardFooterComponent, ViCardHeaderComponent } from '../../vi-card';
import { ViStickerComponent } from '../../vi-sticker';
import { MatDialog } from '@angular/material/dialog';
import { ShareClassroomDialogComponent } from '@viclass/portal.common';

@Component({
    selector: 'lsession-summary',
    templateUrl: './lsession-summary.component.html',
    standalone: true,
    imports: [
        NgIf,
        ViCardComponent,
        ViCardHeaderComponent,
        ViStickerComponent,
        ViCardBodyComponent,
        ViCardFooterComponent,
        ShareClassroomDialogComponent,
    ],
})
export class LsessionSummaryComponent {
    @Input()
    model: LsessionSummaryModel = defaultLsessionSummary();

    constructor(private dialog: MatDialog) {}

    get registrationText(): string {
        switch (this.model.regStatus) {
            case 'WAITING_CONFIRMED':
                return 'Chờ xác nhận';
            case 'REGISTERED':
                return 'Đã  đăng ký';
            case 'REJECTED':
                return 'Đã từ chối';
            case 'CANCELLED':
                return 'Đã Hủy';
            default:
                return 'registration status ' + this.model.regStatus;
        }
    }

    get stickerColor(): string {
        switch (this.model.regStatus) {
            case 'WAITING_CONFIRMED':
                return 'rgb(var(--P3))';
            case 'REGISTERED':
                return 'rgb(var(--P2))';
            case 'REJECTED':
                return 'rgb(var(--BW4))';
            case 'CANCELLED':
                return 'rgb(var(--BW4))';
            default:
                return 'rgb(var(--BW4))';
        }
    }

    get registrationTextColor(): string {
        switch (this.model.regStatus) {
            case 'WAITING_CONFIRMED':
                return 'rgb(var(--P1))';
            case 'REGISTERED':
                return 'rgb(var(--BW1))';
            case 'REJECTED':
                return 'rgb(var(--BW1))';
            case 'CANCELLED':
                return 'rgb(var(--BW1))';
            default:
                return 'rgb(var(--BW1))';
        }
    }

    get isOwner(): boolean {
        return this.model.creatorId == this.model.userId;
    }

    get learningTime() {
        const endedAt = this.model.endedAt || new Date().getTime();
        const diff = moment.utc(moment(endedAt).diff(moment(this.model.startedAt)));
        return diff.format('HH:mm:ss');
    }

    onShare() {
        this.dialog.open(ShareClassroomDialogComponent, {
            data: {
                lsId: this.model.id,
            },
        });
    }

    openInNewTab() {
        window.open(`/classrooms/${this.model.id}/online`, '_blank');
    }
}
