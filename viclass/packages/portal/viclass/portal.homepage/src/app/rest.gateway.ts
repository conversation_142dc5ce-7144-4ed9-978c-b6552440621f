import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { MFEConfResponse } from '@viclass/config.server';
import { Observable } from 'rxjs';

const e = environment.confEnv;

@Injectable({
    providedIn: 'root',
})
export class RestGateway {
    constructor(private http: HttpClient) {}

    getEditorLookups(confSpecs): Observable<MFEConfResponse> {
        return this.http.post<MFEConfResponse>(`/conf/editors/${e}`, confSpecs);
    }
}
