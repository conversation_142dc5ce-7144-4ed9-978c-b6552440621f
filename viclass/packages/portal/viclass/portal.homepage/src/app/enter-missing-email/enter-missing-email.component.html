<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/register-animation.svg">
            <img src="assets/img/register-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[370px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Đăng ký</strong></div>
            <ng-template [ngIf]="(registration$ | async) === null" [ngIfElse]="registrationFound">
                <div class="mt-30">
                    <span
                        class="vi-text-error block"
                        *ngIf="getRegistrationError$ | async as getRegError; else registrationLoading"
                        >! {{ getRegError.msg }}.</span
                    >
                    <ng-template #registrationLoading>
                        <span class="text-[14px] block">! Tìm kiếm thông tin tài khoản...</span>
                    </ng-template>
                </div>
            </ng-template>
            <ng-template #registrationFound>
                <div class="text-[14px] mt-[15px] mb-[30px]">Xin vui lòng bổ sung email cho tài khoản mạng xã hội!</div>
                <form
                    class="vi-form"
                    *fflow="let fum; by: buildForm; fflow as f; submit: submitMissingEmail; noNav: true"
                    [formGroup]="fum"
                    [ferrcoord]="f"
                    [(ferror)]="emailRetypeNotMatchError">
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_footer_email prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="email"
                                placeholder="Email của bạn"
                                [(ferror)]="emailError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="emailError">! {{ emailError.msg }}</span>
                    </div>
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_footer_email prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="emailRetype"
                                placeholder="Nhập lại email"
                                [(ferror)]="emailRetypeError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="emailRetypeError">! {{ emailRetypeError.msg }}</span>
                        <span class="vi-text-error block" *ngIf="emailRetypeNotMatchError"
                            >! {{ emailRetypeNotMatchError.msg }}</span
                        >
                    </div>

                    <div class="text-[14px] text-justify">
                        Tôi đã đọc và đồng ý với các
                        <a class="text-P1 text-[14px]" href="/terms-and-conditions" target="_blank">
                            Điều khoản dịch vụ
                        </a>
                        và
                        <a class="text-P1 text-[14px]" href="/terms-and-conditions#privacy-policy" target="_blank">
                            Chính sách bảo mật
                        </a>
                    </div>
                    <span class="vi-text-error block mt-3" *ngIf="formError$ | async as formError"
                        >! {{ formError.msg }}</span
                    >
                    <button
                        class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                        [disabled]="!f.canSubmit() || (addMissingEmail$ | async)"
                        [form-flow-submit]="f"
                        [spinner]="addMissingEmail$">
                        Xác nhận
                    </button>
                </form>
            </ng-template>
        </div>
    </div>
</section>
