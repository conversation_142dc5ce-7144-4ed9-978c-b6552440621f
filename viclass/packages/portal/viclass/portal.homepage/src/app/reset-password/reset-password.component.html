<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/reset-password.svg">
            <img src="assets/img/reset-password.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[400px] w-[290px] mx-auto">
            <div class="text-[36px] mb-[25px] flex flex-col gap-1 items-center">
                <strong>Đ<PERSON>i mật khẩu</strong>
            </div>
            <ng-template [ngIf]="isjwtTokenInvalid$ | async" [ngIfElse]="tokenValid">
                <div class="flex flex-col gap-1 items-center">
                    <div class="flex-[1_0_80px]">
                        <span class="vcon-common vcon_error text-[80px]"></span>
                    </div>
                    <div class="vi-text-error text-red-500 mt-[20px]">
                        Đường dẫn đổi mật khẩu đã hết hạn. Vui lòng thao tác lại các bước để đổi mật khẩu
                    </div>
                </div>
            </ng-template>
            <ng-template #tokenValid>
                <ng-template [ngIf]="isResetPasswordSuccess$ | async" [ngIfElse]="formSubmit">
                    <div class="flex flex-col gap-1 items-center">
                        <div class="flex-[1_0_80px]">
                            <img src="assets/img/tick-resgistration-completion.svg" alt="" />
                        </div>
                        <div>Mật khẩu của bạn đã được cập nhật thành công.</div>
                        <a href="/login" class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus">Đăng nhập</a>
                    </div>
                </ng-template>
                <ng-template #formSubmit>
                    <form
                        class="vi-form"
                        *fflow="let fum; by: buildForm; fflow as f; submit: submitResetPassword; noNav: true"
                        [formGroup]="fum"
                        [ferrcoord]="f"
                        [(ferror)]="passwordRetypeError">
                        <div class="mb-[20px]">
                            <div class="vi-icon-input">
                                <i class="vcon-general vcon_general_password prepend-icon"></i>
                                <input
                                    class="vi-input prepend-icon append-icon"
                                    type="password"
                                    #pwinput
                                    formControlName="password"
                                    placeholder="Mật khẩu"
                                    [(ferror)]="passwordError" />
                                <i
                                    class="vcon-general vcon_general_preview_view append-icon cursor-pointer"
                                    *ngIf="pwinput.type === 'password'"
                                    (click)="pwinput.type = 'text'"></i>
                                <i
                                    class="vcon-general vcon_general_preview_hide append-icon cursor-pointer"
                                    *ngIf="pwinput.type === 'text'"
                                    (click)="pwinput.type = 'password'"></i>
                            </div>
                            <span class="vi-text-error block" *ngIf="passwordError">! {{ passwordError.msg }}</span>
                        </div>
                        <div class="mb-[20px]">
                            <div class="vi-icon-input">
                                <i class="vcon-general vcon_general_password prepend-icon"></i>
                                <input
                                    class="vi-input prepend-icon append-icon"
                                    type="password"
                                    #retypePw
                                    formControlName="passwordRetype"
                                    placeholder="Nhập lại mật khẩu"
                                    [(ferror)]="passwordRetypeError1" />
                                <i
                                    class="vcon-general vcon_general_preview_view append-icon cursor-pointer"
                                    *ngIf="retypePw.type === 'password'"
                                    (click)="retypePw.type = 'text'"></i>
                                <i
                                    class="vcon-general vcon_general_preview_hide append-icon cursor-pointer"
                                    *ngIf="retypePw.type === 'text'"
                                    (click)="retypePw.type = 'password'"></i>
                            </div>
                            <span class="vi-text-error block" *ngIf="passwordRetypeError"
                                >! {{ passwordRetypeError.msg }}</span
                            >
                            <span class="vi-text-error block" *ngIf="passwordRetypeError1"
                                >! {{ passwordRetypeError1.msg }}</span
                            >
                        </div>
                        <span class="vi-text-error block mt-3" *ngIf="resetPasswordError$ | async"
                            >! {{ (resetPasswordError$ | async).msg }}</span
                        >
                        <button
                            class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                            [disabled]="!f.canSubmit() || (loggingIn$ | async)"
                            [form-flow-submit]="f"
                            [spinner]="loggingIn$">
                            Lưu lại mật khẩu
                        </button>
                    </form>
                </ng-template>
            </ng-template>
        </div>
    </div>
</section>
