<div class="bg-white flex flex-col !min-w-[330px] max-h-[inherit]">
    <div class="flex w-full justify-between bg-BW1 px-[10px] py-[5px]">
        <div class="flex"></div>
        <button (click)="closePopup()" class="p-[5px] w-[50px] text-center">
            <span class="vcon-general vcon_delete align-middle text-BW7"></span>
        </button>
    </div>
    <div class="input-body w-full h-full p-[10px] flex gap-[0.5rem] overflow-hidden pb-[10px] bg-white">
        <div>
            <span class="font-bold size-[24px] w-full">KIỂU ĐƯỜNG VIỀN BẢNG</span>
        </div>
    </div>
    <div class="p-3 flex flex-col gap-4">
        <lib-setting-tool-adjust-number
            label="Độ rộng"
            [value]="{ value: data.width }"
            field="width"
            [min]="data.type === 'double' ? 3 : 1"
            [max]="100"
            (onChange)="onFieldChange($event)"></lib-setting-tool-adjust-number>

        <div>
            <span class="block">Kiểu hiển thị</span>
            <div class="flex gap-6">
                <div *ngFor="let item of borderTypes" class="flex gap-1">
                    <input
                        type="radio"
                        name="stroke-style"
                        [id]="'word-border-style-' + item"
                        [value]="item"
                        [(ngModel)]="data.type"
                        (input)="
                            onFieldChange({
                                value: item,
                                field: 'type',
                            })
                        " />
                    <label
                        [for]="'word-border-style-' + item"
                        class="!h-[24px] flex-1 cursor-pointer flex items-center">
                        <div
                            *ngIf="item !== 'hidden'; else hiddenBorder"
                            class="!h-[1px] w-[15px]"
                            [ngStyle]="{ 'border-top': '3px ' + item + ' black' }"></div>
                        <ng-template #hiddenBorder>
                            <span>Ẩn</span>
                        </ng-template>
                    </label>
                </div>
            </div>
            <div *ngIf="data.type === 'double' && data.width < 3" class="text-red-500 text-sm text-center mt-2">
                * Đường viền kép cần độ rộng tối thiểu là 3
            </div>
        </div>
        <lib-setting-tool-colors
            label="Màu"
            [colorList]="colorList"
            field="color"
            [value]="{ value: data.color }"
            [maxWidth]="330"
            (onChange)="onFieldChange($event)"></lib-setting-tool-colors>
        <div class="flex justify-center items-center gap-4">
            <button type="button" (pointerdown)="confirm()" class="vi-btn vi-btn-normal vi-btn-focus">Xác nhận</button>
            <button type="button" (pointerdown)="closePopup()" class="vi-btn vi-btn-normal vi-btn-outline">Hủy</button>
        </div>
    </div>
</div>
