import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BorderStyleData, DEFAULT_BORDER_STYLE } from '@viclass/editor.word';
import { SettingFieldChangeEmitterData } from '../setting-tool/setting-tool.models';
import { TableBorderDialogConfig } from '../wordtools.models';

/**
 * Component to edit the border style of current table selection
 */
@Component({
    selector: 'lib-table-border-dialog',
    templateUrl: './table-border-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableBorderDialogComponent {
    data: BorderStyleData;

    readonly borderTypes = [
        'solid', // solid border
        'dotted', // dotted border
        'dashed', // dashed border
        'double', // double border
        'hidden', // The same as "none", except in border conflict resolution for table elements
    ] as const;

    readonly colorList = ['#000000', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    constructor(
        private dialogRef: MatDialogRef<TableBorderDialogComponent>,
        private cdRef: ChangeDetectorRef,
        @Inject(MAT_DIALOG_DATA) protected inputData: TableBorderDialogConfig
    ) {
        this.data = { ...(this.inputData.borderStyle || DEFAULT_BORDER_STYLE) };
    }

    /**
     * Close the dialog
     */
    closePopup() {
        this.dialogRef.close();
    }

    /**
     * To be called from the UI to update data on input value change
     */
    onFieldChange(change: SettingFieldChangeEmitterData) {
        this.data[change.field] = change.value;

        if (change.field === 'type' && change.value === 'double' && this.data.width < 3) {
            this.data.width = 3;
            this.cdRef.markForCheck();
        }
    }

    /**
     * To be called from the UI to change the border style with the current data
     */
    confirm() {
        const styleStr = `${this.data.width}px ${this.data.type} ${this.data.color}`;
        this.inputData.tableTool.changeCellBorderStyle(styleStr);
        this.closePopup();
    }
}
