<div class="v-toolbar">
    <div
        class="v-tool-group flex !flex-col !w-auto bg-BW7 overflow-hidden p-[10px]"
        *ngIf="activeCell$ | async as activeCell">
        <div class="flex-1 flex flex-col gap-[2px]" *ngIf="showCells$ | async as showCell">
            <div
                *ngFor="let row of grid"
                [ngClass]="{
                    hidden: row[0].row > showCell.row,
                }"
                class="flex flex-row gap-[2px]">
                <div
                    *ngFor="let cell of row"
                    [ngClass]="{
                        '!bg-P1': cell.row <= activeCell.row && cell.col <= activeCell.col,
                        hidden: cell.row > showCell.row || cell.col > showCell.col,
                    }"
                    (mouseover)="activeCell$.next(cell)"
                    (click)="
                        selected.emit({
                            row: cell.row + 1,
                            col: cell.col + 1,
                        })
                    "
                    class="w-[15px] h-[15px] bg-BW4"></div>
            </div>
        </div>
        <div class="flex items-center pt-[10px]">
            <span>{{ activeCell.row + 1 }} x {{ activeCell.col + 1 }}</span>
        </div>
    </div>
</div>
