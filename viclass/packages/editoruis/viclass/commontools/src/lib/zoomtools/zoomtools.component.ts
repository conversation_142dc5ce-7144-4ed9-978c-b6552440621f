import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
    BaseBoardViewportManager,
    CommonTool,
    CommonToolType,
    DefaultToolBar,
    ToolEventData,
    ToolEventListener,
    ZoomMode,
    ZoomTool,
    ZoomToolState,
} from '@viclass/editor.core';
import { EditorUIComponent, EditorUILoaderComponent } from '@viclass/editorui.loader';
import { ZOOM_TOOLS_SETTINGS_TOKEN, ZoomToolsSettings } from '../commontools.model';
import { BehaviorSubject, map, of } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-zoomtools',
    templateUrl: './zoomtools.component.html',
})
export class ZoomtoolsComponent implements OnInit, OnDestroy, EditorUIComponent {
    private toolListener = ZoomtoolsComponent.ToolListener(this);
    commonToolbar$: BehaviorSubject<DefaultToolBar<CommonToolType, CommonTool>> = new BehaviorSubject(null);
    zoomState: BehaviorSubject<ZoomToolState>;
    show = true;

    currentZoomLevel$ = this.commonToolbar$.pipe(
        switchMap(toolbar => toolbar?.toolState<BehaviorSubject<ZoomToolState>>('zoom') ?? of(null)),
        map((state: ZoomToolState) => (state ? Math.round((1 / state.zoomLevel) * 100) : 100))
    );

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(ZOOM_TOOLS_SETTINGS_TOKEN) settings: ZoomToolsSettings
    ) {}

    ngOnInit(): void {}

    ngOnDestroy(): void {
        if (this.commonToolbar$.value) this.commonToolbar$.value.unregisterToolListener(this.toolListener);
    }

    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        this.commonToolbar$.next(toolbar);
        this.zoomState = this.commonToolbar$.value.toolState<BehaviorSubject<ZoomToolState>>('zoom');

        this.commonToolbar$.value.registerToolListener(this.toolListener);
    }

    disableUI() {}

    hideUI() {
        this.show = false;
    }

    showUI() {
        this.show = true;
    }

    isShowing(): boolean {
        return this.show;
    }

    loadedBy(uiLoader: EditorUILoaderComponent) {
        // this.uiLoader = uiLoader
    }

    hasTool(toolType: CommonToolType) {
        return this.commonToolbar$.value.getTool(toolType);
    }

    hasSomeTool() {
        const tool = this.commonToolbar$.value;
        return !tool.empty && !tool.isDisabled();
    }

    selectZoomMode(mode: ZoomMode) {
        const zoomState = this.commonToolbar$.value.toolState<BehaviorSubject<ZoomToolState>>('zoom');

        if (!this.commonToolbar$.value.isToolActive('zoom')) {
            zoomState.value.zoomMode = mode;
            this.commonToolbar$.value.focus('zoom');
            return;
        }

        if (zoomState.value.zoomMode == mode) {
            zoomState.value.zoomMode = undefined;
            this.commonToolbar$.value.blur('zoom');
        } else {
            zoomState.value.zoomMode = mode;
            this.commonToolbar$.value.update('zoom', zoomState);
        }
    }

    zoomIn() {
        const zoomTool = this.commonToolbar$.value.getTool('zoom') as ZoomTool;
        zoomTool.zoomIn(this.commonToolbar$.value.viewport as BaseBoardViewportManager);
    }

    zoomOut() {
        const zoomTool = this.commonToolbar$.value.getTool('zoom') as ZoomTool;
        zoomTool.zoomOut(this.commonToolbar$.value.viewport as BaseBoardViewportManager);
    }

    private static ToolListener(
        _p: ZoomtoolsComponent
    ): ToolEventListener<DefaultToolBar<CommonToolType, CommonTool>, CommonToolType> {
        return new (class implements ToolEventListener<DefaultToolBar<CommonToolType, CommonTool>, CommonToolType> {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if ((eventData.toolType as CommonToolType) == 'zoom') {
                    // check changes for all events
                    _p.changeDetectorRef.detectChanges();
                }

                return eventData;
            }
        })();
    }
}
