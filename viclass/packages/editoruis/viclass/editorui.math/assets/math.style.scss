@import 'style';

@tailwind components;

@layer components {
    @import 'output';
}

.vcon-mathtype.vcon-color-list {
    .path2 {
        &:before {
            color: var(--color--) !important;
        }
    }
}
.math-shadow-1 {
    box-shadow: 0px 5px 20px 0px rgba(0, 66, 75, 0.2) !important;
}
.math-shadow-2 {
    box-shadow: 0px 0px 15px 0px rgba(219, 0, 255, 0.5) !important;
}
.math-shadow-3 {
    box-shadow: 1px 1px 4px 0px rgba(0, 174, 239, 0.35) !important;
}
.math-border-1 {
    border-width: 2px !important;
    border-style: solid !important;
    border-color: rgb(var(--BW1));
}
.math-border-2 {
    border-width: 2px !important;
    border-style: dashed !important;
    border-color: rgb(var(--BW1));
}
.math-border-3 {
    border-width: 7px !important;
    border-style: double !important;
    border-color: rgb(var(--BW1));
}

.math-root > math-field {
    font-size: 20px;
    background-color: transparent !important;

    &::part(container) {
        --_selection-color: inherit !important;
    }

    &::part(virtual-keyboard-toggle) {
        display: none;
    }

    &::part(menu-toggle) {
        display: none;
    }
}

.v-toolbar {
    &.large {
        .v-tool-group .v-tool-btn {
            width: 50px;
            height: 50px;

            .vcon {
                font-size: 35px;
                width: auto;
                height: auto;
            }
        }
    }

    &.long {
        .v-tool-group .v-tool-btn {
            width: 50px;
            height: 30px;

            .vcon {
                font-size: 25px;
                width: auto;
                height: auto;
            }
        }
    }

    &.normal {
        .v-tool-group .v-tool-btn {
            width: 30px;
            height: 30px;

            .vcon {
                font-size: 25px;
                width: auto;
                height: auto;
            }
        }
    }

    .grid-break {
        grid-column: 1 / -1;
    }
}

$lowercase-path: 'm10.471 8.774 0.0013-0.00162c0.2958-0.37467 0.4385-0.83341 0.4385-1.3632v-1.2547h-0.8876l0.0043 0.20421c0.0058 0.27834 0.0102 0.48067 0.0132 0.60689 0.0014 0.06296 0.0026 0.10747 0.0033 0.13306 3e-4 0.00994 6e-4 0.02027 1e-3 0.02735-0.0012 0.48363-0.05809 0.81974-0.15333 1.0286-0.04701 0.10306-0.09932 0.16608-0.15028 0.20332-0.04876 0.03564-0.10665 0.05541-0.18192 0.05541-0.13442 0-0.25163-0.06049-0.35749-0.23078l-2.5e-4 -4.1e-4c-0.09664-0.15462-0.14538-0.32567-0.14538-0.51803v-7.1258h-0.2-0.54632l-0.05133 0.12294-0.55108 1.3199c-0.22968-0.36565-0.52367-0.67399-0.8814-0.92347l-1.3e-4 -9e-5c-0.49478-0.34446-1.0353-0.51926-1.6172-0.51926-1.2045 0-2.235 0.44821-3.0804 1.3365-0.84334 0.88613-1.268 1.9372-1.268 3.1437 0 1.1862 0.3992 2.2187 1.1948 3.0879 0.82151 0.90121 1.8295 1.3573 3.013 1.3573 0.93249 0 1.7783-0.44753 2.5377-1.2972 0.09527 0.32589 0.2402 0.596 0.4423 0.80055 0.28613 0.28959 0.66558 0.42638 1.1157 0.42638 0.5339 0 0.97534-0.20809 1.307-0.61937zm-0.2265-1.6636h-0.1994c0-1e-3 0-0.00227 1e-4 -0.00367l4e-4 -0.00633c2e-4 -0.00166 8e-4 -0.00904 0.0024-0.01838 7e-4 -0.00366 0.0029-0.01588 0.0081-0.03086 0.0031-0.00823 0.0127-0.0288 0.0198-0.04081 0.0139-0.01939 0.063-0.06273 0.1006-0.08106 0.0178-0.00372 0.04-0.00428 0.0633-0.00253 0.0024 0.10292 0.0039 0.16413 0.0047 0.18364zm-6.3201-5.086 2.7e-4 -2.7e-4c0.43156-0.42063 0.89599-0.61998 1.3985-0.61998 0.56834 0 1.065 0.2515 1.4965 0.78671 0.40041 0.49924 0.59742 1.0502 0.59742 1.6588v2.6455c0 0.53695-0.2317 1.028-0.72572 1.4781-0.49719 0.45301-1.0192 0.66855-1.5704 0.66855-0.7131 0-1.2963-0.39175-1.7473-1.2545-0.37111-0.71995-0.55757-1.5084-0.55757-2.3688 0-0.58394 0.08501-1.1183 0.25275-1.6047l2.8e-4 -8.2e-4c0.19005-0.55897 0.47581-1.0202 0.85524-1.3887z';

$uppercase-path: 'm12.804 13.4 0.2095 0.0099v-0.9501l-0.2042 0.0043c-0.2766 0.0058-0.4996 0.0058-0.6703 1e-4 -0.1754-0.0059-0.2819-0.0174-0.3335-0.0298l-0.0033-7e-4c-0.3013-0.067-0.4922-0.2374-0.5926-0.5291l-1e-4 -1e-4 -3.7353-10.819-2.8e-4 -8e-4c-0.04824-0.1378-0.11826-0.25993-0.22014-0.34852-0.10571-0.091922-0.23357-0.13697-0.37293-0.13697s-0.26723 0.045048-0.37294 0.13697c-0.10188 0.088591-0.1719 0.21072-0.22013 0.34852l-1.5e-4 4.2e-4 -3.5947 10.345-2.3e-4 6e-4c-0.11797 0.3435-0.31612 0.5935-0.59362 0.762-0.2811 0.1707-0.65804 0.2664-1.1443 0.272l-0.1977 0.0022v0.1978 0.5361 0.2089l0.20875-0.0091c0.80244-0.0351 1.3817-0.0525 1.7403-0.0525 0.22453 0 0.88856 0.0174 1.9976 0.0526l0.20635 0.0065v-0.9425h-0.2c-0.29373 0-0.54396-0.0612-0.75648-0.1776h3e-5l-0.00393-0.0021c-0.12974-0.0674-0.21521-0.1435-0.26852-0.2235-0.05234-0.0785-0.08067-0.171-0.08067-0.2845 0-0.0674 0.0217-0.1956 0.07836-0.399 0.05518-0.1981 0.13897-0.452 0.25232-0.763l7.5e-4 -0.0019 0.00288-0.0075 9.7e-4 -0.0025 0.0048-0.0125 9.6e-4 -0.0026 9.6e-4 -0.0025 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.0048-0.0125 9.6e-4 -0.0026 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.00576-0.015 9.6e-4 -0.0026 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0026 0.00672-0.0175 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 9.6e-4 -0.0026 0.00576-0.015 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 9.6e-4 -0.0026 0.0048-0.0125 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 9.6e-4 -0.0025 9.6e-4 -0.0026 0.0048-0.0125 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 0.00192-5e-3 9.6e-4 -0.0026 0.00384-0.01 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.00192-5e-3 9.6e-4 -0.0026 0.00384-0.01 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 0.00288-0.0075 9.6e-4 -0.0026 0.00288-0.0075 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.00288-0.0075 9.6e-4 -0.0026 0.00192-5e-3 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.00384-0.01 9.6e-4 -0.0026 0.00192-5e-3 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 0.0048-0.0125 9.6e-4 -0.0026 9.6e-4 -0.0025 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.0048-0.0125 9.6e-4 -0.0026 9.7e-4 -0.0025 0.00672-0.0175 9.7e-4 -0.0025 0.00576-0.015 9.6e-4 -0.0026 9.7e-4 -0.0025 0.00576-0.015 9.7e-4 -0.0025 0.00192-5e-3 9.6e-4 -0.00252 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.6e-4 -0.00251 9.7e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 0.00192-0.00502 9.6e-4 -0.0025 9.7e-4 -0.00251 9.6e-4 -0.0025 7.5e-4 -0.00195h4.0028c0.16567 0.45376 0.33133 0.9051 0.49699 1.354 0.13699 0.3789 0.23868 0.6814 0.30587 0.9087 0.07 0.2368 0.09507 0.3686 0.09507 0.4201 0 0.038-0.01101 0.0669-0.03728 0.096-0.03013 0.0333-0.08679 0.0729-0.18712 0.1094-0.20316 0.074-0.53024 0.1161-0.99819 0.1132l-0.20124-0.0013v0.9543l0.21617-0.0176c0.42622-0.0345 0.96821-0.052 1.6274-0.052 1.3348 0 2.3712 0.0175 3.1106 0.0525zm-8.0864-4.8822 1.6544-4.8202 1.6694 4.8202h-3.3238z';

mat-slide-toggle.alphabet-switch.mat-mdc-slide-toggle {
    --mdc-switch-selected-handle-color: white;
    --mdc-switch-selected-pressed-handle-color: white;
    --mdc-switch-selected-hover-handle-color: white;
    --mdc-switch-selected-focus-handle-color: white;
    --mdc-switch-selected-pressed-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-hover-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-focus-state-layer-color: rgb(var(--P1));
    --mdc-switch-selected-track-color: rgb(var(--P1));
    --mdc-switch-selected-pressed-track-color: rgb(var(--P1));
    --mdc-switch-selected-hover-track-color: rgb(var(--P1));
    --mdc-switch-selected-focus-track-color: rgb(var(--P1));
    --mdc-switch-selected-icon-color: rgb(var(--P1));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--selected .mdc-switch__handle {
        outline: 2px solid rgb(var(--P1));
    }

    --mdc-switch-unselected-handle-color: white;
    --mdc-switch-unselected-pressed-handle-color: white;
    --mdc-switch-unselected-hover-handle-color: white;
    --mdc-switch-unselected-focus-handle-color: white;
    --mdc-switch-unselected-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-pressed-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-hover-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-focus-track-color: rgb(var(--BW2));
    --mdc-switch-unselected-icon-color: rgb(var(--BW2));
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--unselected .mdc-switch__handle {
        outline: 2px solid rgb(var(--BW2));
    }

    --mdc-switch-disabled-selected-handle-color: white;
    --mdc-switch-disabled-selected-pressed-handle-color: white;
    --mdc-switch-disabled-selected-hover-handle-color: white;
    --mdc-switch-disabled-selected-focus-handle-color: white;
    --mdc-switch-disabled-selected-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-pressed-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-hover-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-focus-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-selected-icon-color: rgb(var(--BW4));

    --mdc-switch-disabled-unselected-handle-color: white;
    --mdc-switch-disabled-unselected-pressed-handle-color: white;
    --mdc-switch-disabled-unselected-hover-handle-color: white;
    --mdc-switch-disabled-unselected-focus-handle-color: white;
    --mdc-switch-disabled-unselected-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-pressed-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-hover-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-focus-track-color: rgb(var(--BW4));
    --mdc-switch-disabled-unselected-icon-color: rgb(var(--BW4));

    ::ng-deep .mdc-switch {
        --mdc-switch-track-width: 70px;
        --mdc-switch-disabled-handle-opacity: 1;
        --mdc-switch-disabled-selected-icon-opacity: 1;
        --mdc-switch-disabled-track-opacity: 1;
        --mdc-switch-disabled-unselected-icon-opacity: 1;
    }

    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--unselected.mdc-switch--disabled .mdc-switch__handle,
    ::ng-deep .mdc-form-field .mdc-switch.mdc-switch--selected.mdc-switch--disabled .mdc-switch__handle {
        outline: 2px solid rgb(var(--BW4));
    }

    .mdc-switch {
        --mdc-switch-track-width: 60px;

        .mdc-switch__icon {
            // Toggle off
            &--off > path {
                transform: translate(6px, 7px);
                d: path($lowercase-path);
            }

            // Toggle on
            &--on > path {
                transform: translate(5px, 5px);
                d: path($uppercase-path);
            }
        }
    }
}
