<div class="v-toolbar">
    <div class="v-tool-group !w-[280px] py-2 px-3 relative">
        <div class="flex justify-between items-center border-b-P1 !border-b-[1px] mb-2 w-full">
            <div class="text-P1 flex justify-center items-center gap-1">
                <span class="vcon vcon-mathtype vcon-latex"></span>
                LATEX
            </div>
            <span class="vcon-general vcon_delete !p-1 cursor-pointer" (click)="close.emit()"></span>
        </div>
        <div class="w-full" (keydown)="$event.key === 'Escape' ? close.emit() : $event.stopPropagation()">
            <textarea
                class="border-[1px] outline-P1 !border-BW4 rounded-[12px] w-full overflow-hidden !p-1"
                [value]="latex"
                (input)="handleLatexChange($event)"
                [disabled]="disabled"
                (focus)="focused = true"
                (blur)="focused = false"
                rows="4"></textarea>
            <div *ngIf="syntaxErrors.length > 0">
                <div class="text-red-500 text-xs">
                    {{ getSyntaxErrorMessage(syntaxErrors[0]) }}
                </div>
            </div>
        </div>
    </div>
</div>
