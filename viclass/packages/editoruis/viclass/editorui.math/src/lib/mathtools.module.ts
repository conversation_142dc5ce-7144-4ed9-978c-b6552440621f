import { DragDropModule } from '@angular/cdk/drag-drop';
import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EditorUILoaderComponent, EditorUILoaderModule } from '@viclass/editorui.loader';
import { MathtoolsComponent } from './mathtools.component';
import { MatrixSizeSelectorComponent } from './matrix-size-selector/matrix-size-selector.component';
import { ToolbarButtonComponent } from './toolbar-button/toolbar-button.component';
import { TooltipComponent } from './tooltip/tooltip.component';
import { LatexEditorComponent } from './latex-editor/latex-editor.component';

@NgModule({
    declarations: [MathtoolsComponent, ToolbarButtonComponent],
    imports: [
        CommonModule,
        OverlayModule,
        MatAutocompleteModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        FormsModule,
        DragDropModule,
        EditorUILoaderModule,
        TooltipComponent,
        MatrixSizeSelectorComponent,
        LatexEditorComponent,
    ],
    providers: [
        {
            provide: OverlayContainer,
            useFactory: (comp: EditorUILoaderComponent) => {
                return comp.overlayContainer;
            },
            deps: [EditorUILoaderComponent],
        },
        { provide: Overlay, useClass: Overlay },
    ],
})
export class MathToolsModule {}
