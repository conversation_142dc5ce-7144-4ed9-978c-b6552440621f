import { CommonModule } from '@angular/common';
import { Component, OnInit, Input, ElementRef, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { Tooltip, TooltipInterface } from 'flowbite';

@Component({
    selector: 'tooltip-math',
    templateUrl: './tooltip.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule],
})
export class TooltipComponent implements OnInit, OnDestroy {
    private _tooltip: TooltipInterface;

    @Input() toolTipFor: HTMLElement;
    @Input() tooltipContent: string;

    constructor(private _elementRef: ElementRef) {}

    ngOnInit(): void {
        const $tooltipEl: HTMLElement = this._elementRef.nativeElement.childNodes[0];
        const $triggerEl: HTMLElement = this.toolTipFor;

        this._tooltip = new Tooltip($tooltipEl as HTMLElement, $triggerEl as HTMLElement);
    }

    ngOnDestroy() {
        this._tooltip.destroy();
    }
}
