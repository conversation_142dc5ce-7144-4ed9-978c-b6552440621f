// Shared styles
.slider-container {
    display: flex;
    align-items: center;
    touch-action: none;

    // Add styling for disabled buttons
    .v-tool-btn {
        &[disabled] {
            opacity: 0.5;
            cursor: not-allowed;

            span {
                color: #a0a0a0;
            }
        }
    }

    &.vertical {
        flex-direction: column;
        justify-content: center;

        .slider-track-container {
            height: var(--slider-height, 150px);
            margin: 10px 0;
            position: relative;
            touch-action: none;
        }

        .slider-track {
            background-color: #e0e0e0;
            height: 100%;
            width: 12px;
            border-radius: 6px;
            position: relative;
            margin: 0 auto;
            overflow: visible;
            cursor: pointer;
            transition: background-color 0.2s;
            touch-action: none;

            &.dragging {
                background-color: #d0d0d0;
            }
        }

        .slider-fill {
            background-color: var(--slider-fill, #4a86e8);
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            border-radius: 6px;
            height: 0; /* The height will be set dynamically via [style.height.%] binding */
        }

        .slider-knob {
            height: 28px;
            width: 28px;
            background-color: white;
            border: 2px solid var(--slider-fill, #4a86e8);
            border-radius: 50%;
            position: absolute;
            left: 50%;
            transform: translateX(-50%) translateY(50%);
            cursor: grab;
            z-index: 1;
            transition:
                transform 0.1s,
                box-shadow 0.2s;
            touch-action: none;
            -webkit-tap-highlight-color: transparent;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;

            &.dragging {
                cursor: grabbing;
                transform: translateX(-50%) translateY(50%) scale(1.1);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
            }
        }
    }

    &.horizontal {
        flex-direction: row;
        gap: 8px;
        justify-content: center;

        .width-value {
            font-size: 14px;
            min-width: 20px;
            text-align: center;
        }
    }
}

.knob-value {
    position: static;
    font-size: 12px;
    color: #4a86e8;
    font-weight: bold;
    opacity: 1;
    user-select: none;
}

/* Optimize for touch screens */
@media (pointer: coarse) {
    .slider-container.vertical {
        .slider-knob {
            height: 32px;
            width: 32px;
        }

        .slider-track {
            width: 16px;
            border-radius: 8px;
        }
    }

    .knob-value {
        font-size: 14px;
    }
}
