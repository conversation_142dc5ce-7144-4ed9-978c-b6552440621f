import { ComponentRef, Injector, Input, ViewContainerRef } from '@angular/core';
import { DefaultToolBar, EditorType } from '@viclass/editor.core';
import { ModuleLookup } from '@viclass/editor.core';

/**
 * Define the information provided to
 * the loader component when loading the component UI
 */
export interface EditorUILookup {
    editorType: EditorType;
    uiImpl: ModuleLookup;
    style?: ModuleLookup;
    settings?: any; // a settings can be passed in to configure the UI. The EditorUIComponent when creating UI will pass this setting to the factory function
}

export interface IEditorUILoaderComponent {
    lookups: string;

    uiDisplay: string;

    vAlign: 'top' | 'center' | 'bottom';

    hAlign: 'left' | 'center' | 'right';

    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    hideUI(editorType: EditorType);

    switchTo(editorType: EditorType);
}

/**
 * Each individual editor UI will export this component representing the UI
 */
export interface EditorUIComponent {
    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T);
    disableUI();
    hideUI();
    showUI();
    isShowing(): boolean;

    loadedBy(uiLoader: IEditorUILoaderComponent);
}

export interface ComponentSettings {}

/**
 * Each individual editor UI implementation must provide this functionalities so that the UI loader
 * can create the UI Component
 */
export interface ComponentFactory {
    (viewContainerRef: ViewContainerRef, injector: Injector, settings?: any): Promise<ComponentRef<EditorUIComponent>>;
}

/**
 * Provide configuration for the loader
 */
export interface LoaderConfiguration {
    uiLookUps: EditorUILookup[];
}
