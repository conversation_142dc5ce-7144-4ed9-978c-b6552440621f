@import '@angular/material/prebuilt-themes/indigo-pink';

// ----- Color ----
// -- Primary --
$D5: #878787;
$dark: #212121;
$P1: #00aeef;
$P2: #8de4ff;
$P3: #d8f8ff;
$G0: #fff;
$G1: #a4adb4;
$G2: #363a3e;
$G3: #121414;
// -- Secondary --
$SC1: #db00ff;
$SC2: #31e37c;
$SC3: #ffd600;
$SC4: #ff7a00;
$SC5: #ff002e;
// -- Pastel --
$PAS1: #fff6ca;
$PAS7: #ffdbe1;
// -- Grayscale --
$BW1: #121414;
$BW2: #363a3e;
$BW3: #a4adb4;
$BW4: #ffffff;
$BW5: #62676b;

// -- Gradient --
$GR1: linear-gradient(264deg, #ee85ff 3.99%, #b0eaff 57.75%);
$GR2: linear-gradient(264deg, #b0eaff 15.07%, #f2a0ff 100%);

// --- SHADOW -----
$SH1: var(--SH1);

// TRANSPARENT
$TP1: var(--TP1);

// --- SPACING -----
$SP1: 5px;
$SP2: 10px;
$SP3: 3px;
$SP4: 15px;

// --- RADIUS ----
$R1: 10px;
$R2: 20px;

// ---- BORDER ----
$BWidth: 1px;

// margin between two toolbars
$tb-margin: 5px;
$tb-group-margin-to-edge: 10px;

// ---- Tool Group
$t-group-background: rgba($G0, 60%);
$t-group-opacity: 1;
$t-group-hover-opacity: 1;
$t-group-glassmorph: blur(5px);
$t-group-size: 40px;
$t-group-font-size: 14px;
$t-group-font-weight: 500;
$t-group-shadow: 0px 5px 20px $TP1;
$t-group-border-color: lighten($G1, 30);

$t-group-border: none;

$t-group-corner-radius: 15px; //calc($t-group-size / 2)

// ---- Icon size ---
$t-icon-size: calc($t-group-size / 2);

// ---- BUTTON ---
$t-btn-size: $t-icon-size + 2 * $SP1;
$t-btn-border-radius: calc($t-btn-size / 2);
$t-btn-background: none;

.pt-5px {
    padding-top: 5px !important;
}

.pt-10px {
    padding-top: 10px !important;
}

.w-100 {
    width: 100% !important;
}

.d-flex {
    display: flex !important;
}

.flex-column {
    flex-direction: column !important;
}

.me-auto {
    margin-right: auto;
}

.ms-auto {
    margin-left: auto;
}

.slider-vertical {
    writing-mode: vertical-lr;
    direction: rtl;
    appearance: slider-vertical;
    vertical-align: bottom;
    width: 34px;
}

.float-ui {
    position: absolute;
}

.v-toolbar-group {
    display: flex;

    .vcon {
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: $t-icon-size;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-size: $t-icon-size;

        &.vcon_more-tools {
            position: absolute;
            font-size: 5px;
            line-height: 5px;
            top: calc(50% - 2.5px);
            right: -8px;
        }
    }

    .v-toolbar,
    .v-tool-group {
        display: flex;
    }

    .v-toolbar-submenu {
        position: absolute;
    }

    &.top {
        top: 0px;

        .v-toolbar {
            top: 0px;
        }
    }

    &.left {
        left: 0px;

        .v-toolbar {
            left: 0px;
        }
    }

    &.right {
        right: 0px;

        .v-toolbar {
            right: 0px;
        }
    }

    &.bottom {
        bottom: 0px;

        .v-toolbar {
            bottom: 0px;
        }

        &.ltr .v-tool-group {
            align-self: flex-end;
        }

        &.rtl .v-tool-group {
            align-self: flex-end;
        }
    }

    editor-ui-holder + * {
        display: flex;
        flex-direction: inherit;
        gap: 10px;
    }

    &.ltr {
        flex-direction: row;

        .v-toolbar-gutter {
            margin-left: $tb-margin;
            margin-right: $tb-margin;
        }

        .v-toolbar {
            flex-direction: row;
            // for animation purpose
            transform: rotateY(-90deg) scaleX(0);
            transform-origin: top left;

            &.with-sub {
                height: calc(2 * $t-group-size + $tb-group-margin-to-edge) !important;
            }

            &.with-2-sub {
                height: calc(3 * $t-group-size + 2 * $tb-group-margin-to-edge) !important;
            }
        }

        .v-tool-group {
            height: $t-group-size;
            flex-direction: row;

            &::before {
                width: 100%;
                height: $t-group-size;
            }
        }

        .v-tool-separation {
            border-left: 1px solid $BW1;
            margin-left: $SP1;
            margin-right: $SP1;
            height: $t-group-size;
        }

        &.top .v-toolbar-submenu {
            top: calc($t-group-size + $tb-group-margin-to-edge);
            left: 0px;
        }

        &.bottom .v-toolbar-submenu {
            bottom: calc($t-group-size + $tb-group-margin-to-edge);
            left: 0px;
        }
    }

    &.rtl {
        flex-direction: row-reverse;

        .v-toolbar-gutter {
            margin-left: $tb-margin;
            margin-right: $tb-margin;
        }

        .v-toolbar {
            flex-direction: row-reverse;
            // for animation purpose
            transform: rotateY(-90deg) scaleX(0);
            transform-origin: top right;

            &.with-sub {
                height: calc(2 * $t-group-size + $tb-group-margin-to-edge) !important;
            }

            &.with-2-sub {
                height: calc(3 * $t-group-size + 2 * $tb-group-margin-to-edge) !important;
            }
        }

        .v-tool-group {
            height: $t-group-size;
            flex-direction: row-reverse;

            &::before {
                width: 100%;
                height: $t-group-size;
            }
        }

        .v-tool-separation {
            border-right: 1px solid $BW1;
            margin-left: $SP1;
            margin-right: $SP1;
            height: $t-group-size;
        }

        &.top .v-toolbar-submenu {
            top: calc($t-group-size + $tb-group-margin-to-edge);
            right: 0px;
        }

        &.bottom .v-toolbar-submenu {
            bottom: calc($t-group-size + $tb-group-margin-to-edge);
            right: 0px;
        }
    }

    &.ttb {
        flex-direction: column;

        .v-toolbar-gutter {
            margin-top: $tb-margin;
            margin-bottom: $tb-margin;
        }

        .v-toolbar {
            flex-direction: column;
            // for animation purpose
            transform: rotateX(-90deg) scaleY(0);
            transform-origin: top left;

            &.with-sub {
                width: calc(2 * $t-group-size + $tb-group-margin-to-edge) !important;
            }

            &.with-2-sub {
                width: calc(3 * $t-group-size + 2 * $tb-group-margin-to-edge) !important;
            }
        }

        .v-tool-group {
            width: $t-group-size;
            flex-direction: column;

            &::before {
                width: 100%;
                height: 100%;
            }
        }

        .v-tool-separation {
            border-top: 1px solid $BW1;
            margin-top: $SP1;
            margin-bottom: $SP1;
            width: $t-group-size;
        }

        &.left .v-toolbar-submenu {
            left: calc($t-group-size + $tb-group-margin-to-edge);
            top: 0px;
        }

        &.right .v-toolbar-submenu {
            right: calc($t-group-size + $tb-group-margin-to-edge);
            top: 0px;
        }
    }

    &.btt {
        flex-direction: column-reverse;

        .v-toolbar-gutter {
            margin-top: $tb-margin;
            margin-bottom: $tb-margin;
        }

        .v-toolbar {
            flex-direction: column-reverse;
            // for animation purpose
            transform: rotateX(90deg) scaleY(0);
            transform-origin: bottom left;

            &.with-sub {
                width: calc(2 * $t-group-size + $tb-group-margin-to-edge) !important;
            }

            &.with-2-sub {
                width: calc(3 * $t-group-size + 2 * $tb-group-margin-to-edge) !important;
            }
        }

        .v-tool-group {
            width: $t-group-size;
            flex-direction: column-reverse;

            &::before {
                width: $t-group-size;
                height: 100%;
            }
        }

        .v-tool-separation {
            border-bottom: 1px solid $G3;
            margin-top: $SP1;
            margin-bottom: $SP1;
            width: $t-group-size;
        }

        &.left .v-toolbar-submenu {
            left: calc($t-group-size + $tb-group-margin-to-edge);
            bottom: 0px;
        }

        &.right .v-toolbar-submenu {
            right: calc($t-group-size + $tb-group-margin-to-edge);
            bottom: 0px;
        }
    }

    .v-toolbar {
        animation: showtoolbar 100ms 50ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;
    }

    @keyframes showtoolbar {
        100% {
            transform: none;
        }
    }

    .v-tool-group {
        border-radius: $t-group-corner-radius;
        box-shadow: $t-group-shadow;
        position: relative;
        pointer-events: all;
        align-items: center;

        &::before {
            content: '';
            background: $t-group-background;
            backdrop-filter: $t-group-glassmorph;
            -webkit-backdrop-filter: $t-group-glassmorph;
            opacity: $t-group-opacity;
            border-radius: $t-group-corner-radius;
            position: absolute;
            z-index: -1;
        }

        &:hover {
            opacity: $t-group-hover-opacity;
        }

        .v-tool-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            background: $t-btn-background;
            color: $BW1;
            padding: 0px;
            border: none;
            width: $t-btn-size;
            height: $t-btn-size;
            border-radius: $t-btn-border-radius;
            margin: calc(($t-group-size - $t-btn-size) * 0.5);

            &:hover {
                cursor: pointer;
                background: $P3;
                color: $BW1;
            }

            &.active {
                background: $P2;
            }

            &:disabled {
                pointer-events: none;
                opacity: 30%;
            }

            svg {
                position: relative !important;
            }

            .vcon {
                width: 20px;
                height: 20px;
            }
        }

        .v-tool-label-btn {
            line-height: $t-btn-size;
            margin: calc(($t-group-size - $t-btn-size) * 0.5) 0px;
            font-family: Montserrat;
            font-size: $t-group-font-size;
            font-weight: $t-group-font-weight;
            border-radius: $t-btn-border-radius;
            padding-left: calc($t-btn-border-radius * 1.2);
            padding-right: calc($t-btn-border-radius * 1.2);

            &:hover {
                cursor: pointer;
                background: $P3;
                color: $BW1;
            }

            &.active {
                background: $P2;
            }

            &:disabled {
                pointer-events: none;
                opacity: 30%;
            }
        }

        .v-tool-label {
            line-height: $t-btn-size;
            margin: calc(($t-group-size - $t-btn-size) * 0.5);
            font-family: Montserrat;
            font-size: $t-group-font-size;
            font-weight: $t-group-font-weight;
            text-align: center;
        }
    }
}

.with-pad {
    .v-toolbar-group {
        padding: $tb-group-margin-to-edge;
    }
}

.no-pad {
    > .v-toolbar-group {
        padding: 0;
    }
}

*::-webkit-scrollbar-track {
    //-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3)
    background-color: $G0;
}

*::-webkit-scrollbar {
    width: 12px;
    background-color: $dark;
}

*::-webkit-scrollbar-thumb {
    background-color: rgb(var(--BW4));
    border: 3px solid $G0;
    border-radius: 20px;
}

* {
    scrollbar-width: thin;
    scrollbar-color: $dark $G0;
}

.editor-ui-group {
    overflow: auto;
    scrollbar-width: none;
    display: flex;
    scroll-behavior: smooth;
}

// SCROLLBAR FOR TOOLBAR - START

.editor-ui-group-vertical {
    flex-direction: column;
}

.v-toolbar-scroll {
    position: sticky;
    z-index: 10;
    pointer-events: all;

    > button {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &.v-toolbar-scroll-vertical {
        left: 0;

        > button {
            height: 20px;
            width: 40px;

            > span {
                transform: rotate(-90deg);
            }
        }

        &.v-toolbar-scroll-start {
            top: 0;

            > button {
                top: 0;
                left: 0;
            }
        }

        &.v-toolbar-scroll-end {
            bottom: 0;

            > button {
                bottom: 0;
                left: 0;
            }
        }
    }

    &.v-toolbar-scroll-horizontal {
        bottom: 0;

        > button {
            height: 40px;
            width: 20px;

            > span {
                transform: rotate(-180deg);
            }
        }

        &.v-toolbar-scroll-start {
            left: 0;

            > button {
                left: 0;
                bottom: 0;
            }
        }

        &.v-toolbar-scroll-end {
            right: 0;

            > button {
                right: 0;
                bottom: 0;
            }
        }
    }
}

.with-pad {
    .v-toolbar-scroll {
        &.v-toolbar-scroll-vertical {
            > button {
                left: $tb-group-margin-to-edge !important;
                // top: $tb-group-margin-to-edge !important;
            }
        }

        &.v-toolbar-scroll-horizontal {
            > button {
                bottom: $tb-group-margin-to-edge !important;
                // right: $tb-group-margin-to-edge !important;
            }
        }
    }
}

// SCROLLBAR FOR TOOLBAR - END

// DOC LAYER - START

.doc-layer-border-none {
    outline: none;
}

.doc-layer-border-no-content {
    outline: 1px dotted #dfdfdf !important;
}

.doc-layer-border-bd1 {
    outline: 4px dashed;
}

.doc-layer-border-bd3 {
    outline: 4px double;
}

.doc-layer-border-bd2 {
    outline: 4px solid;
}

.doc-layer-shadow-none {
    box-shadow: none;
}

.doc-layer-shadow-sd1:not(svg),
svg.doc-layer-shadow-sd1 > foreignObject {
    box-shadow: 0px 5px 20px rgba(0, 66, 75, 0.2);
}

.doc-layer-shadow-sd2:not(svg),
svg.doc-layer-shadow-sd2 > foreignObject {
    box-shadow: 0px 0px 15px 0px rgba(219, 0, 255, 0.5);
}

.doc-layer-shadow-sd3:not(svg),
svg.doc-layer-shadow-sd3 > foreignObject {
    box-shadow: 1px 1px 4px 0px rgba(0, 174, 239, 0.35);
}

// DOC LAYER - END
