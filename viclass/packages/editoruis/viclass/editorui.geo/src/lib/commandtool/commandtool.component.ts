import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
    AfterViewChecked,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    HostListener,
    Inject,
    OnChanges,
    OnDestroy,
    OnInit,
    QueryList,
    SimpleChanges,
    ViewChild,
    ViewChildren,
    ViewEncapsulation,
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ChangeToolEventData, initRandomAwarenessId, ToolEventListener, ViErr } from '@viclass/editor.core';
import {
    ConstraintTemplateData,
    GeometryEditor,
    GeometryToolBar,
    GeometryToolType,
    GeoObjectType,
    GeoToolEventData,
    InputCommandTool,
    InputCommandToolState,
    ParamKind,
    UserConstraintInputData,
    UserParamInput,
} from '@viclass/editor.geo';
import { AxiosError } from 'axios';
import { MathfieldElement } from 'lib-mathlive';
import { BehaviorSubject, fromEvent, merge, Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, tap } from 'rxjs/operators';
import { ButtonData } from '../geometrytools.models';
import { SpinnerLabelComponent } from '../spin.label/spinner.label.component';
import { ConstrainRendererComponent } from './constrain-renderer/constrain-renderer.component';
import { HeighlightPipe } from './heighlight.pipe';
import { ParamInputComponent, ParamInputEvent } from './param-input/param-input.component';
import { resetEngine } from './param-input/setup-engine';
import { validateMathJSON } from './param-input/validator';

@Component({
    standalone: true,
    imports: [
        CommonModule,
        ConstrainRendererComponent,
        SpinnerLabelComponent,
        HeighlightPipe,
        ParamInputComponent,
        DragDropModule,
    ],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-geocommandtoolv2',
    styleUrls: ['commandtool.component.scss'],
    templateUrl: 'commandtool.component.html',
})
export class CommandtoolComponent implements OnInit, OnDestroy, AfterViewChecked, OnChanges {
    _editor: GeometryEditor;
    _toolbar: GeometryToolBar;

    private readonly sm = 640;

    private readonly toolStateListener: ToolEventListener<GeometryToolBar, GeometryToolType>;

    @ViewChild('searchTemplateInput') searchTemplateInput: ElementRef<HTMLInputElement>;
    // @ViewChild('objTypeTrigger') objTypeTrigger: MatAutocompleteTrigger;
    // @ViewChild('elNameInput') elNameInput: ElementRef<HTMLInputElement>;

    // @ViewChildren('objTypeElList') objTypeElList: QueryList<ElementRef<HTMLDivElement>>;
    @ViewChildren('tplElList') tplElList: QueryList<ElementRef<HTMLDivElement>>;
    // @ViewChildren('paramInputs') paramInputs: QueryList<ElementRef<HTMLInputElement>>;

    selectingConstraintTpl: ConstraintTemplateData;
    objTypeFormCtrl: FormControl = new FormControl();
    objTypes: [GeoObjectType, string][] = [];

    private inputChangeSubscription: Subscription;

    private objTypeSubject = new Subject<string>();
    private objTypeSubscription: Subscription;
    private refocusSearchTemplate = false;

    isShowListObject = false;
    isShowListTemplate = false;

    pieces: TemplatePiece[];

    tool: ButtonData<GeometryToolType>;

    possiblePointsNames: string[] = [];

    private viewPortId: string;
    private awarenessId: string;

    isSubmitConstruction$ = new BehaviorSubject<boolean>(false);

    errorMessage$ = new BehaviorSubject<string>('');

    constructor(
        public dialogRef: MatDialogRef<CommandtoolComponent>,
        @Inject(MAT_DIALOG_DATA)
        data: {
            toolbar: GeometryToolBar;
            tool: ButtonData<GeometryToolType>;
            viewportId: string;
        },
        private changeDetectorRef: ChangeDetectorRef
    ) {
        this.toolStateListener = new this._toolListener(this);
        this.toolbar = data.toolbar;
        this.tool = data.tool;
        this.viewPortId = data.viewportId;
        this.dialogRef.addPanelClass('command-tool');
    }

    @HostListener('keydown', ['$event'])
    @HostListener('keyup', ['$event'])
    handleKeydown(event: KeyboardEvent): void {
        if (event.target['nodeName'] === 'INPUT') event.stopPropagation();
    }

    onNoClick(): void {
        this.dialogRef.close();
        this.cancelConstraints();
    }

    async ngOnInit() {
        this.objTypeSubscription = merge(
            this.objTypeSubject,
            this.objTypeFormCtrl.valueChanges.pipe(tap(() => (this.selectingConstraintTpl = null)))
        )
            .pipe(map(value => (this.objTypes = this._filterObjType(value || ''))))
            .subscribe();
        this.onSelectObjType([this.tool.key, this.tool.viName]);
        this.possiblePointsNames = this.inputCommandTool.currentPointNames();

        this.awarenessId = await this._editor.awarenessFeature.sendAwarenessCommand(
            this.viewPortId,
            'Đang dựng hình bằng lệnh',
            {
                id: initRandomAwarenessId(),
                type: 'aw-loading',
                useScheduler: true,
                expireAfterSeconds: 5,
                schedulerInterval: 4,
                startAfterSeconds: 0,
                isNotSendToLocalReceiver: true,
            }
        );

        this.checkScreenWidth();
    }

    isDraggable$ = new BehaviorSubject<boolean>(false);

    @HostListener('window:resize')
    onResize() {
        this.checkScreenWidth();
    }

    checkScreenWidth() {
        this.isDraggable$.next(window.innerWidth > this.sm);
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolStateListener);
        this.objTypeSubject.unsubscribe();
        this.objTypeSubscription.unsubscribe();
        if (this.awarenessId) this._editor.awarenessFeature.clearAwarenessCommand(this.viewPortId, this.awarenessId);
        resetEngine(MathfieldElement?.computeEngine);
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['template']) this.pieces = this.parseTemplate();
    }

    ngAfterViewChecked(): void {
        if (!this.searchTemplateInput) {
            if (this.inputChangeSubscription) {
                this.inputChangeSubscription.unsubscribe();
                this.inputChangeSubscription = null;
            }
            return;
        }

        if (this.inputChangeSubscription) return;

        this.inputChangeSubscription = this.detectInputChange();
    }

    set toolbar(tb: GeometryToolBar) {
        this._toolbar = tb;
        this._editor = tb.editor;
        tb.registerToolListener(this.toolStateListener);
    }

    get inputCommandTool(): InputCommandTool {
        return this.toolbar.getTool('InputCommandTool') as InputCommandTool; // check
    }

    get toolbar(): GeometryToolBar {
        return this._toolbar;
    }

    get inputCommandToolState(): InputCommandToolState {
        return this.inputCommandTool.toolState;
    }

    @HostListener('document:click', ['$event'])
    onClickDocument(event: any) {
        const classes: string = event.target?.getAttribute('class');
        const parentClasses: string = event.target.parentElement?.getAttribute('class');
        const allClasses = classes + ' ' + parentClasses;

        if (event.target instanceof HTMLInputElement) {
            const target = event.target as HTMLInputElement;
            target.focus();
            return;
        }

        const ignoreElements = [
            'search-template-input',
            'template-menu-item',
            'dropdown-template-btn',
            'confirm-tpl-btn',
            'cancel-tpl-btn',
            'obj-type-item',
        ];
        if (!classes || ignoreElements.every(el => !allClasses.includes(el)))
            if (this.isShowListTemplate) this.isShowListTemplate = false;
    }

    private _filterObjType(type: any): [GeoObjectType, string][] {
        const value: string = typeof type === 'string' ? type : type[1];

        if (!this.inputCommandToolState) return [];

        return [...this.inputCommandToolState.objTypeStr.entries()].filter(en =>
            en[1].toLowerCase().includes(value.toLowerCase())
        );
    }

    private detectInputChange(): Subscription {
        if (!this.searchTemplateInput) return null;

        if (this.refocusSearchTemplate) {
            this.searchTemplateInput.nativeElement.focus();
            this.refocusSearchTemplate = false;
        }

        return fromEvent(this.searchTemplateInput.nativeElement, 'keyup')
            .pipe(
                map((e: any) => e.target.value),
                debounceTime(500),
                distinctUntilChanged(),
                map(keyword => keyword?.trim() ?? '')
            )
            .subscribe({
                next: (keyword: string) => {
                    if (keyword == null) return;

                    this.inputCommandToolState.tplKeyword = this.tool.key;
                    this.inputCommandToolState.tplKeyword = keyword;
                    this.toolbar.update('InputCommandTool', this.inputCommandToolState);
                },
            });
    }

    templates(): ConstraintTemplateData[] {
        return this.inputCommandTool.isReady()
            ? this.inputCommandToolState.templates.filter(t => t.template.trim().length > 1)
            : [];
    }

    requiredTemplates(): ConstraintTemplateData[] {
        return this.templates().filter(t => t.possibleConstraints?.[0]?.optional === false);
    }

    async cancelConstraints() {
        this.errorMessage$.next('');

        this.selectingConstraintTpl = null;
        this.isShowListTemplate = false;
        await this.inputCommandTool.cancelConstraints();
    }

    onKeyupTemplateItem(event: KeyboardEvent, idx: number) {
        const previousIdx = idx === 0 ? this.tplElList.length - 1 : idx - 1;
        const nextIdx = idx === this.tplElList.length - 1 ? 0 : idx + 1;

        switch (event.key) {
            case 'ArrowUp':
                this.tplElList.get(previousIdx).nativeElement.focus();
                break;
            case 'ArrowDown':
                this.tplElList.get(nextIdx).nativeElement.focus();
                break;
            case 'Enter':
                this.tplElList.get(idx).nativeElement.click();
                break;
            default:
                this.searchTemplateInput.nativeElement.focus();
                break;
        }
    }

    onSelectTemplate(tplData: ConstraintTemplateData) {
        this.errorMessage$.next('');

        if (this.searchTemplateInput) this.searchTemplateInput.nativeElement.value = '';

        this.selectingConstraintTpl = tplData;
        this.pieces = this.parseTemplate();

        this.changeDetectorRef.detectChanges();
    }

    transformTemplateFulfill(templateFulfill: string): string {
        const regex = /{([^}]+?)}/g;
        return templateFulfill.replace(regex, match =>
            match.replace(
                /[^]*$/,
                `<span class="curly-braces">&#123;</span><span class="content">${match.substring(1, match.length - 1)}</span><span class="curly-braces">&#125;</span>`
            )
        );
    }

    transformLatexTemplateFulfill(templateFulfill: string): string {
        // regex match:           {{$ ... $}}            OR          [{$ ... $}]
        const regex = /(\{\{\$(.*?)(?:(?!\$\}\}).)*\$\}\}|\[\{\$(.*?)(?:(?!\$\}\]).)*\$\}\])/g;
        return templateFulfill.replace(regex, match =>
            match.replace(
                /[^]*$/,
                `<span class="curly-braces">&#123;</span><span class="content">\\(${match.substring(3, match.length - 3)}\\)</span><span class="curly-braces">&#125;</span>`
            )
        );
    }

    showListObject(): void {}

    hideListObject() {
        this.isShowListObject = false;
    }

    onSelectObjType(objType: [GeoObjectType, string]) {
        this.errorMessage$.next('');
        this.inputCommandToolState.objType = objType[0];
        this.objTypeFormCtrl.setValue(objType[1]);

        this.toolbar.update('InputCommandTool', this.inputCommandToolState);

        if (this.selectedConstraints) this.inputCommandTool.cancelConstraints();

        this.cancelConstraintInput(false);

        this.isShowListObject = false;
        this.isShowListTemplate = true;
    }

    onFocusSearchTemplate() {
        this.isShowListObject = false;
        this.isShowListTemplate = true;

        const tplKeywordInputLength = this.searchTemplateInput.nativeElement.value?.length ?? 0;
        const tplKeywordToolstateLength = this.inputCommandToolState.tplKeyword?.length ?? 0;

        if (!tplKeywordInputLength && tplKeywordToolstateLength) {
            this.inputCommandToolState.tplKeyword = '';
            this.toolbar.update('InputCommandTool', this.inputCommandToolState);
        }
    }

    disabledSearchTemplate() {
        if (!this.inputCommandTool.isReady()) return true;
        if (typeof this.objTypeFormCtrl.value === 'object') return !this.objTypeFormCtrl.value;
        return ![...this.inputCommandToolState.objTypeStr.values()].includes(this.objTypeFormCtrl.value);
    }

    onKeyNameInput(event: KeyboardEvent) {
        switch (event.key) {
            case 'Tab':
            case 'Enter':
                this.isShowListTemplate = true;
                this.searchTemplateInput.nativeElement.focus();
                event.preventDefault();
                this.errorMessage$.next('');
                break;
        }

        event.stopPropagation();
    }

    async onKeyupSearchTemplate(event: KeyboardEvent) {
        switch (event.key) {
            case 'Escape':
                if (this.isShowListTemplate) this.isShowListTemplate = false;
                break;
            case 'Enter':
                await this.submitConstraints();
                break;
            case 'ArrowUp':
                if (this.tplElList.length <= 0) return;
                this.tplElList.get(this.tplElList.length - 1).nativeElement.focus();
                break;
            case 'ArrowDown':
                if (this.tplElList.length <= 0) return;
                this.tplElList.get(0).nativeElement.focus();
                break;
        }
        event.stopPropagation();
    }

    get selectedConstraints(): UserConstraintInputData[] {
        return this.inputCommandTool.getUserConstraintSelection()?.selectedConstraints;
    }

    removeCompletedConstraint(event: any, index: number) {
        this.errorMessage$.next('');

        this.inputCommandTool.removeConstraint(index);

        this.selectingConstraintTpl = null;

        this.inputCommandToolState.tplKeyword = this.inputCommandToolState.tplKeyword;
        this.toolbar.update('InputCommandTool', this.inputCommandToolState);

        if (this.searchTemplateInput) this.searchTemplateInput.nativeElement.focus();
        else this.refocusSearchTemplate = true;

        event.stopPropagation();
    }

    async submitConstraints() {
        this.isSubmitConstruction$.next(true);
        this.errorMessage$.next('');

        try {
            const df = this.inputCommandToolState.templates.find(t => t.template.trim().length <= 0);
            if ((!this.selectedConstraints || this.selectedConstraints.length <= 0) && df) {
                this.selectingConstraintTpl = df;
                this.completeConstraintInput();
            }

            this.selectingConstraintTpl = null;
            this.refocusSearchTemplate = false;
            this.isShowListTemplate = false;
            this.isShowListObject = false;
            this.searchTemplateInput.nativeElement.value = '';

            await this.inputCommandTool.submitConstraints();
            if (this.inputCommandTool.getUserConstraintSelection())
                this.inputCommandTool.getUserConstraintSelection().selectedConstraints = [];
            this.cancelConstraints();

            this.isSubmitConstruction$.next(false);
        } catch (e: unknown) {
            // Get axios error
            let axiosErr: AxiosError;
            if (e['state']) {
                const state = e['state'];
                if (state instanceof ViErr) axiosErr = state.raw as AxiosError;
                else if (state instanceof AxiosError) axiosErr = state;
            }

            // Handle axios error
            if (axiosErr) {
                this.isSubmitConstruction$.next(false);
                switch (axiosErr.response?.status) {
                    case 400:
                        this.errorMessage$.next('Tham số không hợp lệ');
                        break;
                    case 403:
                        this.errorMessage$.next('Bạn không có quyền thực hiện hành động này');
                        break;
                    default:
                        this.errorMessage$.next('Có lỗi xảy ra khi dựng hình');
                        break;
                }
                throw e;
            }

            this.isSubmitConstruction$.next(false);
            this.errorMessage$.next('Có lỗi xảy ra khi dựng hình');
            throw e;
        }
    }

    enableSubmitAndCancelConstraintsBtn(): boolean {
        if (!this.inputCommandTool.isReady()) return false;

        return (
            this.inputCommandToolState.templates.length > 0 ||
            (!!this.selectedConstraints && this.selectedConstraints.length > 0)
        );
    }

    private parseTemplate(): TemplatePiece[] {
        const result: TemplatePiece[] = [];
        const paramKindValues = Object.values(ParamKind);
        const regex = new RegExp(
            '({(' +
                paramKindValues.join('|') +
                ')})|(\\((' +
                paramKindValues.join('|') +
                ')\\))|(\\[(' +
                paramKindValues.join('|') +
                ')\\])',
            'gd'
        );

        let preResult: any;
        let curResult: any;
        while ((curResult = regex.exec(this.selectingConstraintTpl.template)) != null) {
            const preMatchedGroup = this.findMatchedGroup(preResult);
            const curMatchedGroup = this.findMatchedGroup(curResult);

            const substr = curResult.input.substring(preMatchedGroup ? preMatchedGroup[1] : 0, curMatchedGroup[0]);
            if (substr)
                result.push({
                    type: 'text',
                    content: substr,
                });

            result.push({
                type: 'input',
                content: curResult.input.substring(curMatchedGroup[0], curMatchedGroup[1]),
                input: '',
            });

            preResult = curResult;
        }

        const preMatchedGroup = this.findMatchedGroup(preResult);
        if (preMatchedGroup && preMatchedGroup[1] < this.selectingConstraintTpl.template.length)
            result.push({
                type: 'text',
                content: preResult.input.substring(preMatchedGroup[1], this.selectingConstraintTpl.template.length),
            });

        return result;
    }

    private findMatchedGroup(parsedResult: any): number[] {
        if (!parsedResult) return undefined;

        // match with {}
        if (parsedResult.indices[1]) return parsedResult.indices[1];

        // match with ()
        if (parsedResult.indices[3]) return parsedResult.indices[3];

        // match with []
        if (parsedResult.indices[5]) return parsedResult.indices[5];

        return undefined;
    }

    onParamInput(idx: number, input: ParamInputEvent) {
        const piece = this.pieces[idx];
        if (piece.type !== 'input') throw 'Update invalid type of template piece';

        piece.latex = input.latex;

        if (piece.content.substring(1, piece.content.length - 1) === 'name')
            // name input can contains new point name -> can not reliably parse to MathJSON
            piece.input = input.latex.replace(/\^{\\prime}/g, "'");
        else {
            piece.input = input.mathjson ? JSON.stringify(input.mathjson) : undefined;
            piece.error = undefined;
            if (input.mathjson) {
                const [valid, errMsg] = validateMathJSON(piece.input);
                if (!valid) piece.error = errMsg ?? 'Lỗi không xác định';
            }
        }
    }

    getFirstInputIdx(): number {
        for (let i = 0; i < this.pieces?.length; i++) if (this.pieces[i].type === 'input') return i;
        return -1;
    }

    getFirstParamError(): string | undefined {
        return this.pieces?.find(p => !!p.error)?.error;
    }

    userInputs(): UserParamInput[] {
        if (!this.pieces) return undefined;

        return this.pieces
            .filter(p => p.type === 'input')
            .map(p => ({
                paramKind: ParamKind[p.content.substring(1, p.content.length - 1)],
                input: p.input,
            }));
    }

    templateFulfill(): string {
        if (!this.pieces) return undefined;

        return this.pieces
            .map(p => {
                switch (p.type) {
                    case 'text':
                        return p.content;
                    case 'input':
                        return p.content[0] + '{$' + p.latex + '$}' + p.content[p.content.length - 1];
                    default:
                        return '';
                }
            })
            .reduce((pre, cur) => pre + cur, '');
    }

    isFulfill(): boolean {
        return this.pieces?.filter(p => p.type === 'input')?.every(p => !!p.input) && !this.getFirstParamError();
    }

    onKeyUpInputConstraint(event: KeyboardEvent) {
        if (event.key === 'Escape') this.cancelConstraintInput();
        else if (event.key === 'Enter' && event.shiftKey && this.isFulfill()) {
            // we need Shift + Enter to submit as we can enter latex in the input box
            // when using only Enter, it will conflict with the latex suggestion confirmation of mathlive
            this.completeConstraintInput();
            event.preventDefault();
        }
        event.stopPropagation();
    }

    completeConstraintInput() {
        this.errorMessage$.next('');

        if (this.selectingConstraintTpl.template || this.selectingConstraintTpl.template === '') {
            this.inputCommandTool.toolState.tplKeyword = '';
            this.inputCommandTool.toolState.userConstraintInput = {
                template: this.selectingConstraintTpl.possibleConstraints[0].tplStringId,
                possibleConstructors: this.selectingConstraintTpl.possibleConstraints,
                inputs: this.userInputs(),
                templateFulfill: this.templateFulfill(),
            } as UserConstraintInputData;

            this.selectingConstraintTpl = null;

            this.toolbar.update('InputCommandTool', this.inputCommandTool.toolState);

            this.refocusSearchTemplate = true;
        }
    }

    cancelConstraintInput(refocus = true) {
        this.errorMessage$.next('');
        this.selectingConstraintTpl = null;

        const selection = this.selectedConstraints;
        if (selection?.length === 0) this.isShowListTemplate = false;

        if (this.searchTemplateInput) {
            this.searchTemplateInput.nativeElement.value = '';
            if (refocus) {
                this.refocusSearchTemplate = refocus;
                this.inputCommandToolState.tplKeyword = '';
                this.toolbar.update('InputCommandTool', this.inputCommandToolState);
            }
        } else this.refocusSearchTemplate = refocus;

        if (this.inputChangeSubscription) {
            this.inputChangeSubscription.unsubscribe();
            this.inputChangeSubscription = null;
        }
    }

    private _toolListener = class implements ToolEventListener<GeometryToolBar, GeometryToolType> {
        constructor(private readonly comp: CommandtoolComponent) {}

        onEvent(eventData: GeoToolEventData): GeoToolEventData | Promise<GeoToolEventData> {
            if (!['InputCommandTool', 'GeoDocFloatingUITool'].includes(eventData.toolType)) return eventData;

            if (eventData.eventType === 'change' && eventData instanceof ChangeToolEventData) {
                if (!this.comp.inputCommandTool.isReady()) return eventData;

                const changeEvent = eventData as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
                if (changeEvent.changes.has('templates')) {
                    if (!this.comp.isShowListTemplate) this.comp.isShowListTemplate = true;
                    if (this.comp.refocusSearchTemplate) {
                        this.comp.searchTemplateInput?.nativeElement.focus();
                        this.comp.refocusSearchTemplate = false;
                    }
                }
            }

            this.comp.changeDetectorRef.detectChanges();

            return eventData;
        }
    };
}

interface TemplatePiece {
    type: 'text' | 'input';
    content: string;
    input?: string;
    latex?: string;
    error?: string;
}
