import { ComponentRef, createNgModule, Injector, ViewContainerRef } from '@angular/core';
import { EditorUIComponent } from '@viclass/editorui.loader';
import { GeometryToolsModule } from './geometrytools.module';
import { GeometrytoolsComponent } from './geometrytools.component';

export function factory(
    viewContainerRef: ViewContainerRef,
    injector: Injector
): Promise<ComponentRef<EditorUIComponent>> {
    const module = createNgModule(GeometryToolsModule, injector);
    return new Promise((rs, rj) => {
        rs(
            module.injector.runInContext(() =>
                viewContainerRef.createComponent(GeometrytoolsComponent, {
                    injector: injector,
                    ngModuleRef: module,
                })
            )
        );
    });
}
