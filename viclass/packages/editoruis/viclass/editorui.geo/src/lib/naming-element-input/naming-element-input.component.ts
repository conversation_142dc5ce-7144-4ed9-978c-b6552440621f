import {
    ConnectedPosition,
    ConnectionPositionPair,
    HorizontalConnectionPos,
    Overlay,
    OverlayConfig,
    OverlayRef,
    VerticalConnectionPos,
} from '@angular/cdk/overlay';
import { CdkPortal } from '@angular/cdk/portal';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    QueryList,
    ViewChild,
    ViewChildren,
} from '@angular/core';
import { ChangeToolEventData, ToolBar, ToolEventListener, ToolState } from '@viclass/editor.core';
import {
    GeometryTool,
    GeometryToolBar,
    GeometryToolType,
    GeoRelType,
    GeoToolEventData,
    NamingElementTool,
    NamingElementToolState,
    ValidationResult,
} from '@viclass/editor.geo';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

const NAMING_ELEMENT_TOOL_TYPE: GeometryToolType = 'NamingElementTool';
const ACTION_CONFIRM = 'confirm';
const ACTION_CANCEL = 'cancel';
const ACTION_AUTO = 'auto';

@Component({
    selector: 'tb-naming-element-input',
    templateUrl: './naming-element-input.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NamingElementInputComponent implements OnInit, OnDestroy {
    @ViewChild('namingInputPortal', { read: CdkPortal, static: false }) namingInputPortal!: CdkPortal;
    @ViewChildren('nameInput') nameInput!: QueryList<ElementRef<HTMLSpanElement>>;

    @Input() toolBar!: GeometryToolBar;
    @Input() vAlign: 'top' | 'center' | 'bottom' = 'top';
    @Input() hAlign: 'left' | 'center' | 'right' = 'center';
    @Input() direction: 'ltr' | 'rtl' | 'btt' | 'ttb' = 'ltr';

    private overlayRef?: OverlayRef;
    private readonly toolListener = NamingElementInputComponent.NamingElementToolListener(this);
    private currentFocusIdx = -1;
    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly overlay: Overlay,
        private readonly changeDetectorRef: ChangeDetectorRef
    ) {}

    ngOnInit(): void {
        this.toolBar.registerToolListener(this.toolListener);
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.toolBar.unregisterToolListener(this.toolListener);
        this.detachInput();
    }

    get tool(): NamingElementTool | undefined {
        return this.toolBar?.getTool(NAMING_ELEMENT_TOOL_TYPE) as NamingElementTool | undefined;
    }

    get state(): NamingElementToolState | undefined {
        return this.toolBar?.toolState(NAMING_ELEMENT_TOOL_TYPE) as NamingElementToolState | undefined;
    }

    attachInput(): void {
        if (this.overlayRef || !this.state) {
            return;
        }

        const vpRoot = this.toolBar?.viewport?.rootEl;
        if (!vpRoot) {
            return;
        }

        const config = new OverlayConfig({
            positionStrategy: this.overlay.position().flexibleConnectedTo(vpRoot).withPositions(this.overlayPositions),
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            hasBackdrop: true,
            backdropClass: 'backdrop-10',
        });
        this.overlayRef = this.overlay.create(config);

        this.currentFocusIdx = -1; // Reset focus index for predictable focusing

        this.overlayRef
            .attachments()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                requestAnimationFrame(() => this.focusNextInput());
            });

        this.overlayRef.attach(this.namingInputPortal);
        this.changeDetectorRef.markForCheck();
    }

    detachInput(): void {
        this.overlayRef?.detach();
        this.overlayRef?.dispose();
        this.overlayRef = undefined;
        this.changeDetectorRef.markForCheck();
    }

    onBeforeInput(event: InputEvent): void {
        if (event.inputType === 'insertParagraph') {
            event.preventDefault();
        }
    }

    onNamingChange(event: Event, idx: number, type: GeoRelType): void {
        if (!this.state) return;
        const rn = this.state.requireNameForUsers.find(e => e.type === type);
        const target = event.target as HTMLElement;
        rn.inputElNames[idx] = target.innerText;
        this.state.idxValidating = idx;
        this.state.typeValidating = type;
        this.state.notProcess = false;
        this.toolBar.update(NAMING_ELEMENT_TOOL_TYPE, this.state);
    }

    get hasError(): boolean {
        if (!this.state) return true; // Default to error state if state is not available
        // returns true if *any* name in *any* entry is undefined
        const hasUnfilledNames = this.state.requireNameForUsers.some(req =>
            req.inputElNames.some(name => name === undefined)
        );
        return (
            hasUnfilledNames ||
            !this.state.validateResult.length ||
            (this.state.validateResult?.some((v: ValidationResult) => !v.valid) ?? false)
        );
    }

    get getError(): { idx: number; message: string } | undefined {
        if (!this.state?.validateResult) return undefined;

        for (let i = 0; i < this.state.validateResult.length; i++) {
            const validateResult = this.state.validateResult[i];
            if (validateResult && !validateResult.valid && validateResult.message) {
                return { idx: i, message: validateResult.message };
            }
        }
        return undefined;
    }

    onSubmit(): void {
        console.log('[NamingElementInput] onSubmit START. HasError:', this.hasError, 'State:', !!this.state);
        if (this.hasError || !this.state) return;

        this.state.result = {
            action: ACTION_CONFIRM,
            data: this.state.requireNameForUsers,
        };
        this.state.notProcess = false;
        this.toolBar.update(NAMING_ELEMENT_TOOL_TYPE, this.state);
    }

    onFocus(idx: number): void {
        this.currentFocusIdx = idx;
    }

    onKeyup(event: KeyboardEvent): void {
        console.log('[NamingElementInput] onKeyup:', event.key);
        switch (event.key) {
            case 'Enter':
                console.log('[NamingElementInput] Enter pressed in onKeyup');
                this.onSubmit();
                event.preventDefault();
                break;
            case 'Escape':
                console.log('[NamingElementInput] Escape pressed in onKeyup');
                this.onCancel();
                event.preventDefault();
                event.stopPropagation();
                break;
        }
    }

    private getSelectionRange(): Range | null {
        const selection = window.getSelection();
        return selection && selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
    }

    private isCursorAtStart(range: Range | null): boolean {
        return !!range && range.collapsed && range.startOffset === 0;
    }

    private isCursorAtEnd(idx: number, range: Range | null, type: GeoRelType): boolean {
        if (!range || !range.collapsed || !this.state) return false;
        const rn = this.state.requireNameForUsers.find(e => e.type === type);
        const currentTextLength = rn.inputElNames[idx]?.length ?? 0;
        return range.startOffset >= currentTextLength;
    }

    onKeydown(event: KeyboardEvent, idx: number, type: GeoRelType): void {
        if (!this.state) return;

        const range = this.getSelectionRange();

        switch (event.key) {
            case 'ArrowLeft':
                if (this.isCursorAtStart(range)) {
                    this.focusPreviousInput();
                    event.preventDefault();
                }
                return;
            case 'Backspace':
                if (this.isCursorAtStart(range)) {
                    this.focusPreviousInput();
                    // Potentially prevent default if it might delete the span or cause other issues
                    // event.preventDefault();
                }
                return;
            case 'ArrowRight':
                if (this.isCursorAtEnd(idx, range, type)) {
                    this.focusNextInput();
                    event.preventDefault();
                }
                return;
            case 'Delete':
                if (this.isCursorAtEnd(idx, range, type)) {
                    this.focusNextInput();
                    // event.preventDefault();
                }
                return;
        }

        const currentText = (event.target as HTMLElement).innerText;
        // If current input is valid, not empty, and a capital letter is pressed (without modifiers)
        // move to the next input. This implies single-character validated inputs.
        if (
            this.state.validateResult?.[idx]?.valid &&
            currentText.length > 0 &&
            /^[A-Z]$/.test(event.key) &&
            !event.ctrlKey &&
            !event.metaKey &&
            !event.altKey
        ) {
            this.focusNextInput();
            // To type the character into the newly focused input would require more complex handling
            // (e.g., event.preventDefault() and programmatically setting the new input's value).
        }
    }

    private focusInputElement(
        elementRef: ElementRef<HTMLSpanElement> | undefined,
        position: 'start' | 'end' | number
    ): void {
        const el = elementRef?.nativeElement;
        if (!el) return;

        el.focus({ preventScroll: true }); // Prevent unintended page scroll

        const selection = window.getSelection();
        if (!selection) return;

        const range = document.createRange();
        // Ensure there's a text node for setting the caret, otherwise select the element itself.
        const textNode = el.firstChild && el.firstChild.nodeType === Node.TEXT_NODE ? el.firstChild : null;

        if (textNode) {
            const textLength = textNode.textContent?.length ?? 0;
            let offset = 0;
            if (typeof position === 'number') {
                offset = Math.max(0, Math.min(position, textLength));
            } else if (position === 'end') {
                offset = textLength;
            }
            range.setStart(textNode, offset);
            range.setEnd(textNode, offset);
        } else {
            // For empty elements or elements without a direct text node child
            range.selectNodeContents(el);
            range.collapse(position === 'start' || position === 0);
        }

        selection.removeAllRanges();
        selection.addRange(range);
    }

    private findFocusableInput(
        startIndex: number,
        direction: 'next' | 'previous'
    ): ElementRef<HTMLSpanElement> | undefined {
        if (!this.nameInput) return undefined;

        const numInputs = this.nameInput.length;
        if (numInputs === 0) return undefined;

        if (direction === 'next') {
            for (let i = startIndex; i < numInputs; i++) {
                const elRef = this.nameInput.get(i);
                if (elRef && !elRef.nativeElement.classList.contains('name-origin')) {
                    return elRef;
                }
            }
        } else {
            // 'previous'
            for (let i = startIndex; i >= 0; i--) {
                const elRef = this.nameInput.get(i);
                if (elRef && !elRef.nativeElement.classList.contains('name-origin')) {
                    return elRef;
                }
            }
        }
        return undefined;
    }

    private focusNextInput(): void {
        const nextElRef = this.findFocusableInput(this.currentFocusIdx + 1, 'next');
        if (nextElRef) {
            this.focusInputElement(nextElRef, 'start');
        }
    }

    private focusPreviousInput(): void {
        const prevElRef = this.findFocusableInput(this.currentFocusIdx - 1, 'previous');
        if (prevElRef) {
            this.focusInputElement(prevElRef, 'end');
        }
    }

    hasInputValue(idx: number, type: GeoRelType): boolean {
        if (!this.state) return false;
        const rn = this.state.requireNameForUsers.find(e => e.type === type);
        const originName = rn.originElNames[idx];
        const inputName = rn.inputElNames[idx]?.trim();
        return (!originName || originName.length === 0) && !!inputName && inputName.length > 0;
    }

    onCancel(): void {
        console.log('[NamingElementInput] onCancel START. State:', !!this.state);
        if (!this.state) return;
        this.state.notProcess = false;
        this.state.result = { action: ACTION_CANCEL };
        this.toolBar.update(NAMING_ELEMENT_TOOL_TYPE, this.state);
    }

    onAuto(): void {
        if (!this.state) return;
        this.state.notProcess = false;
        this.state.result = { action: ACTION_AUTO };
        this.toolBar.update(NAMING_ELEMENT_TOOL_TYPE, this.state);
    }

    private get overlayPositions(): ConnectedPosition[] {
        // This preserves the original positioning logic which was a bit specific.
        // It used the same verticalConnectionPos for origin and overlay, then applied an offset.
        const originX: HorizontalConnectionPos = 'center'; // As per original
        let verticalConnectionPos: VerticalConnectionPos = 'top'; // Default as per original
        const offsetX = 0; // As per original
        let offsetY = 25; // Default as per original. Positive Y pushes overlay DOWN.

        const isToolbarHorizontal = this.direction === 'ltr' || this.direction === 'rtl';

        if (isToolbarHorizontal && this.vAlign === 'top') {
            // If toolbar is horizontal and user wants input at "top" (presumably above toolbar)
            // Original logic: set vertical anchor to 'bottom' and offsetY to -25.
            // This means: anchor overlay's bottom to origin's bottom, then shift overlay UP by 25px.
            verticalConnectionPos = 'bottom';
            offsetY = -25; // Negative Y pushes overlay UP.
        }
        // Note: The original logic didn't explicitly handle other vAlign values ('center', 'bottom')
        // in conjunction with 'direction'. They would fall into the default case:
        // verticalConnectionPos = 'top', offsetY = 25 (overlay 25px below origin's top edge).
        // This might need review if more flexible vAlign behavior is desired.

        return [
            new ConnectionPositionPair(
                { originX, originY: verticalConnectionPos },
                { overlayX: originX, overlayY: verticalConnectionPos },
                offsetX,
                offsetY
            ),
            // Add more fallback positions for robustness if the primary one doesn't fit.
            // Example: Attempt to position above, then below, then centered.
            new ConnectionPositionPair( // Above
                { originX: 'center', originY: 'top' },
                { overlayX: 'center', overlayY: 'bottom' },
                0,
                -10 // Small gap above
            ),
            new ConnectionPositionPair( // Below
                { originX: 'center', originY: 'bottom' },
                { overlayX: 'center', overlayY: 'top' },
                0,
                10 // Small gap below
            ),
        ];
    }

    /**
     * Required for *ngFor in the template to optimize rendering.
     * Tracks items by their index and name to help Angular identify items that have been added, removed, or reordered.
     */
    trackByNameIndex(index: number, nameItem: string | undefined): string {
        // If names can be undefined or not unique, index is a safe bet.
        // Concatenating with name if available provides a bit more specificity.
        return `${index}_${nameItem ?? 'undefined'}`;
    }

    private static NamingElementToolListener(
        component: NamingElementInputComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: GeoToolEventData): GeoToolEventData {
                if (eventData.toolType === NAMING_ELEMENT_TOOL_TYPE) {
                    const changeEvent = eventData as ChangeToolEventData<GeometryToolBar, GeometryToolType>;
                    if (!changeEvent.changes) return eventData;

                    const requireNameChange = changeEvent.changes.get('requireName');
                    if (requireNameChange) {
                        if (requireNameChange.currentValue === true) component.attachInput();
                        else component.detachInput();
                    }
                    // Always mark for check if there are any changes to this tool's state,
                    // as getters like `hasError` or `getError` might depend on other state properties.
                    component.changeDetectorRef.markForCheck();
                }
                return eventData;
            }
        })();
    }
}
