import {
    ConnectedPosition,
    ConnectionPositionPair,
    HorizontalConnectionPos,
    Overlay,
    OverlayConfig,
    OverlayRef,
    VerticalConnectionPos,
} from '@angular/cdk/overlay';
import { CdkPortal } from '@angular/cdk/portal';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import {
    CreateRegularPolygonTool,
    GeometryTool,
    GeometryToolBar,
    GeometryToolType,
    RegularPolygonToolState,
} from '@viclass/editor.geo';
import { BehaviorSubject, Subscription } from 'rxjs';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-regular-polygon-edges-input',
    templateUrl: './regular-polygon-edges-input.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegularPolygonEdgesInputComponent implements OnInit, OnDestroy {
    @Input()
    toolBar: GeometryToolBar;

    @Input()
    vAlign: 'top' | 'center' | 'bottom';

    @Input()
    hAlign: 'left' | 'center' | 'right';

    @Input()
    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    @ViewChild('regularPolygonInput', { read: CdkPortal })
    regPolygonInputPortal: CdkPortal;

    private toolClickSubscription: Subscription;
    private overlayRef?: OverlayRef;
    private toolListener = RegularPolygonEdgesInputComponent.ToolListener(this);

    readonly inputValue$ = new BehaviorSubject<string>('');
    readonly isInputValid$ = new BehaviorSubject<boolean>(true);

    get regularPolygonTool(): CreateRegularPolygonTool {
        return this.toolBar.getTool('CreateRegularPolygonTool') as unknown as CreateRegularPolygonTool;
    }

    /**
     * Overlay positions for the overlay containing the input element.
     * These positions are calculated based on the input direction and vAlign.
     */
    get overlayPositions(): ConnectedPosition[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        let verticalConnectionPos: VerticalConnectionPos = 'top';

        const offsetX: number = 0;
        let offsetY: number = 25;

        const isHorizontal = this.direction == 'ltr' || this.direction == 'rtl';
        if (isHorizontal && this.vAlign == 'top') {
            verticalConnectionPos = 'bottom';
            offsetY = -25;
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    constructor(
        private overlay: Overlay,
        private changeDetectorRef: ChangeDetectorRef
    ) {}

    /**
     * Setup the component.
     * 1. Subscribe to the pointClickNumber$ event of the CreateRegularPolygonTool
     *    and attach/detach the input element based on the click number.
     * 2. Register the {@link RegularPolygonEdgesInputComponent.toolListener} to the toolBar.
     * 3. Set the initial value of the input element to the number of edges of the tool.
     */
    ngOnInit(): void {
        const tool = this.regularPolygonTool;
        if (tool) {
            this.toolClickSubscription = tool.pointClickNumber$.subscribe((clickNum: number) => {
                if (clickNum === 1 || clickNum === 2) {
                    this.attachRegularPolygonInput();
                } else {
                    this.detachRegularPolygonInput();
                }
            });
        }
        this.toolBar.registerToolListener(this.toolListener);

        this.inputValue$.next(tool.noEdge.toString());
    }

    /**
     * Clean up resources and detach the regular polygon input on component destruction.
     */
    ngOnDestroy(): void {
        this.detachRegularPolygonInput();
        this.toolClickSubscription?.unsubscribe();
        this.toolBar.unregisterToolListener(this.toolListener);
    }

    /**
     * Attach the regular polygon edges input to the viewport.
     * This is only done when the tool is in the first or second click state.
     * The input element is attached to the viewport and positioned at the top of the viewport.
     */
    attachRegularPolygonInput() {
        if (this.overlayRef) return; // already attach

        const vpRoot = this.toolBar?.viewport?.rootEl;
        if (!vpRoot) return;

        const config = new OverlayConfig({
            positionStrategy: this.overlay.position().flexibleConnectedTo(vpRoot).withPositions(this.overlayPositions),
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
        });
        this.overlayRef = this.overlay.create(config);
        this.overlayRef.attach(this.regPolygonInputPortal);
    }

    /**
     * Detaches the regular polygon input overlay from the viewport if it is currently attached.
     */
    detachRegularPolygonInput() {
        this.overlayRef?.detach();
        this.overlayRef = undefined;
    }

    /**
     * Handles the change event for the regular polygon edge input.
     * Updates the input value observable and sets the number of edges
     * in the regular polygon tool based on the input value.
     */
    onRegularPolygonChangeNoEdge(event: any) {
        this.inputValue$.next(event.target.value);

        this.regularPolygonTool.noEdge = parseInt(event.target.value);
    }

    /**
     * Retrieves the current number of edges set in the CreateRegularPolygonTool's state.
     */
    get noEdge(): number {
        const toolState = this.toolBar.toolState('CreateRegularPolygonTool') as RegularPolygonToolState;
        return toolState.noEdge;
    }

    /**
     * Updates the input validity and value observables based on the current state
     * of the regular polygon tool. If the tool state is valid, the input value
     * observable is updated with the current number of edges.
     */
    private updateInputFromToolState() {
        const tool = this.regularPolygonTool;

        this.isInputValid$.next(tool.isValid);
        if (tool.isValid) this.inputValue$.next(tool.noEdge.toString());
    }

    /**
     * The tool listener for the regular polygon edges input component.
     * It updates the input value based on the tool state when the tool type is 'CreateRegularPolygonTool'.
     */
    private static ToolListener(
        _p: RegularPolygonEdgesInputComponent
    ): ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType> {
        return new (class
            implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
        {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if ((eventData.toolType as GeometryToolType) == 'CreateRegularPolygonTool') {
                    _p.updateInputFromToolState();
                    _p.changeDetectorRef.markForCheck();
                }
                return eventData;
            }
        })();
    }
}
