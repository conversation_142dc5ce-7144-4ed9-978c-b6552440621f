import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';
import { DocRenderPropType, SettingPropertyType } from '@viclass/editor.geo';

@Component({
    selector: 'lib-setting-tool-adjust-number',
    templateUrl: './setting-tool-adjust-number.component.html',
    styleUrls: ['./setting-tool-adjust-number.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolAdjustNumberComponent {
    // Input properties for component configuration
    @Input() label?: string; // Optional label text
    @Input() field: SettingPropertyType | DocRenderPropType; // Field type identifier
    @Input() value: SettingFieldValue; // Current value of the field
    @Input() suffix?: string; // Optional suffix to display after value
    @Input() min?: number; // Optional minimum value constraint
    @Input() disabled?: boolean; // Optional disabled state
    @Input() max?: number; // Optional maximum value constraint
    @Input() step: number = 1; // Step size for increments/decrements, defaults to 1
    @Input() valueList: number[] = []; // Optional list of allowed values

    // Event emitter for value changes
    @Output() onChange: EventEmitter<SettingFieldChangeEmitterData>;

    // Timer reference for continuous value changes on button press
    pressTimeout?: number;

    constructor() {
        // Inject change detector for manual updates
        this.onChange = new EventEmitter();
    }

    /**
     * Updates the component value when plus/minus buttons are pressed
     * Handles both discrete value lists and continuous numeric ranges
     *
     * @param {number} increaseValue - Positive for increment, negative for decrement
     * @param {boolean} startPress - True for initial press, false for subsequent updates
     */
    changeValue(increaseValue: number, startPress = true) {
        // Get current value or default to 0
        let newVal = this.value?.value ?? 0;

        // Handle discrete value list mode
        if (this.valueList.length) {
            // Clamp current value to value list range
            newVal = Math.max(newVal, this.valueList[0]);
            newVal = Math.min(newVal, this.valueList[this.valueList.length - 1]);

            // Find next appropriate value in the list
            for (const [idx, val] of this.valueList.entries()) {
                if (val > newVal) {
                    // If current list value is higher than newVal
                    if (increaseValue > 0)
                        newVal = val; // Move up to this value
                    else if (increaseValue < 0) newVal = this.valueList[idx - 1 < 0 ? 0 : idx - 1]; // Move down to previous value
                    break;
                } else if (val === newVal) {
                    // If we found the current value in the list
                    if (increaseValue > 0)
                        newVal = this.valueList[idx + 1 > this.valueList.length - 1 ? idx : idx + 1]; // Move up
                    else if (increaseValue < 0) newVal = this.valueList[idx - 1 < 0 ? 0 : idx - 1]; // Move down
                    break;
                }
            }
        } else {
            // Continuous numeric mode - simply add the increment
            newVal += increaseValue;
        }

        // Apply min/max constraints if specified
        newVal = Math.max(newVal, this.min ?? newVal);
        newVal = Math.min(newVal, this.max ?? newVal);

        // Update the value and clear mixed state
        this.value.value = newVal;
        this.value.isMixed = false;

        // Emit change event
        this.onChange.emit({ field: this.field, value: newVal });

        // Schedule next update if button is held down
        // 500ms delay for first repeat, then 100ms for subsequent repeats
        this.pressTimeout = setTimeout(() => this.changeValue(increaseValue, false), startPress ? 500 : 100) as any;
    }

    /**
     * Cancels the continuous value change timer when button is released
     */
    cancelPointerPress() {
        if (this.pressTimeout) {
            clearTimeout(this.pressTimeout);
            this.pressTimeout = undefined;
        }
    }

    /**
     * Computed property that determines if minus button should be disabled
     * Based on current value, value list constraints, and min value
     */
    get isMinusDisabled() {
        return (
            this.disabled ||
            (this.valueList && this.valueList.length > 0 && this.value.value <= this.valueList[0]) ||
            (![null, undefined].includes(this.min) && this.value.value <= this.min)
        );
    }

    /**
     * Computed property that determines if plus button should be disabled
     * Based on current value, value list constraints, and max value
     */
    get isPlusDisabled() {
        return (
            this.disabled ||
            (this.valueList &&
                this.valueList.length > 0 &&
                this.value.value >= this.valueList[this.valueList.length - 1]) ||
            (![null, undefined].includes(this.max) && this.value.value >= this.max)
        );
    }
}
