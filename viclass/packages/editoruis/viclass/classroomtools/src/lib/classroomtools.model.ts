import { InjectionToken } from '@angular/core';
import { EditorType } from '@viclass/editor.core';
import { ComponentSettings } from '@viclass/editorui.loader';

export const MODULE_SETTINGS_TOKEN = new InjectionToken<ClassroomToolsModuleSettings>(
    'classroom-tools-module-settings-token'
);
export const CLASSROOM_TOOLS_SETTINGS_TOKEN = new InjectionToken<ClassroomToolsSettings>(
    'classroom-tools-settings-token'
);

export class ClassroomToolsSettings implements ComponentSettings {
    availableEditors: EditorType[] = [];
    iconClasses: { [key in EditorType]: string };
}

export class ClassroomToolsModuleSettings {
    // default settings
    classroomToolsSettings: ClassroomToolsSettings = new ClassroomToolsSettings();
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    param?: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
};
