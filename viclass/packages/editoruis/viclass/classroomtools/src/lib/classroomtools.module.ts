import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { Injector, NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EditorUILoaderComponent } from '@viclass/editorui.loader';
import {
    CLASSROOM_TOOLS_SETTINGS_TOKEN,
    ClassroomToolsModuleSettings,
    MODULE_SETTINGS_TOKEN,
} from './classroomtools.model';
import { ClassroomToolsComponent } from './classroomtools/classroomtools.component';
import { ToolbarButtonComponent } from './toolbar-button/toolbar-button.component';
import { TooltipComponent } from './tooltip/tooltip.component';

@NgModule({
    declarations: [ClassroomToolsComponent, ToolbarButtonComponent],
    imports: [CommonModule, OverlayModule, FormsModule, MatSlideToggleModule, TooltipComponent],
    exports: [ClassroomToolsComponent],
    providers: [
        {
            provide: MODULE_SETTINGS_TOKEN,
            useValue: new ClassroomToolsModuleSettings(),
        },
        {
            provide: CLASSROOM_TOOLS_SETTINGS_TOKEN,
            useFactory: (moduleSettings: ClassroomToolsModuleSettings) => moduleSettings.classroomToolsSettings,
            deps: [MODULE_SETTINGS_TOKEN],
        },
        {
            provide: OverlayContainer,
            useFactory: (comp: EditorUILoaderComponent) => {
                return comp.overlayContainer;
            },
            deps: [EditorUILoaderComponent],
        },
        { provide: Overlay, useClass: Overlay },
    ],
})
export class ClassroomToolsModule {
    constructor(private injector: Injector) {}
}
