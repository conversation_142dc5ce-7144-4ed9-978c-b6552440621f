import { ConnectionPositionPair } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { MathGraphToolbarBtnStateService } from '../maghtoolbar-btn-state.service';
import { ButtonData } from '../maghtools.models';

@Component({
    selector: 'tb-toolbar-button',
    templateUrl: './toolbar-button.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolbarButtonComponent {
    @Input()
    data: ButtonData<any>;

    @Input()
    subMenuPositions: ConnectionPositionPair[];

    @Input()
    isToolActive: (toolName: any, param?: string) => boolean = _ => false;

    @Input()
    allowCheck: (toolName: any) => boolean = _ => true;

    @Input()
    hideOnUnallowed = false;

    @Input()
    parentPath: string = '';

    @Output()
    btnClicked = new EventEmitter<ButtonData<any>>();

    private leaveTimeoutHandle?: number;

    constructor(public stateService: MathGraphToolbarBtnStateService) {}

    get isAllow(): boolean {
        return this.allowCheck(this.data.name);
    }

    get hasAllowedChildren(): boolean {
        return this.childrenNames.some(name => this.allowCheck(name));
    }

    get isActivated(): boolean {
        return this.isToolActive(this.data.name, this.data.param) || this.hasActivatedChildBtn;
    }

    get hasActivatedChildBtn(): boolean {
        return !!this.data.children?.some(btn => this.isToolActive(btn.name, btn.param));
    }

    get childrenNames(): any[] {
        return this.data.children?.map(btn => btn.name) ?? [];
    }

    get hasChildren(): boolean {
        return this.data.children?.length > 0;
    }

    get btnPath(): string {
        return `${this.parentPath}.${this.data.name}`;
    }

    get isRootBtn(): boolean {
        return !this.parentPath;
    }

    private lastTouchTime = 0;

    onBtnClick(event: PointerEvent) {
        if (event.pointerType === 'touch' && this.hasChildren) {
            // if has children, require double tap to perform click
            const now = performance.now();
            if (now - this.lastTouchTime > 500) {
                this.lastTouchTime = now;
                return;
            }
            this.lastTouchTime = 0;
        }

        this.data.onClick?.(this.data.name);

        if (this.isAllow) {
            this.btnClicked.emit(this.data);
            this.stateService.clearPath();
        }
    }

    /**
     * Propagate the event to parent components
     */
    onChildBtnClicked(btnData: ButtonData<any>) {
        this.data.name = btnData.name;
        this.data.iconClasses = btnData.iconClasses;
        this.data.param = btnData.param;

        this.btnClicked.emit(btnData);
    }

    onBtnMouseLeave(event: PointerEvent, onSubMenu = false) {
        if (onSubMenu || this.isRootBtn) {
            this.clearLastLeaveTimeout();

            this.leaveTimeoutHandle = window.setTimeout(() => {
                this.stateService.leavePath(this.btnPath);
            }, this.stateService.pointerLeaveTimeoutMs(event));
        }
    }

    onBtnMouseEnter(onSubMenu = false) {
        if (onSubMenu || this.isRootBtn) {
            this.clearLastLeaveTimeout();

            this.stateService.enterPath(this.btnPath);
        }
    }

    private clearLastLeaveTimeout() {
        if (this.leaveTimeoutHandle) window.clearTimeout(this.leaveTimeoutHandle);
    }
}
