import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { SettingFieldChangeEmitterData } from './setting-tool.models';
import { RenderPropsCtx } from '../maghtools.models';

@Component({
    selector: 'setting-tool',
    templateUrl: './setting-tool.component.html',
    styleUrls: ['./setting-tool.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolComponent {
    @Input() settings: RenderPropsCtx; // TODO: here, default settings

    @Output() onClose = new EventEmitter<boolean>();
    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    selectedTab: 'doc' | 'defaultStyles' = 'doc';

    lineStyles = ['solid', 'dashed', 'dotted', 'dashed-dotted'];

    onFieldChange(data: SettingFieldChangeEmitterData) {
        switch (data.field) {
            case 'scale':
                this.onChange.emit({
                    field: data.field,
                    value: Number(data.value) / 100,
                });
                break;
            default:
                this.onChange.emit(data);
                break;
        }
    }

    onInputChange(e: Event, field: string) {
        const target = e.target as HTMLInputElement;
        this.onFieldChange({ field, value: target.value });
    }

    onLineChange(type: string) {
        this.onFieldChange({ field: 'lineStyle', value: type });
    }
}
