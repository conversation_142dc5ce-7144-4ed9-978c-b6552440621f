import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
} from '@angular/core';
import { EquationStyle } from '@viclass/editor.magh';
import { Subscription } from 'rxjs';
import { MaghEquationService } from '../../magh.equation.service';
import { SettingFieldChangeEmitterData } from '../../setting-tool/setting-tool.models';

@Component({
    selector: 'lib-equation-settings',
    templateUrl: './equation-settings.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EquationSettingsComponent implements OnDestroy {
    @Input() equationStyle: EquationStyle;

    @Input() colorPalette: string[] = [];

    @Output() onFieldChange = new EventEmitter<SettingFieldChangeEmitterData>();

    protected readonly lineStyles = ['solid', 'dashed', 'dotted', 'dashed-dotted'];

    private subscription: Subscription;

    constructor(
        private cdr: ChangeDetectorRef,
        private maghEquationService: MaghEquationService
    ) {
        this.subscription = this.maghEquationService.equationChanged$.subscribe(() => {
            this.cdr.markForCheck();
        });
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    onLineChange(type: string) {
        this.onFieldChange.emit({ field: 'lineStyle', value: type });
    }
}
