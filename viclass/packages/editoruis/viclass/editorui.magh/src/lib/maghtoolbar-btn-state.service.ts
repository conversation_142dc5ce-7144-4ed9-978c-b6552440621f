import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class MathGraphToolbarBtnStateService {
    /**
     * Keep track of the path that the mouse is hovering over
     */
    public readonly focusPath$ = new BehaviorSubject<string[]>([]);

    enterPath(path: string) {
        // new paths should contains only the parent paths and the newly entered path
        const newPaths = this.focusPath$.value.filter(old => path.startsWith(old) && old !== path);
        newPaths.push(path);
        this.focusPath$.next(newPaths);
    }

    leavePath(path: string) {
        this.focusPath$.next(this.focusPath$.value.filter(p => !p.startsWith(path)));
    }

    clearPath() {
        this.focusPath$.next([]);
    }

    isFocusPath(path: string): Observable<boolean> {
        return this.focusPath$.pipe(map(currentPaths => currentPaths.some(p => p.startsWith(path))));
    }

    pointerLeaveTimeoutMs(event: PointerEvent): number {
        return event.pointerType === 'touch' ? 4000 : event.pointerType === 'pen' ? 2000 : 200;
    }
}
