/** extracted from mathlive build, for the iframe PDF as we don't want to load the full mathlive package there **/
:root {
    --ML__static-fonts: true;
}
.ML__container {
    min-height: auto !important;
    --_hue: var(--hue, 212);
    --_placeholder-color: var(--placeholder-color, hsl(var(--_hue), 40%, 49%));
    --_placeholder-opacity: var(--placeholder-opacity, 0.4);
    --_text-font-family: var(
        --text-font-family,
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        'Segoe UI',
        'Roboto',
        'Oxygen',
        'Ubuntu',
        'Cantarell',
        'Fira Sans',
        'Droid Sans',
        'Helvetica Neue',
        sans-serif
    );
}
.ML__sr-only {
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    clip: rect(0, 0, 0, 0);
    border: 0;
    clip-path: inset(50%);
    white-space: nowrap;
}
.ML__base,
.ML__is-inline {
    display: inline-block;
}
.ML__base {
    border: 0;
    box-sizing: content-box;
    cursor: text;
    font-family: inherit;
    font-style: inherit;
    font-weight: inherit;
    margin: 0;
    outline: 0;
    padding: 0;
    position: relative;
    text-decoration: none;
    vertical-align: baseline;
    visibility: inherit;
    width: min-content;
}
.ML__strut,
.ML__strut--bottom {
    display: inline-block;
    min-height: 0.5em;
}
.ML__small-delim {
    font-family: KaTeX_Main;
}
.ML__text {
    font-family: var(--_text-font-family);
    white-space: pre;
}
.ML__cmr {
    font-family: KaTeX_Main;
    font-style: normal;
}
.ML__mathit {
    font-family: KaTeX_Math;
    font-style: italic;
}
.ML__mathbf {
    font-family: KaTeX_Main;
    font-weight: 700;
}
.ML__mathbfit,
.lcGreek.ML__mathbf {
    font-family: KaTeX_Math;
}
.ML__mathbfit {
    font-style: italic;
    font-weight: 700;
}
.ML__ams,
.ML__bb {
    font-family: KaTeX_AMS;
}
.ML__cal {
    font-family: KaTeX_Caligraphic;
}
.ML__frak {
    font-family: KaTeX_Fraktur;
}
.ML__tt {
    font-family: KaTeX_Typewriter;
}
.ML__script {
    font-family: KaTeX_Script;
}
.ML__sans {
    font-family: KaTeX_SansSerif;
}
.ML__series_el,
.ML__series_ul {
    font-weight: 100;
}
.ML__series_l {
    font-weight: 200;
}
.ML__series_sl {
    font-weight: 300;
}
.ML__series_sb {
    font-weight: 500;
}
.ML__bold {
    font-weight: 700;
}
.ML__series_eb {
    font-weight: 800;
}
.ML__series_ub {
    font-weight: 900;
}
.ML__series_uc {
    font-stretch: ultra-condensed;
}
.ML__series_ec {
    font-stretch: extra-condensed;
}
.ML__series_c {
    font-stretch: condensed;
}
.ML__series_sc {
    font-stretch: semi-condensed;
}
.ML__series_sx {
    font-stretch: semi-expanded;
}
.ML__series_x {
    font-stretch: expanded;
}
.ML__series_ex {
    font-stretch: extra-expanded;
}
.ML__series_ux {
    font-stretch: ultra-expanded;
}
.ML__it {
    font-style: italic;
}
.ML__shape_ol {
    -webkit-text-stroke: 1px #000;
    text-stroke: 1px #000;
    color: transparent;
}
.ML__shape_sc {
    font-variant: small-caps;
}
.ML__shape_sl {
    font-style: oblique;
}
.ML__emph {
    color: #bc2612;
}
.ML__emph .ML__emph {
    color: #0c7f99;
}
.ML__highlight {
    background: #edd1b0;
    color: #007cb2;
}
.ML__center {
    text-align: center;
}
.ML__left {
    text-align: left;
}
.ML__right {
    text-align: right;
}
.ML__label_padding {
    padding: 0 0.5em;
}
.ML__frac-line {
    min-height: 1px;
    width: 100%;
}
.ML__frac-line:after {
    background: currentColor;
    box-sizing: content-box;
    content: '';
    display: block;
    margin-top: max(-1px, -0.04em);
    min-height: max(1px, 0.04em);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transform: translate(0);
}
.ML__sqrt,
.ML__sqrt-sign {
    display: inline-block;
}
.ML__sqrt-sign {
    position: relative;
}
.ML__sqrt-line {
    display: inline-block;
    height: max(1px, 0.04em);
    width: 100%;
}
.ML__sqrt-line:before {
    background: currentColor;
    content: '';
    display: block;
    margin-top: min(-1px, -0.04em);
    min-height: max(1px, 0.04em);
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transform: translate(0);
}
.ML__sqrt-line:after {
    border-bottom-width: 1px;
    content: ' ';
    display: block;
    margin-top: -0.1em;
}
.ML__sqrt-index {
    margin-left: 0.27777778em;
    margin-right: -0.55555556em;
}
.ML__delim-size1 {
    font-family: KaTeX_Size1;
}
.ML__delim-size2 {
    font-family: KaTeX_Size2;
}
.ML__delim-size3 {
    font-family: KaTeX_Size3;
}
.ML__delim-size4 {
    font-family: KaTeX_Size4;
}
.ML__delim-mult .delim-size1 > span {
    font-family: KaTeX_Size1;
}
.ML__delim-mult .delim-size4 > span {
    font-family: KaTeX_Size4;
}
.ML__accent-body > span {
    font-family: KaTeX_Main;
    width: 0;
}
.ML__accent-vec {
    left: 0.24em;
    position: relative;
}
.ML__latex {
    direction: ltr;
    display: inline-block;
    font-family: inherit;
    font-size-adjust: none;
    font-stretch: normal;
    font-style: normal;
    font-variant-caps: normal;
    letter-spacing: normal;
    line-height: 1.2;
    text-align: left;
    text-indent: 0;
    text-rendering: auto;
    word-wrap: normal;
    text-shadow: none;
    -webkit-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: min-content;
    word-spacing: normal;
}
.ML__latex .style-wrap {
    position: relative;
}
.ML__latex .ML__left-right,
.ML__latex .ML__mfrac {
    display: inline-block;
}
.ML__latex .ML__vlist-t {
    border-collapse: collapse;
    display: inline-table;
    table-layout: fixed;
}
.ML__latex .ML__vlist-r {
    display: table-row;
}
.ML__latex .ML__vlist {
    display: table-cell;
    position: relative;
    vertical-align: bottom;
}
.ML__latex .ML__vlist > span {
    display: block;
    height: 0;
    position: relative;
}
.ML__latex .ML__vlist > span > span {
    display: inline-block;
}
.ML__latex .ML__vlist > span > .ML__pstrut {
    overflow: hidden;
    width: 0;
}
.ML__latex .ML__vlist-t2 {
    margin-right: -2px;
}
.ML__latex .ML__vlist-s {
    display: table-cell;
    font-size: 1px;
    min-width: 2px;
    vertical-align: bottom;
    width: 2px;
}
.ML__latex .ML__msubsup {
    text-align: left;
}
.ML__latex .ML__negativethinspace {
    display: inline-block;
    height: 0.71em;
    margin-left: -0.16667em;
}
.ML__latex .ML__thinspace {
    display: inline-block;
    height: 0.71em;
    width: 0.16667em;
}
.ML__latex .ML__mediumspace {
    display: inline-block;
    height: 0.71em;
    width: 0.22222em;
}
.ML__latex .ML__thickspace {
    display: inline-block;
    height: 0.71em;
    width: 0.27778em;
}
.ML__latex .ML__enspace {
    display: inline-block;
    height: 0.71em;
    width: 0.5em;
}
.ML__latex .ML__quad {
    display: inline-block;
    height: 0.71em;
    width: 1em;
}
.ML__latex .ML__qquad {
    display: inline-block;
    height: 0.71em;
    width: 2em;
}
.ML__latex .ML__llap,
.ML__latex .ML__rlap {
    display: inline-block;
    position: relative;
    width: 0;
}
.ML__latex .ML__llap > .ML__inner,
.ML__latex .ML__rlap > .ML__inner {
    position: absolute;
}
.ML__latex .ML__llap > .ML__fix,
.ML__latex .ML__rlap > .ML__fix {
    display: inline-block;
}
.ML__latex .ML__llap > .ML__inner {
    right: 0;
}
.ML__latex .ML__rlap > .ML__inner {
    left: 0;
}
.ML__latex .ML__rule {
    border: 0 solid;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
}
.ML__latex .overline .overline-line,
.ML__latex .underline .underline-line {
    width: 100%;
}
.ML__latex .overline .overline-line:before,
.ML__latex .underline .underline-line:before {
    border-bottom-style: solid;
    border-bottom-width: max(1px, 0.04em);
    content: '';
    display: block;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}
.ML__latex .overline .overline-line:after,
.ML__latex .underline .underline-line:after {
    border-bottom-style: solid;
    border-bottom-width: max(1px, 0.04em);
    content: '';
    display: block;
    margin-top: -1px;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}
.ML__latex .ML__stretchy {
    display: block;
    left: 0;
    overflow: hidden;
    position: absolute;
    width: 100%;
}
.ML__latex .ML__stretchy:after,
.ML__latex .ML__stretchy:before {
    content: '';
}
.ML__latex .ML__stretchy svg {
    display: block;
    height: inherit;
    position: absolute;
    width: 100%;
    fill: currentColor;
    stroke: currentColor;
    fill-rule: nonzero;
    fill-opacity: 1;
    stroke-width: 1;
    stroke-linecap: butt;
    stroke-linejoin: miter;
    stroke-miterlimit: 4;
    stroke-dasharray: none;
    stroke-dashoffset: 0;
    stroke-opacity: 1;
}
.ML__latex .slice-1-of-2 {
    left: 0;
}
.ML__latex .slice-1-of-2,
.ML__latex .slice-2-of-2 {
    display: inline-flex;
    overflow: hidden;
    position: absolute;
    width: 50.2%;
}
.ML__latex .slice-2-of-2 {
    right: 0;
}
.ML__latex .slice-1-of-3 {
    left: 0;
    width: 25.1%;
}
.ML__latex .slice-1-of-3,
.ML__latex .slice-2-of-3 {
    display: inline-flex;
    overflow: hidden;
    position: absolute;
}
.ML__latex .slice-2-of-3 {
    left: 25%;
    width: 50%;
}
.ML__latex .slice-3-of-3 {
    right: 0;
    width: 25.1%;
}
.ML__latex .slice-1-of-1,
.ML__latex .slice-3-of-3 {
    display: inline-flex;
    overflow: hidden;
    position: absolute;
}
.ML__latex .slice-1-of-1 {
    left: 0;
    width: 100%;
}
.ML__latex .ML__nulldelimiter,
.ML__latex .ML__op-group {
    display: inline-block;
}
.ML__latex .ML__op-symbol {
    position: relative;
}
.ML__latex .ML__op-symbol.ML__small-op {
    font-family: KaTeX_Size1;
}
.ML__latex .ML__op-symbol.ML__large-op {
    font-family: KaTeX_Size2;
}
.ML__latex .ML__mtable .ML__vertical-separator {
    box-sizing: border-box;
    display: inline-block;
    min-width: 1px;
}
.ML__latex .ML__mtable .ML__arraycolsep {
    display: inline-block;
}
.ML__latex .ML__mtable .col-align-m > .ML__vlist-t {
    text-align: center;
}
.ML__latex .ML__mtable .col-align-c > .ML__vlist-t {
    text-align: center;
}
.ML__latex .ML__mtable .col-align-l > .ML__vlist-t {
    text-align: left;
}
.ML__latex .ML__mtable .col-align-r > .ML__vlist-t {
    text-align: right;
}
[data-href] {
    cursor: pointer;
}
.ML__error {
    background-color: rgba(204, 0, 65, 0.1);
    background-image: radial-gradient(ellipse at center, #cc0041, transparent 70%);
    background-position: 0 100%;
    background-repeat: repeat-x;
    background-size: 3px 3px;
    display: inline-block;
    padding-bottom: 3px;
}
.ML__error > .ML__error {
    background: transparent;
    padding: 0;
}
.ML__placeholder {
    color: var(--_placeholder-color);
    font-family:
        system-ui,
        -apple-system,
        BlinkMacSystemFont,
        Segoe UI,
        Roboto,
        Oxygen,
        Ubuntu,
        Cantarell,
        Fira Sans,
        Droid Sans,
        Helvetica Neue,
        sans-serif;
    opacity: var(--_placeholder-opacity);
    padding-left: 0.4ex;
    padding-right: 0.4ex;
}
.ML__notation {
    box-sizing: border-box;
    line-height: 0;
    position: absolute;
}
.ML__tooltip-container {
    position: relative;
    transform: scale(0);
}
.ML__tooltip-container .ML__tooltip-content {
    background: var(--tooltip-background-color);
    border: var(--tooltip-border);
    border-radius: var(--tooltip-border-radius);
    display: inline-table;
    max-width: 400px;
    padding: 12px;
    position: fixed;
    visibility: hidden;
    width: max-content;
    z-index: 2;
    --_selection-color: var(--tooltip-color);
    box-shadow: var(--tooltip-box-shadow);
    color: var(--tooltip-color);
    opacity: 0;
    transition: opacity 0.15s cubic-bezier(0.4, 0, 1, 1);
}
.ML__tooltip-container .ML__tooltip-content .ML__text {
    white-space: normal;
}
.ML__tooltip-container .ML__tooltip-content .ML__base {
    display: contents;
}
.ML__tooltip-container:hover .ML__tooltip-content {
    font-size: 0.75em;
    opacity: 1;
    transform: scale(1) translateY(3em);
    visibility: visible;
}
