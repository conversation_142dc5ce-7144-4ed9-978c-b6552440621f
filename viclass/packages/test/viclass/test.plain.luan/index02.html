<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <title>@viclass/testEmbed</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <script src="https://devlocal.viclass.vn/modules/ww/vi.docloader.js" type="text/javascript"></script>
        <script src="./index02.js" type="text/javascript"></script>
    </head>
    <body>
        <h1>Example of loading document 01</h1>
        <div
            class="vi-embed-viewport"
            data-vi-show-tool="true"
            data-vi-vp-id="vpid01"
            data-vi-vp-type="board"
            data-vi-tools="pan"
            style="width: 900px; height: 500px; position: relative">
            <div class="vi-embed-doc" data-vi-doc-id="66c4618d1830c80ab883eab1" data-vi-ed-type="GeometryEditor"></div>
            <div
                class="vi-embed-doc"
                data-vi-doc-id="66c465a60c0e6b6fbcb37d28"
                data-vi-ed-type="FreeDrawingEditor">
            </div>
        </div>
        <h1>Example of loading document 02</h1>
        <div
            class="vi-embed-viewport-2"
            data-vi-show-tool="true"
            data-vi-vp-id="vpid02"
            data-vi-vp-type="board"
            data-vi-tools="zoom"
            data-vi-tool-v-align="bottom"
            data-vi-tool-h-align="left"
            data-vi-tool-direction="ttb"
            style="width: 900px; height: 500px; position: relative">
            <div class="vi-embed-doc" data-vi-doc-id="66c4618d1830c80ab883eab1" data-vi-ed-type="GeometryEditor"></div>
            <div
                class="vi-embed-doc"
                data-vi-doc-id="66c465a60c0e6b6fbcb37d28"
                data-vi-ed-type="FreeDrawingEditor">
            </div>
        </div>
        <div
            class="vi-embed-viewport-3"
            data-vi-show-tool="true"
            data-vi-vp-id="vpid03"
            data-vi-vp-type="board"
            data-vi-tool-v-align="bottom"
            data-vi-tool-h-align="right"
            data-vi-tool-direction="ltr"
            style="width: 900px; height: 500px; position: relative">
            <div class="vi-embed-doc" data-vi-doc-id="66c4618d1830c80ab883eab1" data-vi-ed-type="GeometryEditor"></div>
            <div
                class="vi-embed-doc"
                data-vi-doc-id="66c465a60c0e6b6fbcb37d28"
                data-vi-ed-type="FreeDrawingEditor">
            </div>
        </div>
        <div
            class="vi-embed-viewport"
            data-vi-show-tool="true"
            data-vi-vp-id="vpid04"
            data-vi-vp-type="board"
            data-vi-doc-id="66c4618d1830c80ab883eab1"
            data-vi-ed-type="GeometryEditor"
            style="width: 900px; height: 500px; position: relative">
            <div class="vi-embed-doc" data-vi-doc-id="66c4618d1830c80ab883eab1" data-vi-ed-type="GeometryEditor"></div>
            <div
                class="vi-embed-doc"
                data-vi-doc-id="66c465a60c0e6b6fbcb37d28"
                data-vi-ed-type="FreeDrawingEditor">
            </div>
        </div>
        <div
            class="vi-embed-viewport-5"
            data-vi-show-tool="true"
            data-vi-vp-id="vpid05"
            data-vi-vp-type="board"
            data-vi-doc-id="66c4618d1830c80ab883eab1"
            data-vi-ed-type="GeometryEditor"
            style="width: 900px; height: 500px; position: relative">
        </div>

        <script type="text/javascript">
            async function performLoad() {
                // manager = new UIManager();
                // manager.initialize();
                await viclass.ViDocLoader.initialize();
                await viclass.ViDocLoader.renderAll({
                    vpSelector: '.vi-embed-viewport-2',
                });
                await viclass.ViDocLoader.renderAll();
                const el = document.getElementsByClassName('vi-embed-viewport-3')[0];
                await viclass.ViDocLoader.renderEl(el);
                const el5 = document.getElementsByClassName('vi-embed-viewport-5')[0];
                await viclass.ViDocLoader.renderEl(el5);
            }
            performLoad();
        </script>
    </body>
</html>
