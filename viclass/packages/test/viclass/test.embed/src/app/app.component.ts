/// <reference types="@viclass/ww/typings" />

import { AfterViewInit, Component, ElementRef, ViewChild } from '@angular/core';
import { NgElement } from '@angular/elements';
import { environment } from '../environments/environment';
import { DocLoaderCoordinator } from '@viclass/editor.coordinator/docloader';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.sass'],
})
export class AppComponent implements AfterViewInit {
    title = '@viclass/test.embed';

    @ViewChild('freedrawingDoc01')
    f01: ElementRef<HTMLDivElement>;
    private coordinator: DocLoaderCoordinator;

    async ngAfterViewInit() {
        await this.loadUI();
    }

    isOpen = false;

    test() {
        this.isOpen = !this.isOpen;
    }

    async loadUI() {
        /**
         * In actual reality, when user generate embed code, the system will generate
         * this config automatically.
         */
        let config = environment.docLoader;

        let docLoaderCreator = viclass.DocLoader.createLoader;

        // // get an instance of the coordinator
        this.coordinator = await docLoaderCreator(config);

        await this.coordinator.initialize();
        await this.coordinator.start();

        // setTimeout(() => coordinator.loadDoc(this.f01.nativeElement, {
        //     bType: "board",
        //     edType: "FreeDrawingEditor",
        //     gId: "638ef30a0d06b9634bf10a71"
        //   })
        // )

        setTimeout(() => {
            let ui = document.getElementById('ui') as NgElement;
            this.initializeUI(ui);

            let frdUI = document.getElementById('uiFrd');
            this.loadFrdUi(frdUI);

            viclass.mfeRT
                .loadRemoteModule({
                    type: 'module',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.loader.webcomp.js',
                    exposedModule: './editorui.loader.webcomp',
                })
                .then(() => {});
        });
    }

    initializeUI(ui) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;

            if (ed.eventType == 'all-ui-loaded') {
                compRef.switchTo('FreeDrawingEditor');
            } else if (ed.eventType == 'ui-loaded') {
                let editor = this.coordinator.editorByType(ed.state);
                if (editor) compRef.getUI(ed.state).connectToolbar(editor.toolBar);
            } else if (ed.eventType == 'loader-initialized') {
                if (ed.eventType === 'all-ui-loaded') {
                    // compRef.switchTo("FreeDrawingEditor")
                } else if (ed.eventType === 'ui-loaded') {
                    // let editor = this.coordinator.editorByType(ed.state)
                    // compRef.getUI(ed.state).connectToolbar(editor.toolBar)
                } else if (ed.eventType === 'loader-initialized') {
                    compRef = ed.source;

                    compRef.loadBaseTheme({
                        remoteName: 'editorui.theme',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                        exposedModule: './editorui.theme',
                    });
                }
            }
        });
    }

    loadFrdUi(ui) {
        let compRef = undefined;
        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;
            if (ed.eventType === 'all-ui-loaded') {
                compRef.switchTo('FreeDrawingEditor');
            } else if (ed.eventType === 'ui-loaded') {
                let editor = this.coordinator.editorByType('FreeDrawingEditor');
                compRef.getUI(ed.state).connectToolbar(editor.toolBar);
                compRef.getUI(ed.state).showUI();
            } else if (ed.eventType === 'loader-initialized') {
                compRef = ed.source;
                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }

            ui.setAttribute(
                'lookups',
                JSON.stringify([
                    {
                        editorType: 'FreedrawingToolsBar',
                        uiImpl: {
                            remoteName: 'editorui.freedrawing',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.freedrawing.js',
                            exposedModule: './editorui.freedrawing',
                        },
                        style: {
                            remoteName: 'editorui.commontools.style',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.freedrawing.style.js',
                            exposedModule: './editorui.freedrawing.style',
                        },
                    },
                ])
            );
        });
    }
}
