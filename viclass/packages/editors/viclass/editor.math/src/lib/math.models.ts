import { DocFocusedES, DocLocalContent, FocusDocEvent } from '@viclass/editor.core';
import { MathDocCtrl } from './docs/math.doc.ctrl';

export type MathDocFocusedES = DocFocusedES<MathDocCtrl>;

export type MathDocEvent = FocusDocEvent<MathDocCtrl>;

export interface MathToolDocListener {
    onDocAttached(docCtrl?: MathDocCtrl): void;
    onDocDetached(docCtrl?: MathDocCtrl): void;
}

export type MathDocInitData = {
    content: string;
};

export interface MathDocLocalContent extends DocLocalContent {
    content: string;
}
