import { InterpolationHandler } from '@viclass/editor.coordinator/common';
import {
    AbstractCommand,
    BoundaryRectangle,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    DOMElementLayerCtrl,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    ViewportManager,
} from '@viclass/editor.core';
import { MathCmdTypeProto } from '@viclass/proto/editor.math';
import { DocInfo, FCCmdTypeProto } from '@viclass/proto/feature.common';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { MathDocInitData } from '../math.models';
import { mathDocReg, mathLayerReg } from '../math.utils';
import { MathDoc, MathLayer } from '../model';
import { UpdateContentCmd } from './math.cmd';

type UpdateBoundaryData = {
    id: DocInfo;
    b: BoundaryRectangle;
}[];

export class MathCmdProcessor extends CmdProcessor {
    private readonly updateBoundaryInterpolationHandler: InterpolationHandler<UpdateBoundaryData> =
        new InterpolationHandler<UpdateBoundaryData>();

    constructor(private editor: MathEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<MathCmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<MathCmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                console.log('reload math doc');
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }
            case MathCmdTypeProto.UPDATE_CONTENT: {
                this.processUpdateContentCmd(cmd as UpdateContentCmd);
                break;
            }
            default:
                break;
        }

        return cmd;
    }

    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        const result = this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return result;
    }

    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const initData = cmd.state.getInitdata() as Uint8Array;
        const decoder = new TextDecoder();
        const initObj: MathDocInitData = JSON.parse(decoder.decode(initData));

        if (cmd.meta.origin == CmdOriginType.remote && !cmd.state.getGlobalId())
            throw new Error('State data for new math document is not complete!');

        const doc = new MathDoc(cmd.meta.targetId, cmd.state.getGlobalId(), initObj.content);

        this.editor.insertDocDelegator.insertDocCtrl(cmd.meta.viewport, doc);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<MathDocCtrl>(mathDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);

        const layerState = new MathLayer(layerId, docId, fcConvertProtoToBoundary(cmd.state.getBoundary()));
        layerState.zindex = zindex;

        const layerCtrl = docCtrl.viewport.requestLayer(DOMElementLayerCtrl, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docCtrl.state.globalId,
            viewport: docCtrl.viewport,
            editor: this.editor,
            state: layerState,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);
        docCtrl.initializeLayerContent();

        const layerRegistry = this.editor.regMan.registry<DOMElementLayerCtrl>(
            mathLayerReg(cmd.meta.viewport.id, docId)
        );
        layerRegistry.addEntity(layerId, layerCtrl);

        this.editor.insertDocDelegator.notifyDocCreation(vm, docId);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.insertDocDelegator.notifyDocCreation(cmd.meta.viewport, ...cmd.state.getLocalidList());

        return cmd;
    }

    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        this.editor.boundaryDelegator.processUpdateBoundaryCmd(cmd, MathEditor.cmdChannelThrottle);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return cmd;
    }

    private processUpdateContentCmd(cmd: UpdateContentCmd) {
        const localId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;

        const docRegistry = this.editor.regMan.registry<MathDocCtrl>(mathDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(localId);

        if (cmd.state.getVersion() > docCtrl.state.version) {
            // console.log('Update CMD v' + cmd.state.getVersion(), cmd.state.getLatex());

            docCtrl.state.version = cmd.state.getVersion();
            docCtrl.setContent(cmd.state.getLatex());
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    isLocalCmd(cmd: AbstractCommand<any>) {
        return cmd.meta.origin === CmdOriginType.local;
    }

    private clearCurrentViewportHistoryIfRemoteCmd(meta: CmdMeta) {
        if (meta.origin === CmdOriginType.remote) this.editor.clearHistory(meta.viewport.id);
    }
}
