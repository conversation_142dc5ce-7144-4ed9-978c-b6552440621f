import { LatexGroup } from './latex.model';

export const MATRIX_GROUP: LatexGroup = {
    type: 'group',
    label: 'Ma trận',
    icon: 'group_structure-matrix',
    children: [
        {
            type: 'sub-group',
            children: [
                {
                    type: 'item',
                    latex: '\\begin{pmatrix} $MATRIX$ \\end{pmatrix}',
                    icon: 'parentheses-symbols',
                    label: {
                        vi: 'Ma trận',
                    },
                },
                {
                    type: 'item',
                    latex: '\\begin{bmatrix} $MATRIX$ \\end{bmatrix}',
                    icon: 'square-brackets',
                    label: {
                        vi: 'Ma trận',
                    },
                },
                {
                    type: 'item',
                    latex: '\\begin{Bmatrix} $MATRIX$ \\end{Bmatrix}',
                    icon: 'braces',
                    label: {
                        vi: 'Ma trận',
                    },
                },
                {
                    type: 'item',
                    latex: '\\begin{vmatrix} $MATRIX$ \\end{vmatrix}',
                    icon: 'vertical-bar',
                    label: {
                        vi: 'Ma trận',
                    },
                },
                {
                    type: 'item',
                    latex: '\\begin{Vmatrix} $MATRIX$ \\end{Vmatrix}',
                    icon: 'double-pipe-character',
                    label: {
                        vi: 'Ma trận',
                    },
                },
                {
                    type: 'item',
                    latex: '\\begin{matrix} $MATRIX$ \\end{matrix}',
                    icon: 'bracket_none',
                    label: {
                        vi: 'Ma trận',
                    },
                },
            ],
        },
    ],
};

export function createMatrixLatex(template: string, rows: number, cols: number): string {
    rows = Math.max(rows, 1);
    cols = Math.max(cols, 1);

    let placeholders = '';

    for (let r = 0; r < rows; r++) {
        for (let c = 0; c < cols; c++) {
            if (c > 0) {
                placeholders += '& ';
            }

            placeholders += r === 0 && c === 0 ? '#@ ' : '#? ';

            if (c === cols - 1 && r !== rows - 1) {
                placeholders += '\\\\ ';
            }
        }
    }

    return template.replace('$MATRIX$', placeholders);
}
