import { LatexGroup } from './latex.model';

export const LOG_GROUP: LatexGroup = {
    type: 'group',
    label: 'Hàm Log',
    icon: 'group_log',
    children: [
        {
            type: 'sub-group',
            children: [
                {
                    type: 'item',
                    latex: '\\exp',
                    icon: 'exponential',
                    label: {
                        en: 'exponential',
                    },
                },
                {
                    type: 'item',
                    latex: '\\log',
                    icon: 'logarit',
                    label: {
                        vi: 'Logarit thập phân',
                    },
                },
                {
                    type: 'item',
                    latex: '\\lg',
                    icon: 'logarit-thap-phan',
                    label: {
                        vi: 'Logarit thập phân',
                    },
                },
                {
                    type: 'item',
                    latex: '\\ln',
                    icon: 'logarit-tu-nhien',
                    label: {
                        vi: 'Logarit tự nhiên',
                    },
                },
                {
                    type: 'item',
                    latex: '\\log_{e}',
                    icon: 'log-e',
                    label: {
                        en: 'Log e',
                    },
                },
                {
                    type: 'item',
                    latex: '\\log_{10}',
                    icon: 'log-10',
                    label: {
                        en: 'Log 10',
                    },
                },
            ],
        },
    ],
};
