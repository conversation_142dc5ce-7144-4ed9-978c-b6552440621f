/***
 * !!! NOTE: use these syntax when define the LaTeX:
 * - the latex symbol often start with '\' character -> need to escape it like: LaTex \sum -> Js: "\\sum"
 * - #@: placeholder that will be replaced by the current selection (if not then it will behavior as #?)
 * - #?: other placeholder
 */
export type LatexType = 'item' | 'group' | 'sub-group';

export type LatexBase<T extends LatexType> = {
    type: T;
};

export type LatexItem = LatexBase<'item'> & {
    latex: string;
    icon: string;
    label?: {
        en?: string;
        vi?: string;
    };
};

export type LatexSubGroup = LatexBase<'sub-group'> & {
    children: LatexItem[];
};

export type LatexGroup = LatexBase<'group'> & {
    label?: string;
    icon?: string;
    children: LatexSubGroup[];
};
