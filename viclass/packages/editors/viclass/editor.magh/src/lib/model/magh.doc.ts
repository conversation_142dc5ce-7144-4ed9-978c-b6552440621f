import {
    BoundaryRectangle,
    DocLocalId,
    DocumentId,
    KeyboardEventData,
    LayerId,
    MouseEventData,
    NativeEventTarget,
    VDoc,
    VDocLayer,
} from '@viclass/editor.core';
import { SerializedEquation } from '../doceditor';
import { MaghDocRenderProp } from './gateway.models';

export type MathGraphMouseEvent = MouseEventData<NativeEventTarget<any>>;
export type MathGraphKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;

export class MathGraphDoc implements VDoc {
    layer: MathGraphLayer;
    version = 0;

    constructor(
        public id: DocLocalId,
        public globalId: DocumentId,
        public equations: SerializedEquation[],
        public docRenderProp: MaghDocRenderProp,
        version?: number
    ) {
        if (version) this.version = version;
    }

    getLayers(): VDocLayer[] {
        return [this.layer as VDocLayer];
    }

    setLayers(layers: VDocLayer[]) {
        this.layer = layers[0] as MathGraphLayer;
    }

    addLayer(layer: VDocLayer) {
        this.layer = layer as MathGraphLayer;
    }
}

export class MathGraphLayer implements VDocLayer {
    zindex: number;

    constructor(
        public id: LayerId,
        public docId: DocLocalId,
        public boundary?: BoundaryRectangle
    ) {}
    getBoundary(): BoundaryRectangle {
        return this.boundary;
    }
    getZindex() {
        return this.zindex;
    }
}
