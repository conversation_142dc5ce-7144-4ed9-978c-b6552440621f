import { Kern<PERSON> } from '../kernel/Kernel';
import { DoubleUtil } from '../utils/DoubleUtil';
import { SegmentType } from './SegmentType';

/**
 * Point representing part of a segment
 */
export class MyPoint {
    public constructor(
        public x: number,
        public y: number,
        public segmentType: SegmentType = SegmentType.LINE_TO
    ) {}

    getX(): number {
        return this.x;
    }

    getY(): number {
        return this.y;
    }

    public isEqual(other: MyPoint): boolean {
        return (
            DoubleUtil.isEqual(this.x, other.x, Kernel.MIN_PRECISION) &&
            DoubleUtil.isEqual(this.y, other.y, Kernel.MIN_PRECISION)
        );
    }

    /**
     * @return lineTo flag
     */
    public getLineTo = (): boolean => {
        return this.segmentType === SegmentType.LINE_TO;
    };

    /**
     *
     * @return true if coords are finite numbers
     */
    public isFinite = (): boolean => {
        return Number.isFinite(this.x) && Number.isFinite(this.y);
    };

    /**
     *
     * @param t
     *            parameter
     * @param point2
     *            second point
     * @return (1-t) * this + t * point2
     */
    public barycenter = (t: number, point2: MyPoint): MyPoint => {
        return new MyPoint((1 - t) * this.x + t * point2.x, (1 - t) * this.y + t * point2.y, SegmentType.MOVE_TO);
    };

    /**
     * Change to lineto /moveto point
     *
     * @param lineTo
     *            whether this should be lineto point
     */
    public setLineTo = (lineTo: boolean): void => {
        this.segmentType = lineTo ? SegmentType.LINE_TO : SegmentType.MOVE_TO;
    };

    /**
     * Reuses the segmentType field (active iff LINE_TO)
     * @return whether it is an active point (for TSP solver)
     */
    public isActive = (): boolean => {
        return this.segmentType === SegmentType.LINE_TO;
    };

    /**
     * Reuses the segmentType field (for TSP solver)
     * @param active if true, set segmentType to LINE_TO, otherwise to MOVE_TO
     */
    public setActive = (active: boolean): void => {
        this.segmentType = active ? SegmentType.LINE_TO : SegmentType.MOVE_TO;
    };

    /**
     * @return segment type
     */
    public getSegmentType = (): SegmentType => {
        return this.segmentType;
    };

    /**
     * @return copy of this point
     */
    public copy = (): MyPoint => {
        return new MyPoint(this.x, this.y, this.segmentType);
    };

    /**
     * @return whether coordinates are not NaN (checks only x)
     */
    public isDefined = (): boolean => {
        return Number.isFinite(this.x);
    };

    /**
     * @param returnType new type
     * @return copy with given type
     */
    public withType = (returnType: SegmentType): MyPoint => {
        return new MyPoint(this.x, this.y, returnType);
    };
}
