import { CurvePlotterStackItem } from './CurvePlotterStackItem';

/**
 * Simple stack class for already evaluated values of the curve.
 */
export class CurvePlotterStack {
    private readonly items: CurvePlotterStackItem[];
    private top: number;

    /**
     * Constructor
     *
     * @param length of the stack
     * @param onScreen if first item on screen
     * @param evalNums first evaluation
     */
    public constructor(length: number, onScreen: boolean, evalNums: number[]) {
        this.items = new Array<CurvePlotterStackItem>(length);
        for (let i: number = 0; i < length; i++) {
            this.items[i] = new CurvePlotterStackItem();
        }
        this.items[0].set(1, 0, onScreen, [...evalNums]);
        this.top = 1;
    }

    /**
     * Push an element to stack, consisting the following info:
     *
     * @param dyadic t of f(t)
     * @param depth of the bisection.
     * @param onScreen if the evaluated value on screen
     * @param evalNums f(t)
     */
    public push = (dyadic: number, depth: number, onScreen: boolean, evalNums: number[]): void => {
        this.items[this.top].set(dyadic, depth, onScreen, evalNums);
        this.top++;
    };

    /**
     * Pops item from the top.
     *
     * @returns top of the item.
     */
    public pop = (): CurvePlotterStackItem | null => {
        this.top--;
        return this.top >= 0 ? this.items[this.top] : null;
    };

    /**
     *
     * @return if the stack have items.
     */
    public hasItems = (): boolean => {
        return this.top !== 0;
    };
}
