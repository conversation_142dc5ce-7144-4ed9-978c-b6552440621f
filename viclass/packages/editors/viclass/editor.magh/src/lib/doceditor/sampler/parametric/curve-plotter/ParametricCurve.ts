import { CurveEvaluable, Equation } from '../../../common';

export class ParametricCurve extends CurveEvaluable {
    constructor(private equation: Equation) {
        super();
    }

    getMinParameter(): number {
        return this.equation.supportInternalScope ? this.equation.internalScope.min : Number.MIN_SAFE_INTEGER;
    }
    getMaxParameter(): number {
        return this.equation.supportInternalScope ? this.equation.internalScope.max : Number.MAX_SAFE_INTEGER;
    }
    newDoubleArray(): number[] {
        return [];
    }
    distanceMax(p1: number[], p2: number[]): number {
        return Math.max(Math.abs(p1[0] - p2[0]), Math.abs(p1[1] - p2[1]));
    }
    evaluateCurve(t: number, out: number[]): void {
        out[0] = this.equation.evaluateLhs({ t: t });
        out[1] = this.equation.evaluateRhs({ t: t });
    }
    getDefinedInterval(a: number, b: number): number[] {
        throw new Error(`Method not implemented. ${a} ${b}`);
    }
    getTrace(): boolean {
        return true;
    }
    isClosedPath(): boolean {
        return false;
    }
    isFunctionInX(): boolean {
        return true;
    }
}
