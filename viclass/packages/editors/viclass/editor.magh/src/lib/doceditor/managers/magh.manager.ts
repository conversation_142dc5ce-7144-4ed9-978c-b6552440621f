import { MathGraphDocCtrl } from '../../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../../magh.editor';
import { MathGraphDocEditor } from '../magh.doc.editor';

export default abstract class MathGraphManager {
    private readonly unsubscribeFns: Array<() => void> = [];

    protected get maghEditor(): MathGraphEditor {
        return this.docEditor.editor;
    }

    protected get docCtrl(): MathGraphDocCtrl {
        return this.docEditor.docCtrl;
    }

    constructor(protected docEditor: MathGraphDocEditor) {}

    abstract init(): void;

    protected addUnsubscribe(fn: () => void): void {
        this.unsubscribeFns.push(fn);
    }

    destroy(): void {
        this.unsubscribeFns.forEach(fn => fn());
    }
}
