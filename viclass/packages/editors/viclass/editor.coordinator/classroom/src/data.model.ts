import { <PERSON><PERSON><PERSON><PERSON><PERSON>ontent, EditorCoordinatorConfig, Position, ViewportState } from '@viclass/editor.core';
import { LineId } from './tools';

export type ClassroomStatus = 'CREATED' | 'OPENED' | 'PAUSED' | 'STOPPED';

export type ResponseError = 'CLASS_NOT_OPENED' | 'PEER_KICKED_OUT' | 'PEER_INVALID';

export interface ClassroomInfo {
    id: string;
    owner: string;
    status: ClassroomStatus;
    defaultCoordState: string;
    presentingCoordState: string;
    presentingUser: string;
    presentingPeer: string;
    pinnedCoordStates: string[];
}

export interface DocLayerInfo {
    id: number;
    channelCode: number;
    docLocalId: number;
    index: number;
    zindex?: number;
    position: number[];
}

export type FitScreen = 'widthFit' | 'heightFit' | 'noneFit' | 'autoFit';

export interface PresenterSetting {
    syncPresenterState: boolean;
    fitScreen: FitScreen;
}

export interface DefaultSetting {
    background: boolean;
    backgroundColor: string;

    shadow: boolean;
    shadowType: string;

    border: boolean;
    borderType: string;
    borderColor: string;
}
export interface PresenterState {
    vpPos: number[];
    vpSize: number[];
    vpZoom: number;
}

export interface AddMarkerPreviewPointState {
    userId: string;
    lineId: LineId;
    point: Position;
    color: string;
    size: number;
    endPoint?: boolean;
}

export interface AddMarkerSegmentState {
    userId: string;
    lineId: LineId;
    points: Position[];
    color: string;
    size: number;
    numPreviewRemoved: number;
}
export interface DocSetting extends DefaultSetting {
    docLocalId: number;
    channelCode: number;
}

export interface ClassRoomCoordinatorState extends ViewportState {
    owner: string;
    title: string;
    /**
     * docMapping map from a combination of (coordinator channel code, local id)
     * to a global id
     */
    docMapping: Map<string, string>;

    /**
     * Layer map from a combination of (coordinator channel code, local id, layer id) to the information
     * required for that layer
     */
    layers: Map<string, DocLayerInfo>;
    default?: boolean;
    presenting?: boolean;
    pinned?: boolean;

    defaultSetting: DefaultSetting;
    presenterState?: PresenterState;
    presenterSetting?: PresenterSetting;
    docSettings?: Map<string, DocSetting>;
}

export interface ClassroomLocalContentState {
    localContents: Map<string, DocLocalContent>;
}

export interface SynchronizationConfig {
    ccsUrl: string;
    roomId: string;
    userId: string; // user id as in the viclass registration
    peerId?: string; // the peer id of this classroom user
    remotePeerId?: string;
}

export interface ClassroomCoordinatorConfig extends EditorCoordinatorConfig {
    syncConf: SynchronizationConfig;
}
