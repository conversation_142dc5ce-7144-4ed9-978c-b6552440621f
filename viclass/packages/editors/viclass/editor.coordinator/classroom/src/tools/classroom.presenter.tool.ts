import { interpolate, InterpolationHandler } from '@viclass/editor.coordinator/common';
import {
    BoardViewportManager,
    PanEventData,
    reliableSaveCmdMeta,
    ToolState,
    VEventListener,
    ZoomEventData,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.core';
import { Subject, Subscription } from 'rxjs';
import { sampleTime } from 'rxjs/operators';
import { ClassroomCoordinator } from '../classroom.coordinator';
import { SyncPresenterCmd } from '../coordinator.state.cmd';
import { FitScreen, PresenterSetting, PresenterState } from '../data.model';
import { ClassroomTool, ClassroomToolType } from './classroom.tool';

export class ClassroomPresenterState implements ToolState {
    presenterState: PresenterState;
    presenterSetting: PresenterSetting;
}

export class ClassroomPresenterTool extends ClassroomTool {
    override toolType: ClassroomToolType = 'presentertool';
    override toolState?: ClassroomPresenterState = {
        presenterState: { vpPos: null, vpZoom: 1, vpSize: null },
        presenterSetting: { syncPresenterState: true, fitScreen: 'noneFit' },
    };
    private readonly viewportChangeListener: VEventListener<ZoomEventData | PanEventData>;
    private readonly syncPresenterStateSource: Subject<PresenterState> = new Subject();
    private syncPresenterStateSourceSub: Subscription;

    constructor(public coord: ClassroomCoordinator) {
        super();

        this.viewportChangeListener = new (class implements VEventListener<ZoomEventData | PanEventData> {
            constructor(private t: ClassroomPresenterTool) {}

            onEvent(eventData: any): any {
                if (this.t.coord.roomInfo.presentingUser !== this.t.coord.userId) return eventData;
                if (this.t.toolbar.viewport.mode === 'EditMode' || this.t.toolbar.viewport.mode === 'InteractiveMode') {
                    this.handleViewportEvent(eventData);
                }
                return eventData;
            }

            handleViewportEvent(eventData: any) {
                switch (eventData.eventType) {
                    case 'viewport-zoom': {
                        const origin = eventData as ZoomEventData;
                        this.t.toolState.presenterState.vpZoom = origin.state.zoomLevel;
                        this.t.toolState.presenterState.vpPos = [origin.state.vpPos.x, origin.state.vpPos.y];
                        break;
                    }
                    case 'viewport-pan': {
                        const origin = eventData as PanEventData;
                        this.t.toolState.presenterState.vpPos = [origin.state.pos.x, origin.state.pos.y];
                        this.t.toolState.presenterState.vpSize = [origin.state.size.width, origin.state.size.height];
                        break;
                    }
                }
                const presenterState = this.t.toolState.presenterState;
                this.t.syncPresenterState({
                    vpPos: [presenterState.vpPos[0], presenterState.vpPos[1]],
                    vpZoom: presenterState.vpZoom,
                    vpSize: [presenterState.vpSize[0], presenterState.vpSize[1]],
                });
            }
        })(this);
    }

    /**
     * Adjust the presenter setting of the tool and apply it to the coordinator and the viewport.
     * @param changedFields the changed fields of the presenter setting.
     * @returns a promise that resolves when the adjustment is done.
     */
    async adjustSettings(changedFields: Record<string, any>): Promise<void> {
        const coordId = this.toolbar.viewport.id;

        const presenterSetting = { ...this.getPresenterSetting() };
        for (const key of Object.keys(changedFields)) presenterSetting[key] = changedFields[key];

        // Update the presenter setting of the coordinator.
        this.coord.setPresenterSetting(coordId, presenterSetting);

        // Update the presenter setting of the tool.
        this.updatePresenterSettingFromCoord();

        // If the user disables sync presenter state, return immediately.
        if (presenterSetting.syncPresenterState === false) return;

        // Update the presenter state of the tool.
        this.updatePresenterStateFromCoord();

        // Apply the presenter state to the viewport.
        this.setPresenterStateForVP(this.getPresenterState(), presenterSetting.fitScreen);
    }

    /**
     * Update the presenter state of the tool from the coordinator state.
     * The presenter state will be updated from the coordinator state,
     * and the presenter state will be applied to the toolbar.
     */
    private updatePresenterStateFromCoord(): void {
        // Get the presenter state from the coordinator.
        const presenterState = this.getPresenterState();

        // Update the presenter state of the tool.
        this.toolState.presenterState = presenterState;

        // Update the presenter state of the toolbar.
        this.toolbar.update('presentertool', {
            ...this.toolState,
        });
    }

    /**
     * Update the presenter setting of the tool from the coordinator state.
     * The presenter setting will be updated from the coordinator state,
     * and the presenter setting will be applied to the toolbar.
     */
    private updatePresenterSettingFromCoord(): void {
        // Get the presenter setting from the coordinator.
        const presenterSetting = this.getPresenterSetting();

        // Update the presenter setting of the tool.
        this.toolState.presenterSetting = presenterSetting;

        // Update the presenter setting of the toolbar.
        this.toolbar.update('presentertool', {
            ...this.toolState,
        });
    }

    private async syncPresenterState(presenterState: PresenterState): Promise<void> {
        this.syncPresenterStateSource.next(presenterState);
    }

    getPresenterSetting(): PresenterSetting {
        return this.coord.getPresenterSetting(this.toolbar.viewport.id);
    }

    getPresenterState(): PresenterState {
        return this.coord.getPresenterState(this.toolbar.viewport.id);
    }

    /**
     * Set the presenter state to the viewport
     * @param presenter the presenter state to be set
     * @param fitScreen the fit screen type, if undefined, the presenter state will be applied as is
     */
    private setPresenterStateForVP(presenter: PresenterState, fitScreen?: FitScreen) {
        const coordId = this.toolbar.viewport.id;
        const vm = this.coord.getViewportManager(coordId);

        let zoomLevel: number | undefined = undefined;

        if (presenter.vpZoom || presenter.vpSize) {
            const width = presenter.vpSize[0] || vm.viewportScreenWidth();
            const height = presenter.vpSize[1] || vm.viewportScreenHeight();
            const fitWidthScale = width / vm.viewportScreenWidth();
            const fitHeightScale = height / vm.viewportScreenHeight();
            zoomLevel = presenter.vpZoom || 1;

            let zoomLevelScale = zoomLevel;
            switch (fitScreen) {
                case 'heightFit':
                    zoomLevelScale = zoomLevelScale * fitHeightScale;
                    break;
                case 'widthFit':
                    zoomLevelScale = zoomLevelScale * fitWidthScale;
                    break;
                case 'autoFit':
                    if (fitWidthScale > fitHeightScale) zoomLevelScale = zoomLevelScale * fitWidthScale;
                    else zoomLevelScale = zoomLevelScale * fitHeightScale;
                    break;
            }

            // If the presenter's screen is smaller than the user's screen, the zoomScale value is not taken.
            if (zoomLevelScale > zoomLevel) zoomLevel = zoomLevelScale;
        }

        // set the current look at of the viewport to the presenter position
        if (presenter.vpPos) vm.centerAt({ x: presenter.vpPos[0], y: presenter.vpPos[1] });
        // zoom the viewport to the calculated zoom level

        if (zoomLevel !== undefined) vm.zoom(zoomLevel);
    }

    private readonly presenterStateInterpolationHandler: InterpolationHandler<PresenterState> =
        new InterpolationHandler<PresenterState>();

    applyPresenterStateFromCmd(cmd: SyncPresenterCmd) {
        const presenter = cmd.state.getPresenter();
        const presenterState = {
            vpPos: [presenter.getVpPos().getX(), presenter.getVpPos().getY()],
            vpZoom: presenter.getVpZoom(),
            vpSize: [presenter.getVpSize().getWidth(), presenter.getVpSize().getHeight()],
        };

        this.presenterStateInterpolationHandler.runInterpolation(
            presenterState,
            (preState: PresenterState, curState: PresenterState, ratio: number) =>
                <PresenterState>{
                    vpPos: [
                        interpolate(preState.vpPos[0], curState.vpPos[0], ratio),
                        interpolate(preState.vpPos[1], curState.vpPos[1], ratio),
                    ],
                    vpSize: [
                        interpolate(preState.vpSize[0], curState.vpSize[0], ratio),
                        interpolate(preState.vpSize[1], curState.vpSize[1], ratio),
                    ],
                    vpZoom: interpolate(preState.vpZoom, curState.vpZoom, ratio),
                },
            (state: PresenterState) => this.applyPresenterState(state),
            () => structuredClone(this.coord.getPresenterState(this.toolbar.viewport.id)),
            ClassroomCoordinator.syncThrottle
        );
    }

    applyPresenterState(presenter: PresenterState) {
        if (this.coord.getClassroomToolbar(this.toolbar.viewport.id).isToolDisable(this.toolType)) return;

        const presenterSetting = this.getPresenterSetting() as PresenterSetting;
        const fitScreen = presenterSetting.fitScreen as FitScreen;

        this.coord.setPresenterState(this.toolbar.viewport.id, presenter);
        this.updatePresenterStateFromCoord();

        // if the presenter is being synced, apply the presenter to the viewport
        if (presenterSetting.syncPresenterState) {
            // calculate the scale of the presenter to the viewport
            this.setPresenterStateForVP(presenter, fitScreen);
        }
    }

    /**
     * Attach the viewport to the presenter tool.
     */
    override onAttachViewport() {
        if (!this.toolbar.viewport) {
            throw new Error('Viewport is not attached to the toolbar');
        }

        const presenterSetting = this.getPresenterSetting();

        if (!presenterSetting) {
            // Set the default presenter setting of the coordinator.
            this.coord.setPresenterSetting(this.toolbar.viewport.id, {
                // Enable sync presenter state by default.
                syncPresenterState: true,

                // Set fit screen to none by default.
                fitScreen: 'noneFit',
            });
        }

        // Update the presenter state of the tool from the coordinator state.
        this.updatePresenterStateFromCoord();

        // Update the presenter setting of the tool from the coordinator state.
        this.updatePresenterSettingFromCoord();

        // Apply the presenter state to the viewport.
        this.setPresenterStateForVP(this.toolState.presenterState);

        // Register the viewport listeners.
        (this.toolbar.viewport as BoardViewportManager)
            .zoomEventEmitter()
            .registerListener(this.viewportChangeListener as VEventListener<ZoomEventData>);

        (this.toolbar.viewport as BoardViewportManager)
            .panEventEmitter()
            .registerListener(this.viewportChangeListener as VEventListener<PanEventData>);

        this.syncPresenterStateSourceSub = this.syncPresenterStateSource
            .pipe(sampleTime(ClassroomCoordinator.syncThrottle))
            .subscribe((presenterState): any => {
                const coordId = this.toolbar.viewport.id;

                const coordState = this.coord.getCoordState(coordId);
                if (!coordState) return;

                coordState.version++;
                const cmd = new SyncPresenterCmd(
                    reliableSaveCmdMeta(
                        this.coord.getVmByState(coordId),
                        coordState.version,
                        0,
                        -1,
                        CmdTypeProto.SYNC_PRESENTER,
                        true
                    )
                );
                cmd.setCoordId(coordId);
                cmd.setPresenter(presenterState);
                return this.coord.presenterCmdChannel.receive(cmd);
            });
    }

    override onDetachViewport() {
        if (this.toolbar.viewport) {
            (this.toolbar.viewport as BoardViewportManager)
                .zoomEventEmitter()
                .unregisterListener(this.viewportChangeListener as VEventListener<ZoomEventData>);

            (this.toolbar.viewport as BoardViewportManager)
                .panEventEmitter()
                .unregisterListener(this.viewportChangeListener as VEventListener<PanEventData>);

            this.syncPresenterStateSourceSub?.unsubscribe();
        }
    }
    override onDisable() {
        super.onDisable();
        this.onDetachViewport();
    }

    override onEnable() {
        super.onDisable();
        this.onAttachViewport();
    }
}
