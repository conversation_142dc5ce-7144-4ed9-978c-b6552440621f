import { <PERSON>rror<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ViErrEventEmitter } from '@viclass/editor.core';

export class CoordErrorHandler extends ViErrorHandler {
    constructor(public override readonly emitter: ViErrEventEmitter) {
        super(emitter);
    }
}

export const coordErrorHandlerEmitter = new ViErrEventEmitter();
export const coordErrorHandler = new CoordErrorHandler(coordErrorHandlerEmitter);
