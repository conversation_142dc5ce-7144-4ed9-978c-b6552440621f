import { AbstractCommand, CmdProcessor } from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { ClassroomCoordinator } from './classroom.coordinator';
import { SyncPresenterCmd } from './coordinator.state.cmd';
import { ClassroomPresenterTool } from './tools';

export class PresenterCmdProccessor extends CmdProcessor {
    constructor(private coord: ClassroomCoordinator) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<CmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<CmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case CmdTypeProto.SYNC_PRESENTER: {
                await this.processSyncPresenterCmd(cmd as SyncPresenterCmd);
                break;
            }
        }

        return cmd;
    }

    private async processSyncPresenterCmd(cmd: SyncPresenterCmd) {
        const coordState = this.coord.getCoordState(cmd.state.getCoordinatorId());
        const coordStateVersion = cmd.meta.sequence;
        if (!coordState || coordStateVersion <= coordState.version) return;
        coordState.version = coordStateVersion;

        const coordId = cmd.state.getCoordinatorId();
        const presenterTool = this.coord
            .getClassroomToolbar(coordId)
            .getTool('presentertool') as ClassroomPresenterTool;

        presenterTool.applyPresenterStateFromCmd(cmd);
    }
}
