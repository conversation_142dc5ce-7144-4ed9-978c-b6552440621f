import { interpolate, InterpolationHandler } from '@viclass/editor.coordinator/common';
import {
    Awareness,
    AwarenessId,
    BaseBoardViewportManager,
    buildOneshotAwarenessCmdOption,
    Cursor,
    defaultCursor,
    Position,
    UserContext,
} from '@viclass/editor.core';
import { BehaviorSubject, combineLatest, of, sampleTime, Subscription, switchMap } from 'rxjs';
import { ClassroomCoordinator } from './classroom.coordinator';
import { ClassroomInfo } from './data.model';
import { PRESENTER_CURSOR } from './handler.attachment.manager';

type MouseAwarenessData = {
    x: number;
    y: number;
    cursors: Cursor[];
    userId: string;
    userAvatarUrl?: string;
};

export class ClassroomPresenterMouseHander {
    // aspmp2c = awareness send presenter mouse position to classmate
    readonly aspmp2cId: AwarenessId = 'aspmp2c';
    private readonly aspmp2cInterpolationHandler = new InterpolationHandler<MouseAwarenessData>();
    private readonly defaultPresenterAvatar = '/static/assets/images/avatar-man.png';

    private aspmp2cSub: Subscription;
    private aspmp2cHandlerSub: Subscription;

    constructor(private readonly coord: ClassroomCoordinator) {}

    start() {
        this.aspmp2cSub = combineLatest({
            vm: this.coord.activeVmObs$,
            roomInfo: this.coord.roomInfoObs$,
        })
            .pipe(
                switchMap(({ vm, roomInfo }) => {
                    const fallbackValue = combineLatest({
                        vm: of(undefined as BaseBoardViewportManager),
                        userCtx: of(undefined as UserContext),
                        position: of(<Position>{ x: 0, y: 0 }),
                        cursors: of([] as Cursor[]),
                    });

                    if (!roomInfo || !vm) return fallbackValue;
                    if (roomInfo.presentingUser !== this.coord.userId) return fallbackValue;

                    const ham = this.coord.hams.get(vm.id);
                    const cursorMonitor = ham?.cursorMonitor;
                    if (!cursorMonitor) return fallbackValue;

                    return combineLatest({
                        vm: of(vm),
                        userCtx: of(this.coord.userCtxGetter?.()),
                        position: cursorMonitor.positionSink$,
                        cursors: cursorMonitor.cursorsSink$,
                    });
                }),
                sampleTime(ClassroomCoordinator.syncThrottle)
            )
            .subscribe(data => {
                if (!data.vm || !data.position || data.userCtx?.userId !== this.coord.userId) return;

                const screenRoot = data.vm.screenRoot();
                this.coord.awarenessFeature.useAwareness(
                    data.vm.id,
                    '',
                    buildOneshotAwarenessCmdOption(this.aspmp2cId, <MouseAwarenessData>{
                        x: (data.position as Position).x * data.vm.zoomLevel + screenRoot.x,
                        y: (data.position as Position).y * data.vm.zoomLevel - screenRoot.y,
                        cursors: data.cursors || defaultCursor,
                    }),
                    undefined,
                    false
                );
            });

        this.aspmp2cHandlerSub = this.coord.activeVmObs$
            .pipe(
                switchMap(vp =>
                    combineLatest({
                        vp: of(vp),
                        roomInfo: this.coord.roomInfoObs$,
                        awareness: (this.coord.awarenessFeature?.getReceivedAwareness(vp?.id) ||
                            of(undefined)) as BehaviorSubject<Awareness[] | undefined>,
                    })
                )
            )
            .subscribe(
                (data: {
                    vp: BaseBoardViewportManager | undefined;
                    roomInfo: ClassroomInfo | undefined;
                    awareness: Awareness[] | undefined;
                }) => {
                    if (!data.roomInfo || !data.vp) return null;

                    const context = this.coord.userCtxGetter?.();
                    if (!context) return null;

                    const ham = this.coord.hams.get(data.vp.id);
                    const cm = ham?.cursorManager;
                    if (!cm) return;

                    // if the current user is the presenter, hide the presenter cursor
                    if (data.roomInfo.presentingUser === this.coord.userId) {
                        if (cm) cm.hideCursor(PRESENTER_CURSOR);
                        return;
                    }

                    const firstMouseAwarness = data.awareness
                        .filter(a => a.options.id === this.aspmp2cId && a.userId !== this.coord.userId)
                        .slice(-1)?.[0];
                    if (!firstMouseAwarness) return null;

                    const firstMouseAwarnessPayload: MouseAwarenessData = {
                        ...firstMouseAwarness.options.payload,
                        userId: context?.presenter?.userId,
                        userAvatarUrl: context?.presenter?.avatarUrl,
                    };

                    const screenRoot = data.vp.screenRoot();
                    const firstMouseAwarenessDataTransformed = <MouseAwarenessData>{
                        ...firstMouseAwarnessPayload,
                        x: (firstMouseAwarnessPayload.x - screenRoot.x) / data.vp.zoomLevel,
                        y: (firstMouseAwarnessPayload.y + screenRoot.y) / data.vp.zoomLevel,
                    };
                    this.aspmp2cInterpolationHandler.runInterpolation(
                        firstMouseAwarenessDataTransformed,
                        (preState, curState, ratio) => ({
                            ...curState,
                            x: interpolate(preState.x, curState.x, ratio),
                            y: interpolate(preState.y, curState.y, ratio),
                        }),
                        state => {
                            if (state.cursors.length) {
                                cm.showCursor(PRESENTER_CURSOR);
                                cm.updateCursor(PRESENTER_CURSOR, state.cursors);
                                cm.updatePosition(PRESENTER_CURSOR, {
                                    x: state.x,
                                    y: state.y,
                                });
                                cm.updateAvatar(
                                    PRESENTER_CURSOR,
                                    state.userAvatarUrl?.length ? state.userAvatarUrl : this.defaultPresenterAvatar
                                );
                            } else cm.hideCursor(PRESENTER_CURSOR);
                        },
                        () => firstMouseAwarenessDataTransformed,
                        ClassroomCoordinator.syncThrottle
                    );
                }
            );
    }

    stop() {
        this.aspmp2cSub?.unsubscribe();
        this.aspmp2cHandlerSub?.unsubscribe();
    }
}
