import { <PERSON><PERSON><PERSON>erCtrl, <PERSON><PERSON><PERSON><PERSON><PERSON>, StickyLayerCtrl, ViewportManager } from '@viclass/editor.core';
import { WrappingBoardViewportManager } from './wrapping.board.vm';

export class NoWrapStickyLayer extends StickyLayerCtrl {
    override nativeEl: HTMLElement | SVGElement;
    override domEl: HTMLElement;

    override attachRenderer<V extends ViewportManager, L extends BaseLayerCtrl>(renderer: LayerRenderer<V, L>) {}

    constructor(
        public override viewport: WrappingBoardViewportManager,
        public override style: object
    ) {
        super();

        this.nativeEl = document.createElement('div');
        this.nativeEl.classList.add('vi-wrapping-div-sticky-layer');
        this.domEl = this.nativeEl;

        Object.assign(this.nativeEl.style, {
            position: 'absolute',
            overflow: 'hidden',
        });

        Object.assign(this.nativeEl.style, style);
    }
}
