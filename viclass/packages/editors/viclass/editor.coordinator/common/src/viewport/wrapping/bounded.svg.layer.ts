import {
    attrsNS,
    BoardViewportManager,
    BoundaryRectangle,
    BoundedSVGLayerCtrl,
    DefaultEventEmitter,
    DocumentEditor,
    extractHeight,
    extractWidth,
    LayerRenderer,
    MouseEventData,
    PanEventData,
    Position,
    PositionLocatable,
    ScreenPosition,
    VDocLayer,
    VEventListener,
    xmlns,
    ZoomEventData,
} from '@viclass/editor.core';

export class NoWrapBoundedSVGLayer extends BoundedSVGLayerCtrl implements PositionLocatable {
    svg: SVGElement;
    nativeEl: HTMLElement | SVGElement;

    private _clientRenderer: LayerRenderer<any, any>;
    viewportChangeListener: VEventListener<any>;

    constructor(
        public override boundary: BoundaryRectangle,
        public override viewport: BoardViewportManager,
        public override state?: VDocLayer,
        public override editor?: DocumentEditor
    ) {
        super();

        this.nativeMouseEventEmitter = new DefaultEventEmitter();
        this.nativePointerEventEmitter = new DefaultEventEmitter();

        this.svg = document.createElementNS(xmlns, 'svg');
        this.nativeEl = this.svg;
        this.nativeEl.classList.add('vi-wrapping-bounded-svg-layer');

        this.viewportChangeListener = this.initViewportChangeListener();

        this.viewport.panEventEmitter().registerListener(this.viewportChangeListener);
        this.viewport.zoomEventEmitter().registerListener(this.viewportChangeListener);

        this.svg.style.position = 'absolute';

        this.setPosition();

        this.nativeMouseEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the mouse events from this
            this.registerMouseEventOnElement(this.svg);
        };

        this.nativeMouseEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterMouseEventsOnElement(this.svg);
        };

        this.nativePointerEventEmitter.onBeforeListenerAdded = l => {
            // when someone wants to listen to the pointer events from this
            this.registerPointerEventOnElement(this.svg);
        };

        this.nativePointerEventEmitter.onAfterListenerRemoved = l => {
            this.unregisterPointerEventsOnElement(this.svg);
        };
    }

    private topLeftScreenPos(boundary: BoundaryRectangle): ScreenPosition {
        let topLeft: Position;

        if (boundary.start && boundary.end)
            topLeft = {
                x: Math.min(boundary.start.x, boundary.end.x),
                y: Math.max(boundary.start.y, boundary.end.y),
            };
        else {
            if (!boundary.width || !boundary.height)
                throw new Error('Boundary provided to a bounded graphic layer must at least have size information');

            topLeft = {
                x: -boundary.width / 2,
                y: boundary.height / 2,
            };

            // initialize the position of the layer
            boundary.start = topLeft;
            boundary.end = { x: boundary.width / 2, y: -boundary.height / 2 };

            // by default, putting it at the middle of the current viewport
        }

        const sp = this.viewport.getScreenPos(topLeft);

        return sp;
    }

    private setPosition(changeSize: boolean = true) {
        const sp = this.topLeftScreenPos(this.boundary);
        const unit = 1 / this.viewport.zoomLevel;
        const w = extractWidth(this.boundary);
        const h = extractHeight(this.boundary);

        attrsNS(this.svg, {
            transform: `translate(${Math.round(sp.x)} ${Math.round(sp.y)}) scale(1 -1)`,
        });

        if (changeSize) {
            const width = `${Math.round(w * unit)}px`;
            const height = `${Math.round(h * unit)}px`;

            const viewbox = `${Math.round(-w / 2)} ${Math.round(-h / 2)} ${w} ${h}`;

            attrsNS(this.svg, {
                width: width,
                height: height,
                viewBox: viewbox,
            });
        }
    }

    updateBoundary(b: BoundaryRectangle) {
        this.boundary = b;
        this.setPosition();
    }

    /**
     * Convert from position in board coordinate to svg coordinate
     * @param pos
     * @returns position local in the svg coordinate
     */
    svgPos(pos: Position): Position {
        const tx = Math.min(this.boundary.start.x, this.boundary.end.x);
        const ty = Math.max(this.boundary.start.y, this.boundary.end.y);
        const w = this.boundary.width;
        const h = this.boundary.height;
        return { x: pos.x - tx - w / 2, y: h / 2 + pos.y - ty }; // because we already scale the canvas of -1 in y direction, we don't need to revert y
    }

    protected initViewportChangeListener(): VEventListener<any> {
        const self = this;
        let requested = false;
        return new (class implements VEventListener<ZoomEventData | PanEventData> {
            onEvent(eventData: ZoomEventData | PanEventData): any {
                if (!requested) {
                    requestAnimationFrame(fNo => {
                        requested = false;
                        self.setPosition(true);
                    });
                    requested = true;
                }

                return eventData;
            }
        })();
    }

    mouseLocation(eventData: MouseEventData<any>): Position {
        const event: MouseEvent = eventData.nativeEvent;
        const boundingRect = this.svg.getBoundingClientRect();

        // position inside the canvas but in screen coordinate system
        const svgScreenPos = {
            x: event.clientX - boundingRect.x,
            y: event.clientY - boundingRect.y,
        };

        const tx = Math.min(this.boundary.start.x, this.boundary.end.x);
        const ty = Math.max(this.boundary.start.y, this.boundary.end.y);

        // because svgScreenPos is the vector from the top left point to the mouse click position, in screen coordinate
        // hence, have to multiply with the zoom level to know the distance in the actual board coordinate system
        return {
            x: tx + svgScreenPos.x * this.viewport.zoomLevel,
            y: ty - svgScreenPos.y * this.viewport.zoomLevel,
        };
    }
}
