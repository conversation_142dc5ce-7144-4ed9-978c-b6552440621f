import { $createTextNode, $isTextNode, LexicalNode, ParagraphNode, TextNode } from 'lexical';

/**
 * Create an empty text token. Which is a text node with mode `token`.
 * The normal TextNode will be auto merge/remove with its siblings but this one is not.
 * Can be useful to place at the end of paragraph to allow cursor select from the end in some edge cases.
 */
export function $createEmptyTextToken(): TextNode {
    return $createTextNode('').setMode('token');
}

/**
 * Check if this node is an empty text token
 */
export function isEmptyTextToken(node: LexicalNode | null | undefined): node is TextNode {
    return node && $isTextNode(node) && node.getMode() === 'token' && node.getTextContent() === '';
}

/**
 * Unwrap the paragraph node and return the children to the parent node
 */
export function $unwrapNode(node: ParagraphNode): LexicalNode | null {
    const children = node.getChildren();
    const childrenLength = children.length;

    for (let i = 0; i < childrenLength; i++) {
        node.insertBefore(children[i]);
    }

    node.remove();
    return childrenLength > 0 ? children[childrenLength - 1] : null;
}
