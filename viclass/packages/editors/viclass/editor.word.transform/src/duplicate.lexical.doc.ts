import { createHeadlessEditor } from '@lexical/headless';
import { createBinding, syncLexicalUpdateToYjs, syncYjsChangesToLexical } from '@lexical/yjs';
import { DocumentId, EditorType, ViewportId } from '@viclass/editor.core';
import { $getNodeByKey, $nodesOfType, LexicalEditor, NodeKey } from 'lexical';
import * as Y from 'yjs';
import { SubViewportNode, WordNodes } from './nodes';

type SubViewportInfo = {
    nodeKey: NodeKey;
    globalId: DocumentId;
    editorType: EditorType;
    viewportId: ViewportId;
};

type SubViewportByEdType = Map<EditorType, SubViewportInfo[]>;

export type DuplicateSubViewportDocsFunc = (
    edType: EditorType,
    globalIds: DocumentId[]
) => Promise<Map<DocumentId, DocumentId>>;

export async function duplicateLexicalDoc(
    docId: DocumentId,
    originalDoc: Y.Doc,
    duplicateSubViewportDocs: DuplicateSubViewportDocsFunc,
    onError?: (error: Error) => void
): Promise<Y.Doc> {
    return await new Promise<Y.Doc>(async (resolve, reject) => {
        const teardowns: Array<() => void> = [];

        try {
            const lexicalEd = createHeadlessEditor({
                nodes: [...WordNodes],
                onError,
                editable: true,
            });

            // Try to initialize the editor from the ydoc
            const [targetYDoc, removeListener] = createBindedYDoc(lexicalEd);
            teardowns.push(removeListener);

            const originalState = Y.encodeStateAsUpdate(originalDoc);
            Y.applyUpdate(targetYDoc, originalState);

            lexicalEd.update(() => {}, { discrete: true }); // update immediately

            const [nodesByEdType, idReplaceMap] = await readAndDuplicateSubViewports(
                lexicalEd,
                duplicateSubViewportDocs
            );

            // perform the replacements on the headless editor -> will be applied to the Y.Doc dupTarget
            lexicalEd.update(
                () => {
                    nodesByEdType.forEach((nodesInfo, edType) => {
                        for (const nodeInfo of nodesInfo) {
                            const node = $getNodeByKey<SubViewportNode>(nodeInfo.nodeKey);

                            const newVpId = createViewportId(docId, edType);
                            let newGlobalId = idReplaceMap.get(nodeInfo.globalId);
                            if (nodeInfo.globalId?.startsWith('LOCALDOC_')) {
                                newGlobalId = nodeInfo.globalId.replace(nodeInfo.viewportId, newVpId);
                            }

                            node.viewportId = newVpId;
                            node.globalId = newGlobalId;
                        }
                    });
                },
                { discrete: true } // update immediately
            );

            resolve(targetYDoc);
        } catch (error) {
            reject(error);
        } finally {
            teardowns.forEach(t => t());
        }
    });
}

/**
 * Read the subviewports from the lexical editor and duplicate them with the API.
 * We don't actually replace the duplicated subviewports into the node in this step
 */
async function readAndDuplicateSubViewports(
    lexicalEd: LexicalEditor,
    duplicateSubViewportDocs: DuplicateSubViewportDocsFunc
): Promise<[SubViewportByEdType, Map<DocumentId, DocumentId>]> {
    // gather all subviewports and duplicates them
    const nodesByEdType: SubViewportByEdType = lexicalEd.getEditorState().read(() => $getSubViewportsInfo());

    const duplicatePromises = [...nodesByEdType.keys()].map(editorType => {
        const nodes = nodesByEdType.get(editorType);
        const globalIds = nodes.map(node => node.globalId).filter(id => !!id);

        return duplicateSubViewportDocs(editorType, globalIds);
    });

    // accumulate the mappings of <original_id, duplicated_id>
    const idReplaceMap: Map<DocumentId, DocumentId> = (await Promise.all(duplicatePromises)).reduce(
        (accumulator, idMap) => {
            idMap.forEach((dupId, originalId) => {
                accumulator.set(originalId, dupId);
            });
            return accumulator;
        },
        new Map<DocumentId, DocumentId>()
    );

    return [nodesByEdType, idReplaceMap];
}

/**
 * Create a binded Y.Doc in the lexical editor and a listener that syncs the changes to the yjs doc
 * @returns the yjs doc and a function to remove the listener
 */
function createBindedYDoc(lexicalEd: LexicalEditor): [Y.Doc, () => void] {
    const dupTarget = new Y.Doc();
    const dummyId = `dummy-id-${Math.random()}`;
    const dummyProvider: any = {
        awareness: {
            getLocalState: () => null,
            getStates: () => [],
        },
    };
    const dupBinding = createBinding(lexicalEd, dummyProvider, dummyId, dupTarget, new Map([[dummyId, dupTarget]]));
    // this syncs yjs changes to the lexical editor
    const onYjsTreeChanges = (events: Y.YEvent<any>[]) => {
        syncYjsChangesToLexical(dupBinding, dummyProvider as any, events, false);
    };

    dupBinding.root.getSharedType().observeDeep(onYjsTreeChanges);

    const removeListener = lexicalEd.registerUpdateListener(
        ({ prevEditorState, editorState, dirtyLeaves, dirtyElements, normalizedNodes, tags }) => {
            if (tags.has('skip-collab') === false) {
                syncLexicalUpdateToYjs(
                    dupBinding,
                    dummyProvider,
                    prevEditorState,
                    editorState,
                    dirtyElements,
                    dirtyLeaves,
                    normalizedNodes,
                    tags
                );
            }
        }
    );

    return [dupTarget, removeListener];
}

/**
 * Create a new viewport id for the sub-viewport node
 */
function createViewportId(parentId: DocumentId, edType: EditorType) {
    return `${parentId}_${edType}_${new Date().getTime()}${Math.floor(Math.random() * 10000)}`;
}

/**
 * get all subviewports and their info
 */
function $getSubViewportsInfo(): SubViewportByEdType {
    const nodesByEdType: Map<EditorType, SubViewportInfo[]> = new Map();

    $nodesOfType(SubViewportNode).forEach(node => {
        const nodes = nodesByEdType.get(node.editorType) || [];
        nodesByEdType.set(node.editorType, [
            ...nodes,
            {
                nodeKey: node.getKey(),
                globalId: node.globalId,
                editorType: node.editorType,
                viewportId: node.viewportId,
            },
        ]);
    });

    return nodesByEdType;
}
