import {
    CoordinatorE<PERSON>,
    DefaultToolB<PERSON>,
    EditorBlurCES,
    EditorCoordinator,
    EditorFocusCES,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    keyboardHandlingKey,
    MouseEventListener,
    NativeEventTarget,
    PointerEventListener,
    ToolBar,
    ToolEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportMode,
} from '@viclass/editor.core';
import { FloatingUIToolState, FreedrawingEditor } from './freedrawing.api';
import { CommonToolState, EraserToolState, FreedrawingToolEventData, FreedrawingToolType } from './freedrawing.models';
import { FreedrawingObjCtrl } from './objects/freedrawing.obj.ctrl';
import { FreedrawingTool } from './tools/freedrawing.tool';

/**
 *
 * <AUTHOR>
 */

export class FreedrawingToolBar extends DefaultToolBar<
    FreedrawingToolType,
    FreedrawingTool<ToolState, FreedrawingObjCtrl<any>>
> {
    keyboardHandler: KeyboardEventListener<NativeEventTarget<any>>;
    mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    pointerHandler: PointerEventListener<NativeEventTarget<any>>;
    toolListener: ToolEventListener<ToolBar<any, any>, any>;

    private freedrawingToolShortcut: string[][] = [['\\'], ['r'], ['R'], ['l'], ['L'], ['a'], ['A'], ['p'], ['P']];

    constructor(
        private coordinator: EditorCoordinator,
        private editor: FreedrawingEditor
    ) {
        super(coordinator);
        this.initializeToolbar();
    }

    private initializeToolbar() {
        this.toolbarsState
            .set('CommonPropertiesTool', new CommonToolState('#000000', undefined, 3))
            .set('EraserPropertiesTool', new EraserToolState(10))
            .set('FloatingUITool', new FloatingUIToolState());

        // Create keyboard handling from shortcuts
        const shortcuts: Map<string, string[]> = new Map();
        this.freedrawingToolShortcut.map(k => shortcuts.set(keyboardHandlingKey('keydown', k), k));
        shortcuts.forEach((k, _) => this.keyboardHandling.push(new (class extends KeyboardHandlingItem {})(k)));

        this.keyboardHandler = new this._keyboardHandler(this);
        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );

        this.toolListener = new this._toolListener(this);
        this.registerToolListener(this.toolListener);
    }

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: FreedrawingToolBar) {}

        onEvent(event: KeyboardEventData<NativeEventTarget<any>>): KeyboardEventData<NativeEventTarget<any>> {
            if (this.toolbar.isDisabled() || event.nativeEvent.repeat) return event;

            if (['\\'].includes(event.nativeEvent.key)) {
                // if (!this.toolBar.isToolActive('TextTool'))
                //     this.toolBar.focus('TextTool')
                // else this.toolBar.blur('TextTool')
            } else if (['r', 'R'].includes(event.nativeEvent.key)) {
                if (!this.toolbar.isToolActive('RectangleTool')) this.toolbar.focus('RectangleTool');
                else this.toolbar.blur('RectangleTool');
                event.nativeEvent.preventDefault();
                event.continue = false;
            } else if (['l', 'L'].includes(event.nativeEvent.key)) {
                if (!this.toolbar.isToolActive('LineTool')) this.toolbar.focus('LineTool');
                else this.toolbar.blur('LineTool');
                event.nativeEvent.preventDefault();
                event.continue = false;
            } else if (['a', 'A'].includes(event.nativeEvent.key)) {
                // if (!this.toolBar.isToolActive('TextTool'))
                //     this.toolBar.focus('TextTool')
                // else this.toolBar.blur('TextTool')
            } else if (['p', 'P'].includes(event.nativeEvent.key)) {
                if (!this.toolbar.isToolActive('PencilTool')) this.toolbar.focus('PencilTool');
                else this.toolbar.blur('PencilTool');
                event.nativeEvent.preventDefault();
                event.continue = false;
            } else if (event.nativeEvent.key === 'Escape' && this.toolbar.curTool) {
                this.toolbar.blur(this.toolbar.curTool);
            }

            return event;
        }
    };

    private _toolListener = class implements ToolEventListener<ToolBar<any, any>, any> {
        constructor(private toolbar: FreedrawingToolBar) {}

        onEvent(eventData: FreedrawingToolEventData): FreedrawingToolEventData | Promise<FreedrawingToolEventData> {
            if (this.toolbar.isDisabled()) return eventData;

            const tool = this.toolbar.getTool(eventData.toolType) == null ? this.toolbar.activeTool : null;
            if (tool) {
                return tool.handleToolEvent(eventData);
            }

            return eventData;
        }
    };

    protected override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        if (vpMode != 'EditMode') this.disable();
        else this.enable();

        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                default: {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: FreedrawingToolBar) {}

            onEvent(event: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                if (!this.toolbar.viewport || this.toolbar.viewport.id != event.state.vmId) return event;

                switch (event.eventType) {
                    case 'viewport-edit-mode': {
                        this.toolbar.onViewportModeChanged('EditMode');
                        break;
                    }
                    case 'viewport-interactive-mode': {
                        this.toolbar.onViewportModeChanged('InteractiveMode');
                        break;
                    }
                    case 'viewport-view-mode': {
                        this.toolbar.onViewportModeChanged('ViewMode');
                        break;
                    }
                    case 'viewport-disabled': {
                        const preview = this.toolbar.editor.previewLayers.get(event.state.vmId);
                        if (preview) {
                            const activeTool = this.toolbar.activeTool;
                            if (activeTool) {
                                this.toolbar.blur(activeTool.toolType);
                                this.toolbar.focus(activeTool.toolType);
                            }

                            this.toolbar.editor.removePreviewLayer(this.toolbar.viewport);
                        }
                        const evs = event.state as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(evs.vmId, evs);
                        this.toolbar.onViewportModeChanged('Disabled');
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(event.state.vmId);
                        break;
                    }
                    case 'editor-focus': {
                        const state = event.state as EditorFocusCES;
                        if (state.editor.editorType == this.toolbar.editor.editorType) {
                            this.toolbar.registerToolListener(this.toolbar.toolListener);
                        }
                        break;
                    }
                    case 'editor-blur': {
                        const state = event.state as EditorBlurCES;
                        if (state.editor.editorType == this.toolbar.editor.editorType) {
                            this.toolbar.unregisterToolListener(this.toolbar.toolListener);
                        }
                        break;
                    }
                    default:
                        break;
                }

                return event;
            }
        })(this);
    }
}
