import {
    AbstractCommand,
    BoundaryChangeEventData,
    cmdMeta,
    Cursor,
    DefaultVDocCtrl,
    InferredPointerEvent,
    isMac,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseHandlingItem,
    NativeEventTarget,
    NumDPointerChange,
    PointerEventData,
    PointerEventListener,
    PointerHandlingItem,
    pointerTypeDyn,
    pointerTypePenMouse,
    Rectangle,
    Tool,
    ToolState,
    UIPointerEventData,
    UnboundedGraphicLayerCtrl,
    UserInputHandlerType,
    VEventListener,
    ViewportManager,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.freedrawing';
import { BehaviorSubject } from 'rxjs';
import { EndPreviewCmd, UpdatePreviewCmd } from '../cmd/freedrawing.cmd';
import { FreedrawingToolBar } from '../freedrawing.api';
import { FreedrawingEditor } from '../freedrawing.editor';
import { FreedrawingLayerRenderer } from '../freedrawing.layer.renderer';
import {
    FreedrawingHistoryItem,
    FreedrawingKeyboardEvent,
    FreedrawingToolEventData,
    FreedrawingToolType,
} from '../freedrawing.models';
import { freedrawingDocReg, freedrawingLayerReg } from '../freedrawing.util';
import { FreedrawingObjCtrl } from '../objects/freedrawing.obj.ctrl';

/**
 *
 * <AUTHOR>
 */
export abstract class FreedrawingTool<TState extends ToolState, OCtrl extends FreedrawingObjCtrl<any>> implements Tool {
    readonly type: UserInputHandlerType = 'Tool';
    readonly mouseHandling: MouseHandlingItem[] = [];
    readonly pointerHandling: PointerHandlingItem[] = [];
    readonly keyboardHandling: KeyboardHandlingItem[] = [];

    abstract readonly toolType: FreedrawingToolType;
    abstract readonly toolState: TState;

    protected isPointerDown = false;

    private lastHistoryItem: FreedrawingHistoryItem;
    protected firstObj: boolean = false;

    keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: FreedrawingTool<TState, OCtrl>) {}

        onEvent(event: FreedrawingKeyboardEvent): FreedrawingKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    constructor(
        protected editor: FreedrawingEditor,
        readonly toolbar: FreedrawingToolBar
    ) {
        this.registerPointerHandling(
            { event: 'longpress', disable: true },

            { event: 'pointerdown', button: 0, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },
            { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },

            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 }
        );
    }

    onAttachViewport() {}
    onDetachViewport() {}

    handleKeyboardEvent(event: FreedrawingKeyboardEvent): FreedrawingKeyboardEvent {
        if (event.nativeEvent.key == 'Escape') {
            if (this.hasPreviewObjectCtrl(event.viewport)) {
                // cancel the preview object
                const previewing = this.curPreviewObjectCtrl(event.viewport);
                if (previewing) {
                    const endPreviewCmd = new EndPreviewCmd(
                        cmdMeta(event.viewport, previewing.id, CmdTypeProto.END_PREVIEW)
                    );
                    this.sendCommand(endPreviewCmd);

                    this.editor.cancelPreviewHistoryItem(event.viewport.id);

                    event.nativeEvent.preventDefault();
                    event.continue = false;
                    return event;
                }
            }
        }

        return event;
    }

    onDisable() {}

    onEnable() {}

    registerMouseHandling(...handling: { event: string; button: number }[]) {
        this.mouseHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: any[]) {
        this.keyboardHandling.push(...handling);
        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = false;
            })(['esc'])
        );
    }

    pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        cursor?: BehaviorSubject<Cursor[] | undefined>;

        constructor(public tool: FreedrawingTool<ToolState, any>) {}

        onEvent(event: PointerEventData<any>): PointerEventData<any> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            if ('nativeEvent' in event)
                // handle UI event
                return this.tool.handlePointerEvent(event);
            else return this.tool.handleNonUIPointerEvent(event);
        }
    })(this);

    protected updateCursor(cursor: Cursor[] | undefined) {
        if (!this.pointerHandler.cursor) this.pointerHandler.cursor = new BehaviorSubject(undefined);
        this.pointerHandler.cursor.next(cursor);
    }

    handlePointerEvent(event: UIPointerEventData<any>): UIPointerEventData<any> {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                if (this.isPointerDown) this.finishPreviewIfAny(event.viewport);
                this.onPointerDown(event).then(() => {
                    this.isPointerDown = true;
                });
                break;
            }
            case 'pointermove': {
                if (this.isPointerDown) this.onPointerMove(event);
                break;
            }
            case 'pointerup': {
                if (this.isPointerDown) {
                    this.onPointerUp(event);
                    this.isPointerDown = false;
                }
                break;
            }
        }

        event.continue = false;

        return event;
    }

    handleNonUIPointerEvent(event: NumDPointerChange | InferredPointerEvent): NumDPointerChange | InferredPointerEvent {
        return event;
    }

    /**
     *
     * @param viewport The viewport where the preview needs handling
     * @returns true if the previewing object is updated
     */
    protected finishPreviewIfAny(viewport: ViewportManager): boolean {
        if (!this.hasPreviewObjectCtrl(viewport)) return false;

        this.isPointerDown = false;

        // verify current previewing object, if it's not valid then it will be removed
        const previewing = this.curPreviewObjectCtrl(viewport);

        if (previewing) {
            const endPreviewCmd = new EndPreviewCmd(cmdMeta(viewport, previewing.id, CmdTypeProto.END_PREVIEW));
            this.sendCommand(endPreviewCmd);
            return false;
        }

        // update previewing object's state
        return true;
    }

    onBoundaryChangeFromUserInteraction(boundaryEvent: BoundaryChangeEventData) {
        switch (boundaryEvent.eventType) {
            case 'boundary-update-start':
                break;
            case 'boundary-update-end':
                if (this.lastHistoryItem) {
                    this.editor.addHistoryItem(this.lastHistoryItem);
                    this.lastHistoryItem = null;
                }
                break;
            case 'boundary-update':
                this.updateBoundary(boundaryEvent.mouseEvent.viewport, {
                    start: boundaryEvent.state.newBoundary.start,
                    end: boundaryEvent.state.newBoundary.end,
                });
                break;
        }
    }

    private updateBoundary(vm: ViewportManager, boundary: Rectangle) {
        // update preview object's state
        const previewing = this.curPreviewObjectCtrl(vm);
        const cmd = new UpdatePreviewCmd(cmdMeta(vm, previewing.id, CmdTypeProto.UPDATE_BOUNDARY_PREVIEW));
        cmd.updateBoundary(this.toolType, boundary, true);

        this.sendCommand(cmd);

        const layer = this.topLayer(previewing.document as DefaultVDocCtrl);

        // create history item
        const state = JSON.parse(JSON.stringify(previewing.state));
        state.boundary = JSON.parse(JSON.stringify(boundary));

        this.lastHistoryItem = {
            supporter: this.editor,
            viewportId: vm.id,
            docId: previewing.document.state.id,
            layerId: layer.state.id,
            objId: previewing.id,
            toolType: this.toolType,
            state: state,
            action: 'UPDATE_BOUNDARY',
        };
    }

    handleToolEvent(eventData: FreedrawingToolEventData): FreedrawingToolEventData {
        const vm = this.toolbar.viewport;

        switch (eventData.eventType) {
            case 'change': {
                this.onToolChange(eventData, vm);
                break;
            }
        }

        return eventData;
    }

    onBlur() {
        this.isPointerDown = false;

        if (isMac) {
            document.body.classList.remove('disable-user-select');
        }
    }

    onFocus() {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;

        if (isMac) {
            document.body.classList.add('disable-user-select');
        }
    }

    protected onToolChange(event: FreedrawingToolEventData, vm: ViewportManager) {
        // do nothing
    }

    protected needAsyncCreation(vp: ViewportManager): boolean {
        if (!this.editor.docByViewport.get(vp.id)) return true;
        else {
            const doc = this.curDoc(vp);
            const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
                freedrawingLayerReg(vp.id, doc.state.id)
            );

            if (!layerRegistry.hasEntity()) {
                return true;
            }
        }
        return false;
    }

    protected async doCreateAsync(vp: ViewportManager): Promise<boolean> {
        // insert doc if not exist
        if (!this.editor.docByViewport.get(vp.id)) {
            await this.editor.crdFeature.createDocument(this.editor, {
                vm: vp,
            });
        }

        const doc = this.curDoc(vp);
        const layerRegistry = this.editor.regMan.registry<FreedrawingLayerRenderer>(
            freedrawingLayerReg(vp.id, doc.state.id)
        );

        if (!layerRegistry.hasEntity()) {
            await this.editor.crdFeature.createLayer(this.editor, {
                vm: vp,
                docId: doc.state.id,
                globalId: doc.state.globalId,
            });
        }

        return true;
    }

    protected abstract onPointerDown(event: UIPointerEventData<any>): Promise<boolean>;

    protected onPointerUp(event: UIPointerEventData<any>): boolean {
        return this.finishPreviewIfAny(event.viewport);
    }

    protected onPointerMove(event: UIPointerEventData<any>) {
        if (!this.hasPreviewObjectCtrl(event.viewport) || !this.isPointerDown) return false;

        // update previewing object's state
        return true;
    }

    protected sendCommand(cmd: AbstractCommand<CmdTypeProto>) {
        cmd.meta.channelCode = this.editor.id;
        this.editor.cmdChannel.receive(cmd);
    }

    protected hasPreviewObjectCtrl(vm: ViewportManager): boolean {
        const layer = this.editor.previewLayers.get(vm.id);
        return layer && layer.hasObjects();
    }

    protected curPreviewObjectCtrl(vm: ViewportManager): OCtrl {
        const layer = this.editor.previewLayers.get(vm.id);
        if (!layer) return undefined;
        // because only has one object in preview, so we can get it directly by index
        return layer.objects[layer.objects.length - 1] as OCtrl;
    }

    protected curDoc(vm: ViewportManager): DefaultVDocCtrl {
        const localId = this.editor.docByViewport.get(vm.id);
        const registry = this.editor.regMan.registry<DefaultVDocCtrl>(freedrawingDocReg(vm.id));
        if (!registry) return null;
        return registry.getEntity(localId);
    }

    protected topLayerRenderer(docCtrl: DefaultVDocCtrl): FreedrawingLayerRenderer {
        const layer = this.topLayer(docCtrl);
        return this.editor.regMan
            .registry<FreedrawingLayerRenderer>(freedrawingLayerReg(docCtrl.viewport.id, docCtrl.state.id))
            ?.getEntity(layer.state.id);
    }

    isTopLayerVp(viewport: ViewportManager): boolean {
        const layers = viewport.allLayers().filter(l => !!l.editor);
        if (layers.length < 1) return undefined;

        const topLayer = layers[layers.length - 1];
        return topLayer.editor instanceof FreedrawingEditor;
    }

    protected topLayer(doc: DefaultVDocCtrl): UnboundedGraphicLayerCtrl {
        if (doc.layers.length <= 0) return null;

        const layer = doc.layers.reduce((maxLayerZindex, nextLayer) =>
            maxLayerZindex.zindex > nextLayer.zindex ? maxLayerZindex : nextLayer
        );
        return layer as UnboundedGraphicLayerCtrl;
    }

    boundaryChangeListener(): VEventListener<BoundaryChangeEventData> {
        const self = this;
        return new (class implements VEventListener<BoundaryChangeEventData> {
            onEvent(eventData: BoundaryChangeEventData): BoundaryChangeEventData | Promise<BoundaryChangeEventData> {
                const tool = self.toolbar.getTool(self.toolType);
                tool.onBoundaryChangeFromUserInteraction(eventData);

                return eventData;
            }
        })();
    }
}
