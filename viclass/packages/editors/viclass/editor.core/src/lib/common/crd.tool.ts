import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { BehaviorSubject } from 'rxjs';
import {
    BaseBoardViewportManager,
    BoardViewportManager,
    CmdChannel,
    DocumentEditor,
    KeyboardEventData,
    Rectangle,
    reliableCmdMeta,
    Tool,
    ToolBar,
    UIPointerEventData,
    UserInputHandlerType,
    ViewportId,
    ViewportManager,
} from '../api';
import { RectangleObjectBoundary } from '../boundary';
import { ROBFeature, SelectionFeature } from '../tools';
import { mouseLocation } from '../util';
import { fcConvertProtoToBoundary, FCPreviewBoundaryCmd } from './common.cmd';
import { DocCRDFeature } from './crd.feature';

export class CRDTool {
    private pointerDownEvent?: UIPointerEventData<any>;
    protected lastPointerMove?: UIPointerEventData<any>;
    protected requestedFrame: boolean = false;
    type: UserInputHandlerType = 'Tool';
    toolbar?: ToolBar<any, Tool> | undefined;
    private previewBoundaryObj?: RectangleObjectBoundary;
    isCreatingDoc$?: BehaviorSubject<boolean>;

    // if in the process of creating doc on the server
    // then mouse event should not be effective anymore
    private canHandlePointerEvent: boolean = true;

    constructor(
        public readonly toolType: any,
        private editor: DocumentEditor,
        private cmdChannel: CmdChannel,
        private robFeature: ROBFeature,
        private selectFeature: SelectionFeature,
        private DocCRDFeature: DocCRDFeature
    ) {}

    resetState() {
        if (this.pointerDownEvent) this.sendStopPreview(this.toolbar!.viewport!);

        if (this.isCreatingDoc$) this.isCreatingDoc$.next(false);

        delete this.pointerDownEvent;
        delete this.lastPointerMove;
    }

    onFocusCreateDocTool() {
        const vpId = this.toolbar?.viewport?.id;
        if (!vpId) return;

        const selectTool = this.selectFeature.getSelectToolByVp(vpId);
        selectTool?.deselectAllDocCtx(undefined, false);
    }

    destroy(viewport: ViewportManager) {
        this.resetState();
    }

    private disablePointerHandling() {
        this.canHandlePointerEvent = false;
    }

    private enablePointerHandling() {
        this.canHandlePointerEvent = true;
    }

    private destroyCurDocBoundary(viewportId: ViewportId) {
        if (this.previewBoundaryObj) {
            this.robFeature.releaseROB(viewportId, this.previewBoundaryObj);
            delete this.previewBoundaryObj;
        }
    }

    processCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        switch (cmd.meta.cmdType) {
            case FCCmdTypeProto.PREVIEW_BOUNDARY:
                if (!this.previewBoundaryObj) {
                    if (!cmd.state.getBoundary()) return cmd;

                    this.previewBoundaryObj = this.robFeature.requestROB(
                        cmd.meta.viewport.id,
                        fcConvertProtoToBoundary(cmd.state.getBoundary()!)
                    );
                    this.previewBoundaryObj.show();
                    this.previewBoundaryObj.disablePointerEvent();
                } else {
                    if (!cmd.state.getBoundary()) this.destroyCurDocBoundary(cmd.meta.viewport.id);
                    else this.previewBoundaryObj.updateBoundary(fcConvertProtoToBoundary(cmd.state.getBoundary()!));
                }

                break;
        }

        return cmd;
    }

    handlePointerEvent(event: UIPointerEventData<any>): UIPointerEventData<any> {
        if (!this.canHandlePointerEvent) {
            return event;
        }
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                if (this.pointerDownEvent) {
                    this.resetState();
                } else {
                    this.onPointerDown(event);
                }
                event.continue = false;
                break;
            }
            case 'pointermove': {
                if (this.pointerDownEvent) {
                    const nativeEvent = event.nativeEvent;
                    // if we detect mouse move and the 'left' button is not pressed
                    // that means the button has been released else where, possibly outside of
                    // monitoring of the event manager
                    if (nativeEvent.pointerType === 'mouse' && !(nativeEvent.buttons & 1)) {
                        this.resetState();
                        event.continue = false;
                        return event;
                    }

                    this.lastPointerMove = event;
                    if (!this.requestedFrame) {
                        requestAnimationFrame(() => {
                            this.requestedFrame = false;
                            this.onPointerMove(this.lastPointerMove!);
                        });
                        this.requestedFrame = true;
                    }
                    event.continue = false;
                }
                break;
            }
            case 'pointerup': {
                if (this.pointerDownEvent) {
                    this.onPointerUp(event);
                }

                event.continue = false;

                break;
            }
        }

        return event;
    }

    handleKeyboardEvent(event: KeyboardEventData<any>): KeyboardEventData<any> {
        if (event.nativeEvent.key == 'Escape') {
            this.resetState();
        }

        return event;
    }

    private updateCurBoundaryFromEvent(event: UIPointerEventData<any>): Rectangle | undefined {
        let boundary: Rectangle;
        const mousePos = mouseLocation(event);
        if (!this.previewBoundaryObj)
            boundary = {
                start: mouseLocation(this.pointerDownEvent!),
                end: mousePos,
            };
        else
            boundary = {
                start: this.previewBoundaryObj.boundary.start!,
                end: mousePos,
            };

        const meta = reliableCmdMeta(event.viewport, 0, -1, FCCmdTypeProto.PREVIEW_BOUNDARY);
        const pcmd = new FCPreviewBoundaryCmd(meta);

        pcmd.setState(boundary);

        this.cmdChannel.receive(pcmd);

        return boundary;
    }

    private async onPointerUp(event: UIPointerEventData<any>) {
        if (this.previewBoundaryObj) {
            const boundary = this.updateCurBoundaryFromEvent(event);

            if (boundary) {
                // proceed to create document, send relevant insert doc insert layer commands
                const start = boundary.start!;
                const end = boundary.end!;

                const topLeft = {
                    x: Math.min(start.x, end.x),
                    y: Math.max(start.y, end.y),
                };

                const bottomRight = {
                    x: Math.max(start.x, end.x),
                    y: Math.min(start.y, end.y),
                };

                const bd = {
                    start: topLeft,
                    end: bottomRight,
                    width: Math.abs(topLeft.x - bottomRight.x),
                    height: Math.abs(topLeft.y - bottomRight.y),
                };

                if (!this.DocCRDFeature) throw new Error('Create doc feature not initialized!!!!');

                const vm = this.toolbar!.viewport! as BoardViewportManager;

                const sTL = vm.getScreenPos(bd.start);
                const sBR = vm.getScreenPos(bd.end);
                const sW = Math.abs(sTL.x - sBR.x);
                const sH = Math.abs(sTL.y - sBR.y);

                // prevent if the preview is too small
                if (sW > 50 && sH > 50) {
                    try {
                        this.disablePointerHandling();
                        this.previewBoundaryObj.blink(100);
                        // create the document
                        const c = await this.DocCRDFeature.createDocument(
                            this.editor,
                            {
                                vm: vm,
                                boundary: bd,
                            },
                            true
                        );

                        const ctrl = this.editor.findDocumentByLocalId(vm.id, c.expectedChanges[0].localId);
                        if (!ctrl) throw new Error('Document not available locally after creation');
                        else {
                            this.selectFeature.selectOnly(this.editor, vm, ctrl);
                        }
                    } catch (e) {
                        console.log('create document failed... ', e);
                    } finally {
                        this.previewBoundaryObj?.stopBlinking();
                        this.enablePointerHandling();
                    }
                }

                this.resetState();
            }
        } else this.resetState();
    }

    private sendStopPreview(viewport: ViewportManager) {
        // stop the preview
        const meta = reliableCmdMeta(viewport, 0, -1, FCCmdTypeProto.PREVIEW_BOUNDARY);
        const pcmd = new FCPreviewBoundaryCmd(meta);
        pcmd.state.setStoppreview(true);
        this.cmdChannel.receive(pcmd);
    }

    private onPointerMove(event: UIPointerEventData<any>) {
        this.updateCurBoundaryFromEvent(event);
    }

    private onPointerDown(event: UIPointerEventData<any>) {
        if (!(event.viewport instanceof BaseBoardViewportManager)) return;

        this.pointerDownEvent = event;
        this.isCreatingDoc$?.next(true);
    }
}
