import {
    EditorCoordinator,
    KeyboardEventData,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventData,
    MouseEventListener,
    NativeEventTarget,
    ToolState,
} from '../api';
import { mouseLocation } from '../util';
import { CommonTool, CommonToolType } from './common.tool';
import { ContextMenuFeature, ListMenu } from './contextmenu.feature';
import { SelectContext } from './select.tool';
import { SelectionFeature } from './selection.feature';

export interface PositionContextMenu {
    x: number;
    y: number;
}

export interface ContextMenuState extends ToolState {
    listMenu: ListMenu[];
    position: PositionContextMenu | null;
}

/**
 *
 * <AUTHOR>
 */
export class ContextMenuTool extends CommonTool {
    override readonly toolType: CommonToolType = 'contextmenu';

    override readonly toolState: ContextMenuState;

    constructor(
        private _p: EditorCoordinator,
        private selectionFeature: SelectionFeature,
        private contextmenuFeature: ContextMenuFeature
    ) {
        super();
        this.toolState = {
            listMenu: [],
            position: null,
        };
        this.keyboardHandling.push(
            new (class extends KeyboardHandlingItem {
                override global = true;
                override event = 'keydown';
            })(['esc'])
        );
        this.mouseHandling.push(
            { event: 'contextmenu', button: 2 },
            { event: 'contextmenu', button: 2, global: true },
            { event: 'contextmenu', button: 2, keys: ['ctrl'], global: true }
        );
    }

    override onAttachViewport() {
        if (!this.toolbar.viewport) throw new Error('Viewport has not been attached to the toolbar');
        // add contextmenu tool to contextmenu feature
        this.contextmenuFeature.addContextMenuTool(this.toolbar.viewport.id, this);
    }

    override onDetachViewport() {
        if (!this.toolbar.viewport) throw new Error('Viewport has not been attached to the toolbar');
        this.contextmenuFeature.removeContextMenuTool(this.toolbar.viewport.id);
    }

    clearContextMenu = () => {
        this.toolState.listMenu = [];
        this.toolState.position = null;
        this.toolbar.update('contextmenu', this.toolState);
    };

    override readonly mouseHandler = new (class implements MouseEventListener<NativeEventTarget<any>> {
        constructor(public p: ContextMenuTool) {}

        async onEvent(eventData: MouseEventData<NativeEventTarget<any>>): Promise<any> {
            if (
                this.p.toolbar.isDisabled() ||
                this.p.toolbar.isToolDisable(this.p.toolType) ||
                !this.p.toolbar.viewport
            )
                return eventData;

            const native = eventData.nativeEvent;

            switch (native.type) {
                case 'contextmenu': {
                    eventData.continue = false;
                    native.preventDefault(); // Prevent the default context menu from showing

                    this.p.toolState.position = {
                        x: native.clientX,
                        y: native.clientY,
                    };

                    const viewportId = this.p.toolbar.viewport.id;
                    const selectionCtxs: SelectContext[] = this.p.selectionFeature.getCurrentSelections(viewportId);
                    const viewportPos = this.mouseLocationOnViewport(eventData);

                    const listMenu = await this.p.contextmenuFeature.onContextMenu(
                        selectionCtxs,
                        this.p._p,
                        viewportId,
                        viewportPos
                    );

                    this.p.toolState.listMenu = listMenu;
                    this.p.toolbar.update('contextmenu', this.p.toolState);
                    break;
                }
            }
            return eventData;
        }

        mouseLocationOnViewport(eventData: MouseEventData<NativeEventTarget<any>>) {
            try {
                return mouseLocation(eventData) ?? undefined;
            } catch {
                return undefined;
            }
        }
    })(this);

    override readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public p: ContextMenuTool) {}
        onEvent(eventData: KeyboardEventData<NativeEventTarget<any>>): KeyboardEventData<NativeEventTarget<any>> {
            if (this.p.toolbar.isDisabled() || this.p.toolbar.isToolDisable(this.p.toolType)) return eventData;

            if (eventData.nativeEvent.code === 'Escape' && this.p.toolState.position != null) {
                eventData.continue = false;
                eventData.nativeEvent.preventDefault();
                eventData.nativeEvent.stopPropagation();

                this.p.clearContextMenu();
            }

            return eventData;
        }
    })(this);
}
