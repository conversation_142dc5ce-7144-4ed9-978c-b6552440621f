/// <reference types="@viclass/ww/typings" />
import { BoundaryRectangle, LocatableEvent, MouseEventData, PointerEventData, Position, SpecialKeyboard } from './api';

type AttributeNSAble = {
    setAttribute: (name: string, value: string) => void;
};

type AttributeAble = {
    setAttribute: (name: string, value: any) => void;
};

export function attrsNS(obj: AttributeNSAble, opt: any) {
    for (const key in opt) {
        obj.setAttribute(key, opt[key]);
    }
}

export function attrs(obj: AttributeAble, opt: any) {
    for (const key in opt) {
        obj.setAttribute(key, opt[key]);
    }
}

export async function delayPromise(t: number = 0): Promise<void> {
    return new Promise<void>(function (resolve) {
        setTimeout(() => resolve(), t);
    });
}

export function strIdToBytes(s: string): Uint8Array {
    const encoder = new TextEncoder();

    return encoder.encode(s);
}

export function bytesToStrId(bytes: Uint8Array): string {
    const decoder = new TextDecoder();

    return decoder.decode(bytes);
}

export function mouseHandlingKey(
    event: string,
    button: number,
    pressedButtons?: number,
    keys?: SpecialKeyboard[]
): string {
    if (!pressedButtons && pressedButtons !== 0) pressedButtons = -1;
    if (keys && keys.length > 0) return 'Mouse' + event + button + pressedButtons + keys.sort();
    return event + button + pressedButtons;
}

export function keyboardHandlingKey(event: string, keys: (SpecialKeyboard | string)[]): string {
    return 'Keyboard' + event + keys.sort();
}

export function inferBoundary(boundary: BoundaryRectangle): BoundaryRectangle {
    if (boundary.start) {
        // infer end point if possible
        if (!boundary.end && boundary.width && boundary.height) {
            boundary.end = {
                x: boundary.start.x + boundary.width,
                y: boundary.start.y - boundary.height,
            };
        }
    } else if (boundary.end) {
        if (!boundary.start && boundary.width && boundary.height) {
            boundary.start = {
                x: boundary.end.x - boundary.width,
                y: boundary.end.y + boundary.height,
            };
        }
    }

    return boundary;
}

export function extractWidth(boundary: BoundaryRectangle): number {
    let width = boundary.width;
    if (!width)
        if (!boundary.end) throw new Error("Boundary doesn't have width dimension");
        else width = Math.abs(boundary.end.x - boundary.start!.x);

    return width;
}

export function extractHeight(boundary: BoundaryRectangle): number {
    let height = boundary.height;

    if (!height)
        if (!boundary.end) throw new Error("Boundary doesn't have height dimension");
        else height = Math.abs(boundary.end.y - boundary.start!.y);

    return height;
}

export function isPositionBoundaryChanged(b1: BoundaryRectangle, b2: BoundaryRectangle): boolean {
    if (b2.start && b1.start) return b2.start.x != b1.start.x || b2.start.y != b1.start.y;
    else if ((b2.start && !b1.start) || (b1.start && !b2.start)) return true;
    else return false;
}

export function isSizeBoundaryChanged(b1: BoundaryRectangle, b2: BoundaryRectangle): boolean {
    const b1Width = extractWidth(b1);
    const b1Height = extractHeight(b1);
    const b2Width = extractWidth(b2);
    const b2Height = extractHeight(b2);

    return Math.abs(b2Width - b1Width) > 0 || Math.abs(b1Height - b2Height) > 0;
}

/**
 * Utility implementation to retrieve mouse location of a mouse event
 * This methods doesn't guarantee to return a location because if the layer doesn't support the method
 * then it will not have the mouseLocation function
 * @param event
 * @returns
 */
export function mouseLocation(event: LocatableEvent<any>): Position {
    const source = event.source;

    if (source['mouseLocation'] && typeof source['mouseLocation'] === 'function') {
        const func = source['mouseLocation'] as (event: MouseEventData<any> | PointerEventData<any>) => Position;

        return func.call(source, event);
    } else {
        throw new Error('Unable to map mouse event to corresponding position');
    }
}

export class ReflowSync {
    private callback: Function | undefined;

    execute(callback: Function) {
        const previousCallback = this.callback;
        this.callback = callback;

        if (!previousCallback)
            requestAnimationFrame(() => {
                this.performSync();
            });
    }

    private performSync() {
        if (this.callback) this.callback();
        this.callback = undefined;
    }
}

/**
 * CachingReflowSync is a class designed to efficiently manage event handling,
 * especially when the processing involves heavy computation or needs to synchronize
 * with the browser's rendering cycle (reflow). This is particularly useful for
 * tasks like hit testing, rendering, or handling complex logic.
 *
 * It leverages requestAnimationFrame to ensure that heavy tasks are executed
 * within the browser's animation frame cycle, minimizing performance issues
 * and maintaining smooth interactions.
 */
export class CachingReflowSync<E, V> {
    private cache?: V = undefined;
    private isProcessing: boolean = false;
    private latestEvent?: E = undefined;
    private processCallback?: (event: E) => V | undefined = undefined;

    handleEvent(event: E, processCallback: (event: E) => V | undefined, onResult?: (res?: V) => void) {
        this.latestEvent = event;
        this.processCallback = processCallback;
        if (!this.isProcessing) {
            this.isProcessing = true;
            requestAnimationFrame(() => {
                this.processEventFrame();
            });
        }
        if (onResult) onResult(this.cache);
    }

    private setCache(result: V | undefined) {
        this.cache = result;
    }

    private processEventFrame() {
        try {
            if (this.latestEvent && this.processCallback) {
                const res = this.processCallback(this.latestEvent);
                this.setCache(res);
            }
        } catch (e) {
            throw e;
        } finally {
            this.latestEvent = undefined;
            this.isProcessing = false;
        }
    }
}

/**
 * util function to generate a random id.
 * currently use for awareness feature
 */
export function randomId(prefix: string = '', strLen: number = 10, containSuffixTimestamp: boolean = true): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomString = '';
    const charactersLength = characters.length;
    for (let i = 0; i < strLen; i++) randomString += characters.charAt(Math.floor(Math.random() * charactersLength));
    return prefix + randomString + (containSuffixTimestamp ? performance.now().toString() : '');
}

export const isMac = typeof navigator !== 'undefined' ? /macintosh|mac os x/i.test(navigator.userAgent) : false;

export function isCtrlOrMeta(event: KeyboardEvent | MouseEvent) {
    return isMac ? event.metaKey : event.ctrlKey;
}
