import Flatten from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
    ViErr,
} from '@viclass/editor.core';
import { syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewPolygon,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects/geo.document.ctrl';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewPointRenderProp,
    buildPreviewPolygonRenderProp,
    createVector,
    defaultNonUIPointerEventHandler,
    getPointAndVertex,
    handleIfPointerNotInError,
    isDifferentCoords,
    pickPointName,
} from './tool.utils';
import point = Flatten.point;
import line = Flatten.line;
import vector = Flatten.vector;

export class CreateRectangleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateRectangleTool';

    private points: RenderVertex[] = [];
    private linePerpendicular1: Flatten.Line;
    private linePerpendicular2: Flatten.Line;
    private isPointerDown = false;

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateRectangleTool> =
        new PotentialSelectionDelegator(this);

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex' && this.points.filter(p => p.relIndex == el.relIndex).length < 1;
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.linePerpendicular1 = undefined;
        this.linePerpendicular2 = undefined;
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length == 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length == 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length == 2) {
            await this.handleThirdPoint(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;

        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length == 2) {
            this.editor.filterElementFunc = el => false;
        }
        if (this.points.length == 3) {
            await this.finalizeRectangle(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        if (this.points.length > 1) return; // handle 0 or 1 point
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex; // add/update first point

        this.previewRectangle(ctrl, vertex.coords, vertex.coords);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        if (this.points.length > 2 || this.points.length < 1) return; // handle 1 or 2 points
        const { ctrl, vertex } = getPointAndVertex(this, event);
        if (isDifferentCoords(vertex.coords, this.points[0].coords)) {
            this.points[1] = vertex; // add/update 2nd point when it not match the first point
        }
        if (this.points.length !== 2) return;

        const vertex1 = this.points[0];
        const vertex2 = this.points[1];
        const p1 = point(vertex1.coords[0], vertex1.coords[1]);
        const p2 = point(vertex2.coords[0], vertex2.coords[1]);

        this.linePerpendicular1 = line(p2, vector(p2, p1));
        this.linePerpendicular2 = line(p1, vector(p2, p1));

        this.previewRectangle(ctrl, vertex1.coords, vertex2.coords);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleThirdPoint(event: GeoPointerEvent) {
        if (this.points.length > 3 || this.points.length < 2) return; // handle 2 or 3 points
        const { ctrl, vertex, coords } = getPointAndVertex(this, event);
        this.points[2] = vertex; // add/update third point

        // Calculate rectangle points based on the third point
        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];

        const p = point(coords[0], coords[1]);
        const projection1 = p.projectionOn(this.linePerpendicular1);
        const projection2 = p.projectionOn(this.linePerpendicular2);
        const v3 = [projection1.x, projection1.y, 0.0];
        const v4 = [projection2.x, projection2.y, 0.0];

        this.previewRectangle(ctrl, v1, v2, v3, v4);
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeRectangle(event: GeoPointerEvent) {
        // Retrieve controller, pointer position, and document global ID from event
        const { ctrl, pos, docGlobalId } = this.posAndCtrl(event);

        // Get 3D coordinates for the first and second rectangle points
        const firstPointCoords = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const secondPointCoords = [this.points[1].coords[0], this.points[1].coords[1], 0];

        // Project the current pointer position onto the rectangle's two perpendiculars to get third and fourth corners
        const pointerPos = point(pos.x, pos.y);
        const projectionOnFirstPerpendicular = pointerPos.projectionOn(this.linePerpendicular1);
        const projectionOnSecondPerpendicular = pointerPos.projectionOn(this.linePerpendicular2);
        const thirdPointCoords = [projectionOnFirstPerpendicular.x, projectionOnFirstPerpendicular.y, 0.0];
        const fourthPointCoords = [projectionOnSecondPerpendicular.x, projectionOnSecondPerpendicular.y, 0.0];

        // Calculate the "nth" direction index for distinguishing rectangles above/below/right/left etc.
        const baseVector = createVector(this.points[0], this.points[1]);
        const rectangleDirection = nthDirectionByLine(baseVector, firstPointCoords, thirdPointCoords);

        // Create RenderVertex representations for the last two points of the rectangle
        const rectangleVertex3: RenderVertex = {
            relIndex: -12,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewPointRenderProp(),
            coords: thirdPointCoords,
            name: undefined,
            usable: true,
            valid: true,
        };
        const rectangleVertex4: RenderVertex = {
            relIndex: -13,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewPointRenderProp(),
            coords: fourthPointCoords,
            name: undefined,
            usable: true,
            valid: true,
        };
        // Store the final rectangle vertices in the points array
        this.points[2] = rectangleVertex3;
        this.points[3] = rectangleVertex4;

        // Prepare to construct any new points whose names were not supplied yet
        const constructionPointRequests: GeoElConstructionRequest[] = [];
        // Request names for rectangle points from the user
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Hình Chữ Nhật',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            // User did not provide valid point names, so reset tool state and exit
            this.resetState();
            return;
        }

        // Build construction requests for any anonymous (unnamed) points
        for (let idx = 0; idx < this.points.length; idx++) {
            const pt = this.points[idx];
            if (!pt.name) {
                pt.name = inputPointNames[idx];
                const pointConstruction = buildPointConstruction(pt.name, {
                    x: pt.coords[0],
                    y: pt.coords[1],
                });
                constructionPointRequests.push(pointConstruction);
            }
        }

        // Decide how to construct the rectangle: by four points, or from segment+height, depending on what's available
        let rectangleConstructionRequest: GeoElConstructionRequest;

        const newPointsCount = this.points.reduce((acc, pt) => acc + (this.isNewVertex(pt) ? 1 : 0), 0);
        if (newPointsCount === 0) {
            // If all four points are available, build rectangle from point names
            rectangleConstructionRequest = this.buildRectangleFromPointsConstruction(this.points.map(pt => pt.name));
        } else {
            // Otherwise, construct from line segment (first two points) and height using pointer offset
            const baseLineName = `${this.points[0].name}${this.points[1].name}`;
            const baseLine = line(
                point(firstPointCoords[0], firstPointCoords[1]),
                point(secondPointCoords[0], secondPointCoords[1])
            );
            const rectHeight = pointerPos.distanceTo(baseLine)[0];
            const rectangleName = this.points.map(pt => pt.name).join('');
            rectangleConstructionRequest = this.buildRectangleFromLineSegmentAndLengthConstruction(
                rectangleName,
                baseLineName,
                rectHeight,
                rectangleDirection
            );
        }

        try {
            // Run rectangle construction under document awareness user feedback banner
            await ctrl.editor.awarenessFeature.useAwareness(
                ctrl.viewport.id,
                'Đang tạo hình chữ nhật',
                buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
                async () => {
                    // Geometry construction across network/async boundary
                    const constructions = [];
                    this.points.slice(0, 2).forEach(p => {
                        if (this.isNewVertex(p))
                            constructions.push(<ConstructionRequest>{
                                construction: constructionPointRequests.find(c => c.name === p.name),
                            });
                    });
                    constructions.push({ construction: rectangleConstructionRequest });

                    // Construct all new points and the rectangle itself in a single gateway command
                    const constructResponse = await constructExec(() =>
                        this.editor.geoGateway.construct(docGlobalId, constructions)
                    );

                    // Synchronize new elements with renderer and add operation to undo history
                    await syncRenderCommands(constructResponse.render, ctrl);
                    await addHistoryItemFromConstructionResponse(ctrl, constructResponse);
                }
            );
        } catch (e) {
            // Translate any errors thrown during construction to user-friendly error
            throw new ViErr('Có lỗi khi tạo hình chữ nhật', e);
        } finally {
            // Always reset tool state on completion (success or failure)
            this.resetState();
        }
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0 || this.points.length == 4) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // while pointer down -> handle all 3 cases
            if (this.points.length === 1) {
                this.handleFirstPoint(event);
            } else if (this.points.length === 2) {
                this.handleSecondPoint(event);
            } else if (this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        } else {
            // other mouse move -> only handle preview for the third point
            if (this.points.length === 2 || this.points.length === 3) {
                this.handleThirdPoint(event);
            }
        }
    }

    private async previewRectangle(ctrl: GeoDocCtrl, ...faces: number[][]) {
        const polygon: PreviewPolygon = {
            relIndex: -20,
            name: '',
            type: 'RenderPolygon',
            elType: 'Rectangle',
            faces: faces,
            renderProp: buildPreviewPolygonRenderProp(),
            usable: true,
            valid: true,
        };

        await syncPreviewCommands(polygon, ctrl);
    }

    private buildRectangleFromLineSegmentAndLengthConstruction(
        rectangleName: string,
        lineName: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'LineSegmentAndLength');
        construction.name = rectangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-FromLineSegment',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-LengthValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private buildRectangleFromPointsConstruction(pointNames: string[]): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Rectangle/RectangleEC', 'Rectangle', 'FromPoints');
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
        ];

        return construction;
    }
}
