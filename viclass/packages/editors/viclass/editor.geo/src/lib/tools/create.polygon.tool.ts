import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewPolygon,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewPolygonRenderProp,
    defaultNonUIPointerEventHandler,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
} from './tool.utils';

export class CreatePolygonTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreatePolygonTool';

    private points: RenderVertex[] = [];
    private relIndex: number = -100;
    private isPointerDown = false;

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreatePolygonTool> =
        new PotentialSelectionDelegator(this);

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return (
            el.type == 'RenderVertex' &&
            (this.points[0]?.relIndex == el.relIndex || this.points.filter(p => p.relIndex == el.relIndex).length < 1)
        );
    };

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/line preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.relIndex = -100;
        this.isPointerDown = false;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        const { ctrl, vertex } = getPointAndVertex(this, event, this.relIndex--);

        // Add new point or update existing point
        this.points.push(vertex);

        // Preview the current polygon
        await this.previewPolygon(ctrl);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        const { ctrl } = getPointAndVertex(this, event, this.relIndex--);
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        if (this.points.length === 1)
            if (this.points.length == 1) {
                this.points[0].unselectable = false;
                await syncEndPreviewModeCommand(ctrl);
                syncPreviewCommands(this.points[0], ctrl);
            }

        if (this.points.length > 2 && this.points[this.points.length - 1].relIndex === this.points[0].relIndex) {
            this.points.pop(); // Remove the last element if it is the same as the starting point
            await this.constructPolygon(ctrl);
            return;
        }
    }

    private async previewPolygon(ctrl: GeoDocCtrl) {
        if (this.points.length === 0) return;

        const polygon: PreviewPolygon = {
            relIndex: -20,
            name: '',
            type: 'RenderPolygon',
            elType: 'Polygon',
            faces: this.points.map(p => p.coords),
            renderProp: buildPreviewPolygonRenderProp(),
            usable: true,
            valid: true,
        };

        await syncPreviewCommands(polygon, ctrl);
    }

    private async constructPolygon(docCtrl: GeoDocCtrl) {
        // submit construction
        const constructionPoints: GeoElConstructionRequest[] = [];
        const inputPointNames = (
            await this.requestElementNames(docCtrl, [
                {
                    objName: 'Đa Giác',
                    originElement: this.points,
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                const constructionPoint = buildPointConstruction(p.name, {
                    x: p.coords[0],
                    y: p.coords[1],
                });
                constructionPoints.push(constructionPoint);
            }
        }

        const polygonName = this.points.map(p => p.name).join('');
        const constructionPolygon = this.buildPolygonConstruction(polygonName);

        this.resetState();

        await docCtrl.editor.awarenessFeature.useAwareness(
            docCtrl.viewport.id,
            'Đang tạo hình đa giác',
            buildDocumentAwarenessCmdOption(docCtrl.editor.awarenessConstructId, docCtrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docCtrl.state.globalId, [
                        ...constructionPoints.map(
                            c =>
                                <ConstructionRequest>{
                                    construction: c,
                                }
                        ),
                        {
                            construction: constructionPolygon,
                        },
                    ])
                );

                await syncRenderCommands(constructResponse.render, docCtrl);
                await addHistoryItemFromConstructionResponse(docCtrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length === 0) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async processPointerMove(event: GeoPointerEvent) {
        const { ctrl, coords, vertex } = getPointAndVertex(this, event, this.relIndex);

        if (this.isPointerDown && this.points.length > 0) {
            const lastIndex = this.points.length - 1;

            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;
            // Update the last point while dragging
            this.points[lastIndex] = vertex;

            // preview first point as it can be hit by pointer selection

            // Preview the updated polygon
            this.previewPolygon(ctrl);
        } else {
            // Show preview of potential next point
            const polygon: PreviewPolygon = {
                relIndex: -20,
                name: '',
                type: 'RenderPolygon',
                elType: 'Polygon',
                faces: [...this.points.map(p => p.coords), coords],
                renderProp: buildPreviewPolygonRenderProp(),
                usable: true,
                valid: true,
            };

            syncPreviewCommands(polygon, ctrl);
        }
    }

    private buildPolygonConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Polygon/PolygonEC', 'Polygon', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
