import { circle, point } from '@flatten-js/core';
import {
    buildDocumentAwarenessCmdOption,
    ErrorHandlerDecorator,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
} from '@viclass/editor.core';
import { syncEndPreviewModeCommand, syncPreviewCommands, syncRenderCommands } from '../cmd';
import { PotentialSelectionDelegator } from '../delegators/potential.selection.delegator';
import { geoDefaultHandlerFn } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    ConstructionRequest,
    GeoElConstructionRequest,
    GeoRenderElement,
    PreviewPolygon,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { constructExec, GeometryTool } from './geo.tool';
import {
    addHistoryItemFromConstructionResponse,
    buildPointConstruction,
    buildPreviewPointRenderProp,
    buildPreviewPolygonRenderProp,
    defaultNonUIPointerEventHandler,
    getPointAndVertex,
    handleIfPointerNotInError,
    pickPointName,
} from './tool.utils';

export class CreateEquilateralTriangleTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateEquilateralTriangleTool';

    private points: RenderVertex[] = [];
    private previewPoints: RenderVertex[] = [];
    private isPointerDown = false;
    private selectedPreviewPoint: RenderVertex = null;

    protected override readonly filterElementFunc = (el: GeoRenderElement) => {
        return el.type == 'RenderVertex';
    };

    private potentialSelectionDelegator: PotentialSelectionDelegator<CreateEquilateralTriangleTool> =
        new PotentialSelectionDelegator(this);

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.registerPointerHandling(
            // start add point
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1 },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },

            // confirm add point
            { event: 'pointerup', button: 0, pressedButtons: 0, pointerTypes: pointerTypeMouse },
            { event: 'pointerup', pointerTypes: pointerTypePen, numPointer: 0 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            // move point/preview
            {
                event: 'pointermove',
                pointerTypes: pointerTypeMouse,
                keys: ['nokey'],
            },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn }
        );
    }

    override resetState() {
        this.points = [];
        this.previewPoints = [];
        this.isPointerDown = false;
        this.selectedPreviewPoint = null;
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        switch (event.nativeEvent.type) {
            case 'pointerdown': {
                this.onPointerDown(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    override handleNonUIPointerEvent = defaultNonUIPointerEventHandler(this);

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerDown(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (this.isPointerDown) return; // don't handle if pointer is already down
        this.isPointerDown = true;

        if (this.points.length === 0) {
            await this.handleFirstPoint(event);
        } else if (this.points.length === 1) {
            await this.handleSecondPoint(event);
        } else if (this.points.length === 2 && this.previewPoints.length > 0) {
            await this.selectThirdPointPosition(event, false);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: GeoPointerEvent) {
        if (!this.shouldHandleClick(event)) return;
        event.continue = false;
        event.nativeEvent.preventDefault();

        if (!this.isPointerDown) return; // don't handle if pointer down is not set
        this.isPointerDown = false;
        this.potentialSelectionDelegator.clearPotential();

        // For the third point, we need another pointerdown/up pair to finalize
        if (this.points.length == 2 && this.selectedPreviewPoint) {
            await this.selectThirdPointPosition(event, false);
            await this.finalizeTriangle(event);
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleFirstPoint(event: GeoPointerEvent) {
        const { ctrl, vertex } = getPointAndVertex(this, event);
        this.points[0] = vertex;

        const v1 = vertex.coords;
        this.previewTriangle(ctrl, [v1, v1]);
        this.started = true;
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async handleSecondPoint(event: GeoPointerEvent) {
        const { vertex } = getPointAndVertex(this, event);
        this.points[1] = vertex;

        // Calculate potential third points
        const vertex1 = this.points[0];
        const vertex2 = this.points[1];
        const center1 = point(vertex1.coords[0], vertex1.coords[1]);
        const center2 = point(vertex2.coords[0], vertex2.coords[1]);
        const r = center1.distanceTo(center2)[0];
        const c1 = circle(center1, r);
        const c2 = circle(center2, r);
        const intersections = c1.intersect(c2);

        let idx = -12;
        const ps = intersections.map(
            i =>
                ({
                    type: 'RenderVertex',
                    renderProp: buildPreviewPointRenderProp(),
                    coords: [i.x, i.y],
                    usable: true,
                    valid: true,
                    unselectable: true,
                    name: undefined,
                    relIndex: idx--,
                }) as RenderVertex
        );

        // Order points according to mouse position relative to segment [p0, p1]
        const mainVector = [center2.x - center1.x, center2.y - center1.y];
        // Need the original coordinates of the intersection points in 3D for nthDirectionByLine
        const intersection3D_0 = [ps[0].coords[0], ps[0].coords[1], 0];
        const p0_3D = [center1.x, center1.y, 0];

        const nth = nthDirectionByLine(mainVector, p0_3D, intersection3D_0);

        if (nth === 1) {
            this.previewPoints.push(ps[0], ps[1]);
        } else {
            this.previewPoints.push(ps[1], ps[0]);
        }
        // Preview points are rendered on move in processPointerMove or on pointerup for the second point.
        // We don't call syncPreviewCommands(p, ctrl) here as they are part of the triangle preview.
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async selectThirdPointPosition(event: GeoPointerEvent, isPreview = true) {
        const { ctrl, pos } = this.posAndCtrl(event);

        if (this.points.length < 2 || this.previewPoints.length < 2) {
            syncEndPreviewModeCommand(ctrl);
            return;
        }

        const pointerCoords = [pos.x, pos.y];
        const dist0 = Math.hypot(
            this.previewPoints[0].coords[0] - pointerCoords[0],
            this.previewPoints[0].coords[1] - pointerCoords[1]
        );
        const dist1 = Math.hypot(
            this.previewPoints[1].coords[0] - pointerCoords[0],
            this.previewPoints[1].coords[1] - pointerCoords[1]
        );

        const selectedThirdPointCandidate = dist0 <= dist1 ? this.previewPoints[0] : this.previewPoints[1];

        // Preview the triangle using P0, P1, and the selected candidate for P2
        this.previewTriangle(ctrl, [this.points[0].coords, this.points[1].coords, selectedThirdPointCandidate.coords]);

        if (!isPreview) {
            // This path is hit on pointerdown for the third click
            this.selectedPreviewPoint = selectedThirdPointCandidate;
        }
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async finalizeTriangle(event: GeoPointerEvent) {
        const { ctrl, docGlobalId } = this.posAndCtrl(event);
        const vertex = this.selectedPreviewPoint;
        if (!vertex || this.points.length < 2) return; // Should have P0 and P1 set

        const constructionPoints: GeoElConstructionRequest[] = [];
        // requestPointName needs all three points including the selected preview point
        const inputPointNames = (
            await this.requestElementNames(ctrl, [
                {
                    objName: 'Triangle',
                    originElement: [...this.points, vertex],
                    pickName: pickPointName,
                    namesToAvoid: [],
                },
            ])
        )[0];
        if (!inputPointNames.length) {
            this.resetState();
            return;
        }

        // Add construction for P0 and P1 if they don't have names yet
        for (let i = 0; i < this.points.length; i++) {
            const p = this.points[i];
            if (!p.name) {
                p.name = inputPointNames[i];
                constructionPoints.push(buildPointConstruction(p.name, { x: p.coords[0], y: p.coords[1] }));
            }
        }

        const p0Name = this.points[0].name!; // P0 must have a name now
        const p1Name = this.points[1].name!; // P1 must have a name now
        const p2Name = inputPointNames[2]; // Name for the third vertex (selected preview point)

        const triangleName = `${p0Name}${p1Name}${p2Name}`;

        const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
        const v2 = [this.points[1].coords[0], this.points[1].coords[1], 0];
        const v3 = [vertex.coords[0], vertex.coords[1], 0]; // The chosen third point

        // Determine 'nth' based on which preview point was selected
        let nth: number;
        // Check if the selected vertex is the first or second point in the ordered previewPoints array
        if (
            this.previewPoints.length === 2 &&
            vertex.coords[0] === this.previewPoints[0].coords[0] &&
            vertex.coords[1] === this.previewPoints[0].coords[1]
        ) {
            // This corresponds to the first point in the ordered previewPoints array
            // Need to re-calculate nth based on the selected vertex relative to the base segment P0-P1
            const mainVector = [v2[0] - v1[0], v2[1] - v1[1]];
            nth = nthDirectionByLine(mainVector, v1, v3);
        } else if (
            this.previewPoints.length === 2 &&
            vertex.coords[0] === this.previewPoints[1].coords[0] &&
            vertex.coords[1] === this.previewPoints[1].coords[1]
        ) {
            // This corresponds to the second point in the ordered previewPoints array
            // Need to re-calculate nth based on the selected vertex relative to the base segment P0-P1
            const mainVector = [v2[0] - v1[0], v2[1] - v1[1]];
            nth = nthDirectionByLine(mainVector, v1, v3);
        } else {
            // Fallback or unexpected case, try to determine nth from the point geometry
            // This should ideally not be needed if selectThirdPointPosition works correctly
            const mainVector = [v2[0] - v1[0], v2[1] - v1[1]];
            nth = nthDirectionByLine(mainVector, v1, v3);
        }

        const constructionTriangle = this.buildTriangleFromTwoPositionConstruction(triangleName, v1, v2, nth);

        this.resetState(); // Clear state before async call

        await ctrl.editor.awarenessFeature.useAwareness(
            ctrl.viewport.id,
            'Đang tạo tam giác đều',
            buildDocumentAwarenessCmdOption(ctrl.editor.awarenessConstructId, ctrl),
            async () => {
                const constructResponse = await constructExec(() =>
                    this.editor.geoGateway.construct(docGlobalId, [
                        ...constructionPoints.map(c => <ConstructionRequest>{ construction: c }),
                        { construction: constructionTriangle },
                    ])
                );

                await syncRenderCommands(constructResponse.render, ctrl);
                addHistoryItemFromConstructionResponse(ctrl, constructResponse);
            }
        );
    }

    private onPointerMove(event: GeoPointerEvent) {
        if (this.points.length == 0) return;

        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCallback.bind(this));
        event.continue = false;
        event.nativeEvent.preventDefault();
    }

    private pointerMoveCallback(event: GeoPointerEvent) {
        handleIfPointerNotInError(this, () => {
            this.processPointerMove(event);
        });
    }

    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processPointerMove(event: GeoPointerEvent) {
        const { vertex, ctrl } = getPointAndVertex(this, event);

        if (this.isPointerDown) {
            if (!this.potentialSelectionDelegator.checkPotentialAreaAndClearIfOut(event)) return;

            // Update last point while dragging
            if (this.points.length === 1) {
                const lastIndex = this.points.length - 1;
                this.points[lastIndex] = vertex;

                const v1 = vertex.coords;
                this.previewTriangle(ctrl, [v1, v1]);
            } else if (this.points.length === 2 && this.previewPoints.length > 0) {
                // If we're in the third point selection phase, update selected preview point
                // when pointer moves while down
                this.selectThirdPointPosition(event, true);
            }
        } else {
            // Preview for next point
            if (this.points.length == 1) {
                const v1 = [this.points[0].coords[0], this.points[0].coords[1], 0];
                const v = vertex.coords;
                this.previewTriangle(ctrl, [v1, v]);
            } else if (this.points.length == 2 && this.previewPoints.length > 0) {
                this.selectThirdPointPosition(event, true);
            }
        }
    }

    private buildTriangleFromTwoPositionConstruction(
        triangleName: string,
        pos1: number[],
        pos2: number[],
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'EquilateralTriangle/EquilateralTriangleEC',
            'EquilateralTriangle',
            'FromTwoPosition'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-3DPoint',
                params: {
                    value: {
                        type: 'array',
                        values: pos2,
                    },
                },
            },
            {
                indexInCG: 2, // Corrected index to 2 as it's the 3rd parameter
                paramDefId: 'aValue', // Assuming 'aValue' is the correct paramDefId for the nth parameter
                optional: true,
                tplStrLangId: 'tpl-thShape', // Assuming this is the correct language ID
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];
        return construction;
    }

    private previewTriangle(ctrl: GeoDocCtrl, faces: number[][]) {
        const polygon: PreviewPolygon = {
            relIndex: -20, // Use a consistent negative index for the main preview shape
            name: '',
            type: 'RenderPolygon',
            elType: 'EquilateralTriangle', // Use specific element type if available
            faces,
            renderProp: buildPreviewPolygonRenderProp(),
            usable: true,
            valid: true,
            // Add unselectable if preview polygon shouldn't be picked by select tool
            unselectable: true,
        };
        syncPreviewCommands(polygon, ctrl);
    }
}
