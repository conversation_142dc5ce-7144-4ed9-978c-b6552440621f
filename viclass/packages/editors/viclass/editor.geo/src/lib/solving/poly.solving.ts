import { Complex } from './complex';

/**
 * Base class for polynomials of degree 2, 3 or 4. The polynomial equation
 * is of the form:
 * f(x) = a4.x^4 + a3.x^3 + a2.x^2 + a1.x + a0.x
 * where the coefficients a4, a3, a2, a1 and a0 are real numbers.
 *
 * There is a child class for each of the following degrees
 * 2 (quadratic) where a4 = 0, a3 = 0 and a2 <> 0
 * 3 (cubic) where a4 = 0 and a3 <> 0
 * 4 (quartic) where a4 <> 0
 *
 * Solving for f(x) = 0 always returns an array ofcomplex roots. The user
 * can filter the array to get just the real roots with
 * real_roots = Complex.filterRealRoots(all_roots);
 *
 * The real roots are represented as complex numbers with the imaginary
 * part being zero.
 *
 *
 * Research sources:
 * Solving a quartic function : https://mathworld.wolfram.com/QuarticEquation.html
 * Solving a cubic function : https://mathworld.wolfram.com/CubicFormula.html
 *
 * <AUTHOR> (2023)
 * <AUTHOR>
 */
export class PolyBase {
    protected cf: number[];
    protected all_roots: Complex[];

    constructor() {
        this.cf = [];
        this.all_roots = [];
    }

    /**
     * <PERSON><PERSON><PERSON> về lớp thích hợp dựa trên các hệ số.
     */
    static getPoly(coefs: number[]): PolyBase {
        while (coefs.length > 0 && coefs[0] === 0) {
            coefs.shift();
        }

        switch (coefs.length) {
            case 5:
                return new Quartic(coefs);
            case 4:
                return new Cubic(coefs);
            case 3:
                return new Quadratic(coefs);
            case 2:
                return new Linear(coefs);
            default:
                throw new Error('Invalid coefficients for a valid polynomial');
        }
    }

    /**
     * Returns an array of all roots as complex numbers. It does not
     * sort them or remove duplicates.
     */
    roots(): Complex[] {
        return [...this.all_roots];
    }

    /**
     * Đánh giá giá trị của đa thức tại một giá trị x.
     */
    eval(x: number | Complex): Complex {
        const X = typeof x === 'number' ? new Complex(x, 0) : x;
        let R = new Complex(this.cf[0], 0);
        let V = Complex.fromZ(X);

        for (let i = 1; i < this.cf.length; i++) {
            R = R.add(new Complex(this.cf[i], 0).mult(V));
            V = V.mult(X);
        }
        return R;
    }

    /**
     * Trả về biểu diễn chuỗi của đa thức.
     */
    toString(): string {
        const degree = this.cf.length - 1;
        let s = `x^${degree}`;

        for (let i = degree - 1; i >= 0; i--) {
            const n = this.cf[i];
            if (n !== 0) {
                s += `  ${n < 0 ? '-' : '+'} ${Math.abs(n)}`;
                if (i > 0) s += `.x`;
                if (i > 1) s += `^${i}`;
            }
        }
        return s;
    }

    print(): this {
        console.log(this.toString());
        return this;
    }
}

/**
 * Lớp dành cho phương trình bậc nhất.
 *
 * Phương trình có dạng:
 * a1.x + a0
 *
 * @tác giả Peter Lager (2023)
 */
export class Linear extends PolyBase {
    // Array of coefficients in term-power decending order i.e. [a1, a0]
    constructor(coefs: number[]) {
        super();
        if (!Array.isArray(coefs) || coefs.length !== 2 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for a linear equation');
        }
        const a1 = coefs[0];
        const a = [...coefs].reverse();

        for (let i = 0; i < a.length; i++) {
            a[i] /= a1;
        }

        this.cf = a;
        this.all_roots = this.__solve();
    }

    private __solve(): Complex[] {
        return [new Complex(-this.cf[0], 0)];
    }
}

/**
 * Lớp dành cho phương trình bậc hai.
 *
 * Phương trình có dạng:
 * a2.x^2 + a1.x + a0
 */
export class Quadratic extends PolyBase {
    // Array of coefficients in term-power decending order i.e.
    // [a2, a1, a0]
    constructor(coefs: number[]) {
        super();
        if (!Array.isArray(coefs) || coefs.length !== 3 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for a quadratic equation');
        }
        const a2 = coefs[0];
        const a = [...coefs].reverse();

        for (let i = 0; i < a.length; i++) {
            a[i] /= a2;
        }

        this.cf = a;
        this.all_roots = this.__solve();
    }

    private __solve(): Complex[] {
        const a1 = this.cf[1],
            a0 = this.cf[0];
        const t0 = a1 * a1 - 4 * a0,
            t1 = Math.sqrt(Math.abs(t0));
        const D = t0 >= 0 ? new Complex(t1, 0) : new Complex(0, t1);
        return [D.sub(a1).div(2), D.negate().sub(a1).div(2)];
    }
}

/**
 * Cubic equation solver.
 *
 * Solves the equation
 * a3.x^3 + a2.x^2 + a1.x + a0
 * Where all coefficients are real and a3 != 0
 *
 * Pass an array of the coefficients to the constructor e.g.
 * [a3, a2, a1, a0] an error will be thrown if there are not
 * exactly 4 elements or a3 == 0
 *
 * <AUTHOR> Lager (2023)
 */
class Cubic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;

    // Array of coefficients in term-power descending order i.e.
    // [a3, a2, a1, a0]
    constructor(coefs: number[]) {
        super();
        if (!Array.isArray(coefs) || coefs.length !== 4 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for a cubic equation');
        }

        const a3 = coefs[0];
        const a = [...coefs].reverse();

        // Calculate coefficients so that a3 = 1
        for (let i = 0; i < a.length; i++) {
            a[i] /= a3;
        }
        this.cf = a;
        this.all_roots = this.__solve();
    }

    // y^3 + d2.y^2 + d1.y + d0 where d2 == 0
    private _calcDepressedCubic(): void {
        this.SHIFT = new Complex(this.cf[2] / 3, 0);
        this.d = new Array(this.cf.length).fill(0);
        const a2 = this.cf[2];
        const a1 = this.cf[1];
        const a0 = this.cf[0];

        this.d[3] = 1;
        this.d[2] = 0;
        this.d[1] = (3 * a1 - a2 * a2) / 3;
        this.d[0] = -(9 * a1 * a2 - 27 * a0 - 2 * a2 * a2 * a2) / 27;
    }

    private __solve(): Complex[] {
        this._calcDepressedCubic();
        const roots: Complex[] = [];
        const S = this.SHIFT;

        if (this.d[1] === 0) {
            // solve x^3 + d0 = 0
            roots.push(new Complex(Math.cbrt(this.d[0]), 0).sub(S));
        } else {
            const R = new Complex(-this.d[0] / 2, 0);
            const Q = new Complex(this.d[1] / 3, 0);
            const W3 = R.sub(R.squared().add(Q.cubed()).sqrt());
            const Wroots = W3.roots(3);

            for (const root of Wroots) {
                roots.push(root.sub(Q.mult(root.pow(-1))).sub(S));
            }
        }

        return roots;
    }
}

/**
 * Quartic equation solver.
 *
 * Solves the equation
 * a4.x^4 + a3.x^3 + a2.x^2 + a1.x + a0
 * Where all coefficients are real and a4 != 0
 *
 * Pass an array of the coefficients to the constructor e.g.
 * [a4, a3, a2, a1, a0] an error will be thrown if there are not
 * exactly 5 elements or a4 == 0
 */
export class Quartic extends PolyBase {
    private d: number[];
    private SHIFT: Complex;

    // Array of coefficients in term-power descending order i.e.
    // [a4, a3, a2, a1, a0]
    constructor(coefs: number[]) {
        super();
        if (!Array.isArray(coefs) || coefs.length !== 5 || coefs[0] === 0) {
            throw new Error('Invalid coefficients for a quartic equation');
        }

        const a4 = coefs[0];
        const a = [...coefs].reverse();

        // Calculate coefficients so that a4 = 1
        for (let i = 0; i < a.length; i++) {
            a[i] /= a4;
        }
        this.cf = a;
        this.all_roots = this.__solve();
    }

    // y^4 + d3.y^3 + d2.y^2 + d1.y + d0 where d3 == 0
    private __calcDepressedQuartic(): void {
        // shift is the value to add to the roots of the depressed quartic
        // to get the roots to the original quartic
        this.SHIFT = new Complex(this.cf[3] / 4, 0);
        this.d = new Array(5);
        const b = this.cf[3];
        const c = this.cf[2];
        const d = this.cf[1];
        const e = this.cf[0];

        this.d[4] = 1;
        this.d[3] = 0;
        this.d[2] = (-3 * b * b) / 8 + c;
        this.d[1] = (b * b * b) / 8 - (b * c) / 2 + d;
        this.d[0] = -((3 * b * b * b * b) / 256) + (b * b * c) / 16 - (b * d) / 4 + e;
    }

    private __solve(): Complex[] {
        this.__calcDepressedQuartic();
        let roots: Complex[];

        if (this.d[1] === 0) {
            // solve x^4 + d2.x^2 + d0 = 0
            roots = this.__solveBiquadratic();
        } else {
            // solve x^4 + d2.x^2 + d1.x + d0 = 0
            roots = this.__solveGeneral();
        }

        return roots;
    }

    private __solveGeneral(): Complex[] {
        // Depressed quartic coefficients
        const d3 = this.d[3];
        const d2 = this.d[2];
        const d1 = this.d[1];
        const d0 = this.d[0];

        // Resolvant cubic of depressed quartic coefficients
        const r3 = 1;
        const r2 = -d2;
        const r1 = d1 * d3 - 4 * d0;
        const r0 = 4 * d2 * d0 - d1 * d1 - d3 * d3 * d0;

        // Solve resolvant cubic and find a real root
        const cubic = new Cubic([r3, r2, r1, r0]);
        const c_roots = cubic.roots();

        // Find the root with the smallest imaginary part i.e. min|imag|
        let y1 = 1e100;
        let _im_ = 1e100;

        for (const root of c_roots) {
            if (Math.abs(root.imag) < _im_) {
                y1 = root.real;
                _im_ = Math.abs(root.imag);
            }
        }

        // Get solutions to the depressed quartic equation then shift
        // these to get the solutions to the original quartic equation
        const t0 = y1 - d2;
        const t1 = Math.sqrt(Math.abs(t0));
        const R = t0 >= 0 ? new Complex(t1, 0) : new Complex(0, t1);
        let D: Complex, E: Complex;

        if (R.isZero()) {
            const t2 = y1 * y1 - 4 * d0;
            const t3 = Math.sqrt(Math.abs(t2));
            const T = t2 >= 0 ? new Complex(t3, 0) : new Complex(0, t3);
            D = T.sqrt()
                .mult(2)
                .sub(2 * d2);
            E = T.sqrt()
                .mult(2)
                .negate()
                .sub(2 * d2);
        } else {
            D = R.squared()
                .add(2 * d2)
                .negate()
                .add(R.pow(-1).mult(-2 * d1))
                .sqrt();
            E = R.squared()
                .add(2 * d2)
                .negate()
                .sub(R.pow(-1).mult(-2 * d1))
                .sqrt();
        }

        const roots: Complex[] = [];
        roots.push(R.add(D).div(2).sub(this.SHIFT));
        roots.push(R.sub(D).div(2).sub(this.SHIFT));
        roots.push(R.negate().add(E).div(2).sub(this.SHIFT));
        roots.push(R.negate().sub(E).div(2).sub(this.SHIFT));

        return roots;
    }

    private __solveBiquadratic(): Complex[] {
        const d2 = this.d[2];
        const d0 = this.d[0];
        const roots: Complex[] = [];

        if (d2 === 0) {
            // x^4 + d0 = 0
            const R = new Complex(-d0, 0).sqrt().sqrt();
            roots.push(R);
            roots.push(R.negate());
        } else {
            // x^4 + b.x^2 + c = 0
            const quad = new Quadratic([this.d[0], this.d[2], this.d[4]].reverse());
            for (const root of quad.roots()) {
                const r = root.sqrt();
                roots.push(r.sub(this.SHIFT));
                roots.push(r.negate().sub(this.SHIFT));
            }
        }

        return roots;
    }
}
