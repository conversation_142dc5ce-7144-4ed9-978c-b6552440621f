/**
 * Small utility class to hold the coefficients of the implicit equation
 * of an ellipse and for manipulating them.
 */
export class Coefficients {
    a: number;
    b: number;
    c: number;
    d: number;
    e: number;
    f: number;

    constructor(a: number, b: number, c: number, d: number, e: number, f: number) {
        this.a = a;
        this.b = b;
        this.c = c;
        this.d = d;
        this.e = e;
        this.f = f;
    }

    static elliminateTerm(s: Coefficients, t: Coefficients, coef: string): Coefficients | undefined {
        const _s = s.getCoef(coef);
        const _t = t.getCoef(coef);

        if (Number.isFinite(_s) && Number.isFinite(_t)) {
            s = s.mult(_t);
            t = t.mult(_s);
            const u = s.subtract(t);
            return u;
        }
        return undefined;
    }

    copy(): Coefficients {
        return new Coefficients(this.a, this.b, this.c, this.d, this.e, this.f);
    }

    getCoef(coef: string): number {
        return <number>this[coef as keyof Coefficients];
    }

    divide(n: number): Coefficients {
        if (Number.isFinite(n)) {
            return new Coefficients(this.a / n, this.b / n, this.c / n, this.d / n, this.e / n, this.f / n);
        }
        return this.copy();
    }

    mult(n: number): Coefficients {
        if (Number.isFinite(n)) {
            return new Coefficients(this.a * n, this.b * n, this.c * n, this.d * n, this.e * n, this.f * n);
        }
        return this.copy();
    }

    subtract(n: Coefficients | undefined): Coefficients {
        if (n) {
            return new Coefficients(this.a - n.a, this.b - n.b, this.c - n.c, this.d - n.d, this.e - n.e, this.f - n.f);
        }
        return this.copy();
    }

    add(n: Coefficients | undefined): Coefficients {
        if (n) {
            return new Coefficients(this.a + n.a, this.b + n.b, this.c + n.c, this.d + n.d, this.e + n.e, this.f + n.f);
        }
        return this.copy();
    }

    print(): this {
        console.log(`Coefs [ a: ${this.a}   b: ${this.b}   c: ${this.c}   d: ${this.d}   e: ${this.e}   f: ${this.f}]`);
        return this;
    }
}
