import {
    BoardViewportManager,
    BoundaryRectangle,
    BoundedGraphicLayerCtrl,
    DefaultVDocCtrl,
    HasBoundaryCtrl,
    LocatableEvent,
    mouseLocation,
} from '@viclass/editor.core';

import { BehaviorSubject, Observable } from 'rxjs';
import { GeometryEditor } from '../geo.editor';
import { DefaultGeoRenderProp, DocRenderProp, GeoRenderDocState, GeoRenderElement } from '../model';
import { GeoDoc, GeoLayer } from '../model/geo.models';
import { Geo2dRenderer, GeoRenderer } from '../renderer';
import { calculatePosInLayer, validatePointerPos } from '../tools/tool.utils';
import { checkHitOnElementsBoundary2D, checkHitOnElementsInside2D, GeoSelectHitContext } from './index';

export class GeoDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    rendererCtrl: GeoRenderer;

    private readonly _docRenderProp$: BehaviorSubject<DocRenderProp>;

    private readonly _selectedElements$: BehaviorSubject<GeoRenderElement[]>;
    public readonly selectedElements$: Observable<GeoRenderElement[]>;
    get selectedElements() {
        return this._selectedElements$.getValue();
    }

    private readonly _docDefaultElRenderProps$: BehaviorSubject<DefaultGeoRenderProp>;

    constructor(
        public override editor: GeometryEditor,
        public override state: GeoDoc,
        public override viewport: BoardViewportManager
    ) {
        super(state, editor, viewport);

        this._docRenderProp$ = new BehaviorSubject<DocRenderProp>(state.docRenderProp);

        this._docDefaultElRenderProps$ = new BehaviorSubject<DefaultGeoRenderProp>(state.docDefaultElRenderProps);

        this._selectedElements$ = new BehaviorSubject<GeoRenderElement[]>([]);
        this.selectedElements$ = this._selectedElements$.asObservable();
    }

    get docRenderProp$(): Observable<DocRenderProp> {
        return this._docRenderProp$;
    }

    get docDefaultElRenderProps$(): Observable<DefaultGeoRenderProp> {
        return this._docDefaultElRenderProps$;
    }

    updateSelectedElements(sel: GeoRenderElement[]) {
        this._selectedElements$.next(sel);
    }

    updateDocRenderProp(docRenderProp: DocRenderProp) {
        this.state.docRenderProp = docRenderProp;
        this._docRenderProp$.next(this.state.docRenderProp);
    }

    updateDocDefaultElRenderProps(docDefaultElRenderProps: DefaultGeoRenderProp) {
        this.state.docDefaultElRenderProps = docDefaultElRenderProps;
        this._docDefaultElRenderProps$.next(this.state.docDefaultElRenderProps);
    }

    clearUnusableObject() {
        this.rendererCtrl?.clearUnusableObject();
    }

    attachRenderer(r: GeoRenderer) {
        this.rendererCtrl = r;
        this.layers.push(r.layer);
        this.state.layers.push(r.layer.state as GeoLayer);
    }

    onRemove() {
        const idx = this.layers.indexOf(this.rendererCtrl.layer);
        this.state.layers.splice(idx, 1);
        this.layers.splice(idx, 1);
        this.rendererCtrl.onRemoved();
        this.viewport.removeLayer(this.rendererCtrl.layer);
    }

    // TODO: this it checking implementation is a slow implementation
    // upgrade to using quad tree or something
    checkHit(
        event: LocatableEvent<any>,
        l: BoundedGraphicLayerCtrl,
        useRelaxedHitPrecision = false
    ): GeoSelectHitContext {
        const renderer = this.rendererCtrl as Geo2dRenderer;

        const mousePos = mouseLocation(event);
        if (!validatePointerPos(mousePos, this)) return undefined;

        if (renderer.layer === l) {
            // mouse position & type
            const mousePosInLayer = calculatePosInLayer(mousePos, this);
            const mousePosInGeo = renderer.layerToGeoPos(mousePosInLayer);
            const isTouchEvent = event.nativeEvent['pointerType'] == 'touch';

            return (
                checkHitOnElementsInside2D(this, renderer.angleElements(), mousePosInGeo, renderer)[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.pointElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.lineElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.sectorElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.circleElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.ellipseElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsBoundary2D(
                    this,
                    renderer.shapeElements(),
                    mousePosInGeo,
                    renderer,
                    isTouchEvent,
                    useRelaxedHitPrecision
                )[0] ??
                checkHitOnElementsInside2D(this, renderer.shapeElements(), mousePosInGeo, renderer)[0] ?? {
                    doc: this,
                    hitDetails: undefined,
                }
            );
        }

        return undefined;
    }

    checkHitInternal(
        event: LocatableEvent<any>,
        l: BoundedGraphicLayerCtrl,
        useRelaxedHitPrecision = false
    ): GeoSelectHitContext {
        return this.checkHit(event, l, useRelaxedHitPrecision);
    }

    unselect() {
        if (this.layers[0]) this.viewport.sink(this.layers[0]);
    }

    select() {
        if (this.layers[0]) this.viewport.float(this.layers[0]);
    }

    destroy() {}

    updateBoundary(boundary: BoundaryRectangle) {
        for (let i = 0; i < this.state.layers.length; i++) {
            // update layer state
            const layer = this.layers[i];
            this.state.layers[i].boundary = boundary; // update all layers state

            if (this.isBoundedView())
                // in full mode, the layer is an unbounded graphic layer
                (layer as BoundedGraphicLayerCtrl).updateBoundary(boundary);
        }
    }

    buildGeoRenderDocState(): GeoRenderDocState {
        return {
            docId: this.state.globalId,
            canvasWidth: this.rendererCtrl.width,
            canvasHeight: this.rendererCtrl.height,
            numDim: this.state.kind,
            ...this.state.docRenderProp,
        } as GeoRenderDocState;
    }

    isBoundedView() {
        return this.editor.geoEditorConf.docViewMode == 'bounded';
    }

    getBoundary(): BoundaryRectangle {
        return this.state.layers?.[0]?.boundary;
    }
}
