import { ToolState } from '@viclass/editor.core';
import { WordTool, WordToolType } from '.';
import { FormatType } from '../model';

export class FormatTextTool extends WordTool<ToolState, any> {
    readonly toolType: WordToolType = 'FormatTextTool';
    override toolState: ToolState;

    bold() {
        this.formatText('bold');
    }

    italic() {
        this.formatText('italic');
    }

    underline() {
        this.formatText('underline');
    }

    code() {
        this.formatText('code');
    }

    strikethrough() {
        this.formatText('strikethrough');
    }

    highlight() {
        this.formatText('highlight');
    }

    subscript() {
        this.formatText('subscript');
    }

    superscript() {
        this.formatText('superscript');
    }

    formatText(formatType: FormatType) {
        this.executeInFocusedDocCtrl(doc => doc.formatText(formatType));
    }
}
