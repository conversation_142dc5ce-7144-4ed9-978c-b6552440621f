import {
    <PERSON><PERSON><PERSON>,
    CoordinatorEventType,
    DefaultToolBar,
    KeyboardEventData,
    KeyboardEventListener,
    MouseEventData,
    MouseEventListener,
    NativeEventTarget,
    newCursor,
    PointerEventListener,
    ToolState,
    VEventListener,
    ViewportDisableCES,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordEditor } from '../word.editor';
import { WordDocEvent, WordDocFocusedES } from '../word.models';
import { WordToolType } from './models';
import { WordContextTool } from './word.context.tool';
import { WordSettingsTool } from './word.settings.tool';
import { WordTableTool } from './word.table.tool';
import { WordTool } from './word.tool';

export class WordToolBar extends DefaultToolBar<WordToolType, WordTool<ToolState, any>> {
    override keyboardHandler: KeyboardEventListener<NativeEventTarget<any>>;
    override mouseHandler: MouseEventListener<NativeEventTarget<any>>;
    override pointerHandler?: PointerEventListener<NativeEventTarget<any>>;

    wordDocEventListener: VEventListener<WordDocEvent>;

    constructor(private editor: WordEditor) {
        super(editor.coordinator);
        this.wordDocEventListener = new this.WordDocEventListener(this);

        this.keyboardHandler = new this._keyboardHandler(this);
        this.mouseHandler = new this._mouseHandler();
        this.mouseHandling = [];
    }

    protected override async onViewportModeChanged(vpMode: ViewportMode): Promise<void> {
        if (vpMode == 'Disabled') this.disable();
        else this.enable();

        this.tools.forEach((tool, type) => {
            switch (vpMode) {
                case 'EditMode': {
                    this.enableTool(type);
                    break;
                }
                case 'InteractiveMode': {
                    switch (type) {
                        case 'SubEditorManagerTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                        }
                    }
                    break;
                }
                case 'ViewMode': {
                    switch (type) {
                        case 'SubEditorManagerTool': {
                            this.enableTool(type);
                            break;
                        }
                        default: {
                            this.disableTool(type);
                        }
                    }
                    break;
                }
                case 'Disabled': {
                    this.disableTool(type);
                    break;
                }
            }
        });
    }

    override attachViewport(viewport: ViewportManager): void {
        super.attachViewport(viewport);
        this.editor.toolbars.set(viewport.id, this);
        this.editor.selectDelegator.registerDocEventListener(this.wordDocEventListener);
    }

    override detachViewport(viewport: ViewportManager): void {
        super.detachViewport(viewport);
        if (this.wordDocEventListener) {
            this.editor.selectDelegator.unregisterDocEventListener(this.wordDocEventListener);
        }
        this.editor.toolbars.delete(viewport.id);
    }

    protected override generateCoordEventListener(): VEventListener<CoordinatorEvent> {
        return new (class implements VEventListener<CoordinatorEvent> {
            constructor(private toolbar: WordToolBar) {}

            onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
                const eType = eventData.eventType as CoordinatorEventType;
                const eventState = eventData.state;

                if (!this.toolbar.viewport || eventState.vmId != this.toolbar.viewport.id) return eventData;

                switch (eType) {
                    case 'viewport-edit-mode': {
                        this.toolbar.onViewportModeChanged('EditMode');
                        break;
                    }
                    case 'viewport-interactive-mode': {
                        this.toolbar.onViewportModeChanged('InteractiveMode');
                        break;
                    }
                    case 'viewport-view-mode': {
                        this.toolbar.onViewportModeChanged('ViewMode');
                        break;
                    }
                    case 'viewport-disabled': {
                        const evs = eventState as ViewportDisableCES;
                        this.toolbar.editor.selectDelegator.onViewportDisabled(eventData.state.vmId, evs);
                        this.toolbar.onViewportModeChanged('Disabled');
                        break;
                    }
                    case 'viewport-removed': {
                        this.toolbar.editor.selectDelegator.onViewportRemoved(eventData.state.vmId);
                        break;
                    }
                }

                return eventData;
            }
        })(this);
    }

    override addTool(toolType: WordToolType, tool: WordTool<ToolState, any>): void {
        super.addTool(toolType, tool);
        tool.registerToolbar(this);
    }

    private WordDocEventListener = class implements VEventListener<WordDocEvent> {
        constructor(private toolbar: WordToolBar) {}

        onEvent(eventData: WordDocEvent): WordDocEvent | Promise<WordDocEvent> {
            if (this.toolbar.isDisabled()) return eventData;

            if (!this.toolbar.viewport || eventData.state.vm.id != this.toolbar.viewport.id) return eventData;

            switch (eventData.eventType) {
                case 'doc-focused': {
                    const { vm, docCtrl } = eventData.state as WordDocFocusedES;
                    vm.eventManager.captureAllKeyboardEvent(
                        docCtrl.docEditor.lexical.getRootElement(),
                        this.toolbar.keyboardHandler
                    );

                    if (!this.toolbar.isToolDisable('WordSettingsTool')) {
                        const tool = this.toolbar.getTool('WordSettingsTool') as WordSettingsTool;
                        tool.loadSettings();
                    }

                    if (!this.toolbar.isToolDisable('WordContextTool')) {
                        const tool = this.toolbar.getTool('WordContextTool') as WordContextTool;
                        tool.onDocAttached(docCtrl);
                    }
                    if (!this.toolbar.isToolDisable('WordTableTool')) {
                        const tool = this.toolbar.getTool('WordTableTool') as WordTableTool;
                        tool.onDocAttached(docCtrl);
                    }

                    this.toolbar.blur('CreateWordDocumentTool');
                    break;
                }
                case 'doc-unfocused': {
                    const { vm, docCtrl } = eventData.state as WordDocFocusedES;
                    vm.eventManager.unCaptureAllKeyboardEvent(docCtrl.docEditor.lexical.getRootElement());

                    if (!this.toolbar.isToolDisable('WordSettingsTool')) {
                        const tool = this.toolbar.getTool('WordSettingsTool') as WordSettingsTool;
                        tool.loadSettings();
                    }

                    if (!this.toolbar.isToolDisable('WordContextTool')) {
                        const tool = this.toolbar.getTool('WordContextTool') as WordContextTool;
                        tool.onDocDetached(docCtrl);
                    }
                    if (!this.toolbar.isToolDisable('WordTableTool')) {
                        const tool = this.toolbar.getTool('WordTableTool') as WordTableTool;
                        tool.onDocDetached(docCtrl);
                    }

                    docCtrl.unselect();
                    break;
                }
            }

            return eventData;
        }
    };

    private _keyboardHandler = class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(private toolbar: WordToolBar) {}

        onEvent(
            event: KeyboardEventData<NativeEventTarget<any>>
        ): KeyboardEventData<NativeEventTarget<any>> | Promise<KeyboardEventData<NativeEventTarget<any>>> {
            if (this.toolbar.isDisabled()) return event;

            const vm = this.toolbar.viewport;
            if (vm) {
                const focusedDocCtrls: WordDocCtrl[] = this.toolbar.editor.selectDelegator.getFocusedDocs(vm.id) || [];
                if (focusedDocCtrls.length === 1 && focusedDocCtrls[0]) {
                    return focusedDocCtrls[0].keyboardListener.onEvent(event);
                }
            }

            return event;
        }
    };

    private _mouseHandler = class implements MouseEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject([newCursor('auto', 10, '#fff', 0, 'system')]);

        onEvent(event: MouseEventData<NativeEventTarget<any>>) {
            return event;
        }
    };
}
