import { DocLocalId, DocMappingLDEData, HistoryItem, SupportFeatureHistory, ViewportId } from '@viclass/editor.core';

import { WordDoc } from '../model';

export type HistoryItemType = 'paste-doc' | 'render-elements' | 'word-editing' | 'subviewport-editing';

export class WordHistoryItem implements HistoryItem {
    constructor(supporter: SupportFeatureHistory) {
        this.supporter = supporter;
    }
    supporter: SupportFeatureHistory;
    type: HistoryItemType;

    viewportId: ViewportId;
    docId: DocLocalId;

    // document state
    docState?: WordDoc;

    docsMappingLDEData?: DocMappingLDEData[];
}

/**
 * Wrapper around a history item of a sub-viewport inside word document
 */
export class WordSubViewportHistoryItem extends WordHistoryItem {
    constructor(
        supporter: SupportFeatureHistory,
        public originalItem: HistoryItem
    ) {
        super(supporter);
        this.type = 'subviewport-editing';
    }
}
