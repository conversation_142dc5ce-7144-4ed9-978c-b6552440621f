import { DRAG_DROP_PASTE } from '@lexical/rich-text';
import { isMimeType, mediaFileReader } from '@lexical/utils';
import { ImageProcessOptions } from '@viclass/editor.core';
import { COMMAND_PRIORITY_LOW } from 'lexical';
import { INSERT_BLOB_IMAGE_COMMAND, INSERT_IMAGE_COMMAND } from '../image-plugin/image.plugin';
import { WordPlugin } from '../word.plugin';

const ACCEPTABLE_IMAGE_TYPES = ['image/', 'image/heic', 'image/heif', 'image/gif', 'image/webp'];

export class DragDropPastePlugin extends WordPlugin {
    override init(): void {
        this.addUnsubscribe(
            this.lexical.registerCommand(
                DRAG_DROP_PASTE,
                files => {
                    (async () => {
                        const filesResult = await mediaFileReader(
                            files,
                            [ACCEPTABLE_IMAGE_TYPES].flatMap(x => x)
                        );
                        for (const { file } of filesResult) {
                            if (isMimeType(file, ACCEPTABLE_IMAGE_TYPES)) {
                                const editor = this.docCtrl?.editor;
                                if (file.size <= 100 * 1024) {
                                    // If size is less than 100KB -> save directly as base64
                                    const processedFile: Blob =
                                        await editor.attachmentFeature.processFile<ImageProcessOptions>(
                                            editor,
                                            file,
                                            file.type,
                                            { maxSize: 100 * 1024 }
                                        );
                                    const base64Img = await convertBlobToBase64(processedFile);
                                    this.lexical.dispatchCommand(INSERT_IMAGE_COMMAND, {
                                        altText: file.name,
                                        src: base64Img,
                                    });
                                } else {
                                    this.lexical.dispatchCommand(INSERT_BLOB_IMAGE_COMMAND, {
                                        altText: file.name,
                                        blob: file,
                                        type: file.type,
                                    });
                                }
                            }
                        }
                    })();
                    return true;
                },
                COMMAND_PRIORITY_LOW
            )
        );
    }
}

// Helper function to convert Blob/File to base64
async function convertBlobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}
