/**
 * Extracted from https://github.com/yjs/y-websocket
 * exclude the auth protocol as we don't need it
 */

import * as decoding from 'lib0/decoding';
import * as encoding from 'lib0/encoding';
import * as awarenessProtocol from 'y-protocols/awareness';
import * as syncProtocol from 'y-protocols/sync';

export const messageSync = 0;
export const messageQueryAwareness = 3;
export const messageAwareness = 1;

export const MESSAGE_TYPES = {
    sync: 'sync',
    awareness: 'awareness',
    queryAwareness: 'queryAwareness',
};

export type MessageHandler = (
    encoder: encoding.Encoder,
    decoder: decoding.Decoder,
    provider: any,
    emitSynced: boolean,
    messageType: number
) => void;

/**
 *                       encoder,          decoder,          provider,          emitSynced, messageType
 * @type {Array<function(encoding.Encoder, decoding.Decoder, WebsocketProvider, boolean,    number):void>}
 */
export const messageHandlers: MessageHandler[] = [];

messageHandlers[messageSync] = (encoder, decoder, provider, emitSynced, _messageType) => {
    encoding.writeVarUint(encoder, messageSync);
    const syncMessageType = syncProtocol.readSyncMessage(decoder, encoder, provider.doc, provider);
    if (emitSynced && syncMessageType === syncProtocol.messageYjsSyncStep2 && !provider.synced) {
        provider.synced = true;
    }
};

messageHandlers[messageQueryAwareness] = (encoder, _decoder, provider, _emitSynced, _messageType) => {
    encoding.writeVarUint(encoder, messageAwareness);
    encoding.writeVarUint8Array(
        encoder,
        awarenessProtocol.encodeAwarenessUpdate(provider.awareness, Array.from(provider.awareness.getStates().keys()))
    );
};

messageHandlers[messageAwareness] = (_encoder, decoder, provider, _emitSynced, _messageType) => {
    awarenessProtocol.applyAwarenessUpdate(provider.awareness, decoding.readVarUint8Array(decoder), provider);
};
