import { $getNearestBlockElementAncestorOrThrow, mergeRegister } from '@lexical/utils';
import { $isDecoratorBlockNode } from '@viclass/editor.word.transform';
import {
    $getNodeByKey,
    $getSelection,
    $isDecoratorNode,
    $isNodeSelection,
    $isRangeSelection,
    CLICK_COMMAND,
    COMMAND_PRIORITY_LOW,
    ElementFormatType,
    FORMAT_ELEMENT_COMMAND,
    KEY_BACKSPACE_COMMAND,
    KEY_DELETE_COMMAND,
    LexicalEditor,
    NodeKey,
} from 'lexical';
import { NodeSelectionUtils } from './node.selection.utils';

export type NodeClassName = Readonly<{
    base: string;
    focus: string;
}>;

export function createAlignableNode(
    alignableNode: HTMLElement,
    lexical: LexicalEditor,
    nodeKey: NodeKey,
    selectionUtils: NodeSelectionUtils,
    defaultAlign: ElementFormatType = 'left',
    className: NodeClassName = {
        base: '',
        focus: '',
    }
): () => void {
    const teardownFns: (() => void)[] = [];

    alignableNode.classList.add('alignable');
    if (className.base) {
        alignableNode.classList.add(className.base);
    }
    alignableNode.style.textAlign = defaultAlign;

    // handle format element
    teardownFns.push(
        lexical.registerCommand<ElementFormatType>(
            FORMAT_ELEMENT_COMMAND,
            formatType => {
                if (!selectionUtils.isSelected) return false;

                const selection = $getSelection();

                if ($isNodeSelection(selection)) {
                    const node = $getNodeByKey(nodeKey);

                    if ($isDecoratorBlockNode(node)) {
                        node.setFormat(formatType);
                    }
                } else if ($isRangeSelection(selection)) {
                    const nodes = selection.getNodes();

                    for (const node of nodes) {
                        if ($isDecoratorBlockNode(node)) {
                            node.setFormat(formatType);
                        } else {
                            const element = $getNearestBlockElementAncestorOrThrow(node);
                            element.setFormat(formatType);
                        }
                    }
                }

                alignableNode.style.textAlign = formatType;
                return true;
            },
            COMMAND_PRIORITY_LOW
        )
    );

    // handle click
    teardownFns.push(
        lexical.registerCommand(
            CLICK_COMMAND,
            event => {
                if (event.target === alignableNode) {
                    event.preventDefault();
                    if (!event.shiftKey) {
                        selectionUtils.clearSelection();
                    }

                    selectionUtils.setSelected(!selectionUtils.isSelected);
                    return true;
                }
                return false;
            },
            COMMAND_PRIORITY_LOW
        )
    );

    // watch node focus
    const focusSubscription = selectionUtils.isSelected$.subscribe(selected => {
        if (!className.focus) return;
        if (selected) {
            alignableNode.classList.add(className.focus);
        } else {
            alignableNode.classList.remove(className.focus);
        }
    });
    teardownFns.push(() => focusSubscription.unsubscribe());

    // handle node deletion
    const onDelete = (event: KeyboardEvent) => {
        if (selectionUtils.isSelected$.value && $isNodeSelection($getSelection())) {
            event.preventDefault();
            const node = $getNodeByKey(nodeKey);
            if ($isDecoratorNode(node)) {
                node.remove();
                return true;
            }
        }

        return false;
    };

    teardownFns.push(
        mergeRegister(
            lexical.registerCommand(KEY_BACKSPACE_COMMAND, onDelete, COMMAND_PRIORITY_LOW),
            lexical.registerCommand(KEY_DELETE_COMMAND, onDelete, COMMAND_PRIORITY_LOW)
        )
    );

    return () => teardownFns.forEach(fn => fn());
}
