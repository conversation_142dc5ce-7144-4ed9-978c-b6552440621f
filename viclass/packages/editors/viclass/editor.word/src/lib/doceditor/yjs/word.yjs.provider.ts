import { Provider, ProviderAwareness } from '@lexical/yjs';
import { reliableCmdMeta, reliableSaveCmdMeta } from '@viclass/editor.core';
import { WordCmdTypeProto } from '@viclass/proto/editor.word';
import * as decoding from 'lib0/decoding';
import * as encoding from 'lib0/encoding';
import { ObservableV2 } from 'lib0/observable';
import { debounceTime, Subject, tap } from 'rxjs';
import * as awarenessProtocol from 'y-protocols/awareness';
import * as syncProtocol from 'y-protocols/sync';
import * as Y from 'yjs';
import { WordEditor } from '../..';
import { SyncLexicalAwarenessUpdateCmd, SyncLexicalUpdateCmd } from '../../cmds/word.cmd';
import { WordDocCtrl } from '../../docs/word.doc.ctrl';
import { randomUint32, splitUint8Array } from '../../word.utils';
import { FlushableTimeBuffer } from './flush.buffer';
import { messageAwareness, MessageHandler, messageHandlers, messageSync } from './message.handler';

const UPDATE_BUFFER_TIME_MS = 300;
const MISSING_UPDATE_DETECT_TIME_MS = 5000;

// WebRTC allow message of maximum 64KB so we take 60 and spare 4KB for other data
// https://github.com/pion/webrtc/issues/1396
const BUFFER_SAFE_SIZE = 60 * 1024;

export type LexicalYjsEvents = {
    sync: (isSynced: boolean) => void;
    status: (arg0: { status: string }) => void;
    update: (arg0: Uint8Array) => void;
    reload: (doc: Y.Doc) => void;
};

export class WordYjsProvider extends ObservableV2<LexicalYjsEvents> implements Provider {
    synced: boolean = false;

    awareness: ProviderAwareness;

    messageHandlers: MessageHandler[] = messageHandlers.slice();

    nextUpdateAwareness?: string;

    private readonly receivedUpdates$ = new Subject<void>();
    private missingDetectorSubsc = this.receivedUpdates$
        .pipe(
            debounceTime(MISSING_UPDATE_DETECT_TIME_MS),
            tap(() => {
                const isMissing = this.detectMissingUpdates();

                if (isMissing) {
                    console.log('Missing update detected');
                    this.emit('reload', [this.doc]);
                }
            })
        )
        .subscribe();

    private readonly updatesBuffer = new FlushableTimeBuffer<Uint8Array>(
        this.onFlushUpdates.bind(this),
        UPDATE_BUFFER_TIME_MS
    );

    constructor(
        public doc: Y.Doc,
        private editor: WordEditor,
        public docCtrl: WordDocCtrl
    ) {
        super();
        this.awareness = new awarenessProtocol.Awareness(doc) as unknown as ProviderAwareness;

        // listen to an event that fires when a remote update is received
        this.on('update', update => {
            Y.applyUpdateV2(doc, update, this); // the third parameter sets the transaction-origin
        });
    }

    connect(): void | Promise<void> {
        this.doc.on('update', this.updateHandler);
        // @ts-expect-error expected miss match between the declaration of lexical vs yjs
        this.awareness.on('update', this.awarenessUpdateHandler);
    }

    disconnect(): void {
        this.doc.off('update', this.updateHandler);
        // @ts-expect-error expected miss match between the declaration of lexical vs yjs
        this.awareness.off('update', this.awarenessUpdateHandler);
    }

    override destroy(): void {
        this.missingDetectorSubsc.unsubscribe();
        this.updatesBuffer.destroy();
    }

    readMessage = (buf: Uint8Array, emitSynced: boolean = true) => {
        const decoder = decoding.createDecoder(buf);
        const encoder = encoding.createEncoder();
        const messageType = decoding.readVarUint(decoder);
        const messageHandler = this.messageHandlers[messageType];
        if (messageHandler) {
            messageHandler(encoder, decoder, this, emitSynced, messageType);
        } else {
            console.error('Unable to compute message');
            return;
        }

        if (messageType === messageSync) {
            this.receivedUpdates$.next();
        }
    };

    detectMissingUpdates = (): boolean => {
        const update = Y.encodeStateAsUpdate(this.doc);
        const { structs } = Y.decodeUpdate(update);

        // find if missing update in the structs
        return !!structs.find(struct => struct instanceof Y.Skip);
    };

    updateHandler = (update: Uint8Array, origin: any) => {
        // ignore updates applied by this provider
        if (origin !== this) {
            // this update was produced either locally or by another provider
            this.updatesBuffer.addUpdate(update);
        }
    };

    awarenessUpdateHandler = ({ added, updated, removed }) => {
        const changedClients = added.concat(updated).concat(removed);
        const encoder = encoding.createEncoder();
        encoding.writeVarUint(encoder, messageAwareness);
        encoding.writeVarUint8Array(
            encoder,
            awarenessProtocol.encodeAwarenessUpdate(
                this.awareness as unknown as awarenessProtocol.Awareness,
                changedClients
            )
        );
        this.broadcastAwarenessMessage(encoding.toUint8Array(encoder));
    };

    public async markNextUpdateWithAwareness(awarenessId: string) {
        if (!awarenessId) return;

        await this.forceFlush();
        this.nextUpdateAwareness = awarenessId;
    }

    public forceFlush(): Promise<void> {
        return this.updatesBuffer.forceFlush();
    }

    public waitNextAndFlush(): Promise<void> {
        return this.updatesBuffer.waitNextAndFlush();
    }

    public pauseSendingWhile<T>(waitCallback: () => Promise<T>): Promise<T> {
        return this.updatesBuffer.pauseWhile(waitCallback);
    }

    /**
     * to be called from the updates buffer
     */
    private async onFlushUpdates(updates: Uint8Array[], force: boolean): Promise<void> {
        if (updates.length === 0) {
            if (force) {
                // broadcast empty update with force flag to force lexical discrete update
                await this.broadcastUpdateMessage(undefined, true);
            }
            return;
        }

        const accumulated = Y.mergeUpdates(updates);
        const encoder = encoding.createEncoder();
        encoding.writeVarUint(encoder, messageSync);
        syncProtocol.writeUpdate(encoder, accumulated);

        await this.broadcastUpdateMessage(encoding.toUint8Array(encoder), force);
    }

    private async broadcastUpdateMessage(buf: Uint8Array | undefined, force: boolean) {
        if (buf?.length > BUFFER_SAFE_SIZE) {
            this.broadcastUpdateMessageByChunks(buf, force);
            return;
        }

        const meta = reliableSaveCmdMeta(
            this.docCtrl.viewport,
            this.docCtrl.state,
            this.docCtrl.state.id,
            this.docCtrl.state.id,
            WordCmdTypeProto.SYNC_LEXICAL_UPDATE
        );
        const cmd = new SyncLexicalUpdateCmd(meta);
        if (buf) {
            cmd.setUpdate(buf);
        }
        cmd.setYClientId(this.doc.clientID);
        cmd.setForceUpdate(force);
        cmd.setAwarenessId(this.nextUpdateAwareness);

        if (this.nextUpdateAwareness) {
            this.nextUpdateAwareness = undefined;
        }

        await this.editor.sendCommand(cmd);
    }

    private async broadcastUpdateMessageByChunks(buf: Uint8Array, force: boolean) {
        const chunks = splitUint8Array(buf, BUFFER_SAFE_SIZE);
        const partialId = randomUint32();

        const promises: Promise<void>[] = [];

        for (let i = 0; i < chunks.length; i++) {
            const meta = reliableSaveCmdMeta(
                this.docCtrl.viewport,
                this.docCtrl.state,
                this.docCtrl.state.id,
                this.docCtrl.state.id,
                WordCmdTypeProto.SYNC_LEXICAL_UPDATE
            );
            const cmd = new SyncLexicalUpdateCmd(meta);

            cmd.setPartialId(partialId);
            cmd.setUpdate(chunks[i]);
            cmd.setTotalChunks(chunks.length);
            cmd.setChunkIndex(i);

            cmd.setYClientId(this.doc.clientID);
            cmd.setForceUpdate(force);
            cmd.setAwarenessId(this.nextUpdateAwareness);

            promises.push(this.editor.sendCommand(cmd));
        }

        if (this.nextUpdateAwareness) {
            this.nextUpdateAwareness = undefined;
        }

        await Promise.all(promises);
    }

    private async broadcastAwarenessMessage(buf: Uint8Array) {
        const meta = reliableCmdMeta(
            this.docCtrl.viewport,
            this.docCtrl.state.id,
            this.docCtrl.state.id,
            WordCmdTypeProto.SYNC_LEXICAL_AWARENESS
        );
        const cmd = new SyncLexicalAwarenessUpdateCmd(meta);
        cmd.setUpdate(buf);
        cmd.setYClientId(this.doc.clientID);
        await this.editor.sendCommand(cmd);
    }
}

export default WordYjsProvider;
