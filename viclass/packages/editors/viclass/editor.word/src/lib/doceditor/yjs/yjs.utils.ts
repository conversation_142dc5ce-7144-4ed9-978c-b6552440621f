import { mergeRegister } from '@lexical/utils';
import { initLocalState, Provider, setLocalStateFocus } from '@lexical/yjs';
import { BLUR_COMMAND, COMMAND_PRIORITY_EDITOR, FOCUS_COMMAND, LexicalEditor } from 'lexical';

function hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = (hash << 5) - hash + str.charCodeAt(i);
        hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
}

export function getCursorColor(username?: string) {
    const hue = username
        ? hashString(username) % 360 // use hash value for consistent color
        : Math.random() * 360; // no username -> random color
    return `hsl(${hue}, 100%, 40%)`;
}

export function useYjsFocusTracking(
    editor: LexicalEditor,
    provider: Provider,
    name: string,
    color: string,
    awarenessData?: object
) {
    initLocalState(provider, name, color, document.activeElement === editor.getRootElement(), awarenessData || {});

    return mergeRegister(
        editor.registerCommand(
            FOCUS_COMMAND,
            () => {
                setLocalStateFocus(provider, name, color, true, awarenessData || {});
                return false;
            },
            COMMAND_PRIORITY_EDITOR
        ),
        editor.registerCommand(
            BLUR_COMMAND,
            () => {
                setLocalStateFocus(provider, name, color, false, awarenessData || {});
                return false;
            },
            COMMAND_PRIORITY_EDITOR
        )
    );
}
