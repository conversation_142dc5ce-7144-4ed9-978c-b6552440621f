import { DocFocusedES, FocusDocEvent } from '@viclass/editor.core';
import { WordDocCtrl } from './docs/word.doc.ctrl';

export type WordDocFocusedES = DocFocusedES<WordDocCtrl>;

export type WordDocEvent = FocusDocEvent<WordDocCtrl>;

/**
 * Interface for the tool that need to listen to the internal lexical doc context
 * Ex: WordContextTool, WordTableTool,... that need to listen to any changes in the word doc
 */
export interface WordToolDocListener {
    onDocAttached(docCtrl?: WordDocCtrl): void;
    onDocDetached(docCtrl?: WordDocCtrl): void;
}

export type WordDocInitData = {
    viewportElClass: string;
    content: string;
};
