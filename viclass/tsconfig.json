/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "compileOnSave": false,
    "compilerOptions": {
        "paths": {
            "@viclass/editorui.commontools": ["dist/viclass/commontools"],
            "@viclass/editorui.classroomtools": ["dist/viclass/classroomtools"],
            "@viclass/editor.coordinator": ["dist/viclass/editor.coordinator"],
            "@viclass/editor.geo": ["dist/viclass/editor.geo"],
            "@viclass/editorui.geo": ["dist/viclass/editorui.geo"],
            "@viclass/editor.word": ["dist/viclass/editor.word"],
            "@viclass/editorui.word": ["dist/viclass/editorui.word"],
            "@viclass/editor.math": ["dist/viclass/editor.math"],
            "@viclass/editorui.math": ["dist/viclass/editorui.math"],
            "@viclass/editor.magh": ["dist/viclass/editor.magh"],
            "@viclass/editor.mcq": ["dist/viclass/editor.mcq"],
            "@viclass/editorui.magh": ["dist/viclass/editorui.magh"],
            "@viclass/editor.core": ["dist/viclass/editor.core"],
            "@viclass/editor.freedrawing": ["dist/viclass/editor.freedrawing"],
            "@viclass/editorui.freedrawing": ["dist/viclass/editorui.freedrawing"],
            "@viclass/editorui.loader": ["dist/viclass/editorui.loader"],
            "@viclass/portal.common": ["dist/viclass/portal.common"],
            "@viclass/ww": ["dist/viclass/ww"],
            "@viclass/proto/*": ["dist/viclass/proto/*"],
            "@viclass/eb.word": ["packages/backend/viclass/eb.word"],
            "@viclass/eb.math": ["packages/backend/viclass/eb.math"],
            "@viclass/eb.magh": ["packages/backend/viclass/eb.magh"],
            "@viclass/config.server": ["packages/backend/viclass/config.server"],
            "@viclass/themes": ["packages/themes/viclass/themes"]
        },
        "baseUrl": "./",
        "outDir": "./dist/out-tsc",
        "forceConsistentCasingInFileNames": true,
        "noImplicitOverride": true,
        "noPropertyAccessFromIndexSignature": true,
        "noImplicitReturns": true,
        "noFallthroughCasesInSwitch": true,
        "sourceMap": true,
        "declaration": false,
        "downlevelIteration": true,
        "experimentalDecorators": true,
        "moduleResolution": "node",
        "importHelpers": true,
        "target": "es2020",
        "module": "es2020",
        "lib": ["es2020", "dom", "dom.iterable"],
        "allowJs": true,
        "skipLibCheck": true,
        "allowSyntheticDefaultImports": true
    },
    "exclude": ["node_modules", "dist"],
    "angularCompilerOptions": {
        "enableI18nLegacyMessageIdFormat": false,
        "strictInjectionParameters": true,
        "strictInputAccessModifiers": true,
        "strictTemplates": true
    }
}
