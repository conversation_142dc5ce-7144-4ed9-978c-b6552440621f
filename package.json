{"name": "lib-jitsi-meet", "version": "0.0.0", "description": "JS library for accessing Jitsi server side deployments", "repository": {"type": "git", "url": "git://github.com/jitsi/lib-jitsi-meet"}, "keywords": ["jingle", "webrtc", "xmpp", "browser", "jitsi"], "author": "", "readmeFilename": "README.md", "dependencies": {"@jitsi/js-utils": "2.2.1", "@jitsi/logger": "2.0.2", "@jitsi/precall-test": "1.0.6", "@jitsi/rtcstats": "9.7.0", "@jitsi/sdp-interop": "git+https://github.com/jitsi/sdp-interop#3d49eb4aa26863a3f8d32d7581cdb4321244266b", "@testrtc/watchrtc-sdk": "1.38.2", "async-es": "3.2.4", "base64-js": "1.3.1", "current-executing-script": "0.1.3", "jquery": "3.6.1", "lodash-es": "4.17.21", "patch-package": "6.5.1", "sdp-transform": "2.3.0", "strophe.js": "1.5.0", "strophejs-plugin-disco": "0.0.2", "strophejs-plugin-stream-management": "^1.0.3", "uuid": "8.1.0", "webrtc-adapter": "8.1.1"}, "devDependencies": {"@babel/core": "7.16.0", "@babel/eslint-parser": "7.16.0", "@babel/preset-env": "7.16.0", "@babel/preset-typescript": "7.16.7", "@jitsi/eslint-config": "4.1.4", "@types/async": "3.2.12", "@types/jasmine": "4.0.3", "@types/lodash-es": "4.17.12", "@types/sdp-transform": "2.4.5", "babel-loader": "8.2.3", "core-js": "3.19.1", "eslint": "8.1.0", "eslint-plugin-import": "2.25.2", "jasmine-core": "4.2.0", "karma": "6.4.0", "karma-chrome-launcher": "3.1.1", "karma-jasmine": "5.1.0", "karma-sourcemap-loader": "0.3.8", "karma-webpack": "5.0.0", "process": "0.11.10", "string-replace-loader": "3.0.3", "typescript": "5.0.4", "vinyl-fs": "^4.0.0", "webpack": "5.76.0", "webpack-bundle-analyzer": "4.4.2", "webpack-cli": "4.9.0"}, "scripts": {"build": "yarn build:webpack && yarn build:tsc", "build:webpack": "webpack", "build:webpack-dev": "webpack --mode development", "build:tsc": "tsc --build --clean && tsc", "gen-types": "tsc --declaration --emitDeclarationOnly --outFile types/index.d.ts", "dist": "node _dist-copy.js", "lint": "eslint .", "lint-fix": "eslint . --fix", "postinstall": "patch-package", "prepack": "yarn build && yarn gen-types", "test": "karma start karma.conf.js", "test-watch": "karma start karma.conf.js --no-single-run", "validate": "npm ls", "watch": "webpack --config webpack.config.js --watch --mode development"}, "browser": "dist/umd/lib-jitsi-meet.min.js", "module": "dist/esm/JitsiMeetJS.js", "files": ["dist", "types/hand-crafted/*.d.ts", "index.d.ts"], "license": "Apache-2.0"}