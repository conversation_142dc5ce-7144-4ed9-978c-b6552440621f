package vinet.ccs.reactor

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Completable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx3.asCompletable
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.UserAvailableStatus
import portal.kafka.api.IKafkaEventReactor
import portal.kafka.api.KafkaEventData
import portal.kafka.notifications.*
import portal.kafka.notifications.notidata.*
import portal.lsession.pojo.toPojo
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.ActorTaskType
import vinet.ccs.actor.UserAvailableStatusHandler
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.exception.PeerException
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.model.SignalMessage
import vinet.ccs.model.SignalType
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerStatus
import vinet.ccs.utility.defaultMapper
import vinet.ccs.utility.json

@Singleton
class ClassroomNotificationEventReactor constructor(
    private val peerMan: PeerManager,
    private val actorManager: ActorManager,
    private val lsessionSG: LSessionServiceGateway,
    private val userAvailableStatusHandler: UserAvailableStatusHandler,
) : IKafkaEventReactor, Logging {
    override val reactorName = "notification-event-reactor"
    private val scope = CoroutineScope(Dispatchers.Default)

    private var recordId = -1L

    override fun process(record: ConsumerRecord<String, KafkaEventData>): Completable {
        val event = record.value()
        recordId = record.offset()

        if (event !is NotificationEvent)
            return Completable.error(Exception("Cannot precess event ${event.javaClass.simpleName} by $reactorName"))

        if (event.expiredTime < System.currentTimeMillis()) {
            return Completable.complete()
        }

        val objNode = defaultMapper.valueToTree<ObjectNode>(event)
        val data = event.data
        val jsonObjData = objNode.get(NotificationEvent::data.name) as ObjectNode

        // ignore some notifications
        if (data is AcceptPresentationRequestND || data is StopPresentationND || data is AcceptRaiseHandND) return Completable.complete()

        if (data is NewQuestionND) {
            jsonObjData.remove(NewQuestionND::jsonActivity.name)
            jsonObjData.set<ObjectNode>("activity", defaultMapper.readTree(data.jsonActivity))
        } else if (data is RequestPresentationND) {
            jsonObjData.remove(RequestPresentationND::jsonActivity.name)
            jsonObjData.set<ObjectNode>("activity", defaultMapper.readTree(data.jsonActivity))
        }

        val lsId = jsonObjData.get("lsId").asText()
        val signalType = signalMessageType(data)

        objNode.remove(KafkaEventData::clazz.name)
        objNode.remove(NotificationEvent::targetTo.name)

        val job = scope.launch {
            handleNotification(data, event.targetTo, objNode, lsId, signalType, this)
        }

        return job.asCompletable(Dispatchers.Default).doOnTerminate {
            logger.debug("[{}] process record done", recordId)
        }
    }

    private fun signalMessageType(notiData: NotificationData?): SignalType? {
        return when (notiData) {
            is AcceptPresentationRequestND -> SignalType.AcceptPresentationRequestND
            is AcceptRaiseHandND -> SignalType.AcceptRaiseHandND
            is AcceptShareScreenND -> SignalType.AcceptShareScreenND
            is ReqShareScreenND -> SignalType.ReqShareScreenND
            is CancelShareScreenND -> SignalType.CancelShareScreenND
            is LeaveClassND -> SignalType.LeaveClassND
            is RaiseHandND -> SignalType.RaiseHandND
            is RegisterND -> SignalType.RegisterND
            is StopPresentationND -> SignalType.StopPresentationND
            is RegistrationCancelledND -> SignalType.RegistrationCancelledND
            is AcceptRegistrationND -> SignalType.AcceptRegistrationND
            is NewQuestionND -> SignalType.NewQuestionND
            is RejectPresentationRequestND -> SignalType.RejectPresentationRequestND
            is RejectRegistrationND -> SignalType.RejectRegistrationND
            is RequestPresentationND -> SignalType.RequestPresentationND
            is RejectRaiseHandND -> SignalType.RejectRaiseHandND
            is RejectShareScreenND -> SignalType.RejectShareScreenND
            is CancelRaiseHandND -> SignalType.CancelRaiseHandND
            is ShareScreenRemovedND -> SignalType.ShareScreenRemovedND
            is JoinClassND -> SignalType.JoinClassND
            is StartClassND -> SignalType.StartClassND
            is StopClassND -> SignalType.StopClassND
            is StopQuestionND -> SignalType.StopQuestionND
            is CancelPresentationRequestND -> SignalType.CancelPresentationRequestND
            is RequestPinTabND -> SignalType.RequestPinTabND
            is CancelRequestPinTabND -> SignalType.CancelRequestPinTabND
            is RejectRequestPinTabND -> SignalType.RejectRequestPinTabND
            is ApproveRequestPinTabND -> SignalType.ApproveRequestPinTabND
            is UpdateRequestPinTabND -> SignalType.UpdateRequestPinTabND
            else -> null
        }
    }

    private suspend fun handleNotification(
        notiData: NotificationData?,
        target: NotificationTarget,
        notification: JsonNode,
        lsId: String?,
        signalType: SignalType?,
        childScope: CoroutineScope
    ) {
        if (signalType == null) return

        // an event is maybe targeted to only one user, all user in classroom or a group of users
        when (target) {
            is ClassroomTarget -> handleClassroomTarget(target, notification, signalType)
            is UserTarget -> {
                // currently, user only receives notification when they are in classroom
                if (lsId != null) handleUserTarget(target.userId, notification, lsId, signalType, childScope)
            }

            is MultiTarget -> {
                target.targets.map { handleNotification(notiData, it, notification, lsId, signalType, childScope) }
            }

            is GroupTarget -> {
                target.userIds.map {
                    if (lsId != null) handleUserTarget(it, notification, lsId, signalType, childScope)
                }
            }

            else -> throw Exception("Not support ${target.javaClass}")
        }
    }

    private suspend fun doReloadRegistration(actor: UserClassroomActor) {
        val lsRegistrationDeferred = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(actor.roomId, actor.userId)

        val lsRegistration = try {
            lsRegistrationDeferred.await().registration.toPojo()
        } catch (t: Throwable) {
            logger.error("Exception when get lsession registration: ", t)
            // do nothing
            return
        }

        actor.lsRegistration = lsRegistration
    }

    private suspend fun handleClassroomTarget(
        target: ClassroomTarget,
        notification: JsonNode,
        signalType: SignalType,
    ) {
        val exclude = target.usersExcluded ?: emptyList()
        val msgBuilder: (Peer) -> SignalMessage = {
            SignalMessage("", it.info.id, signalType, json.objectNode().putPOJO("notiData", notification))
        }

        // send message to all peers except current requesting peer
        peerMan.filterAndSendAsync(msgBuilder) {
            it.info.roomId == target.lsId && !exclude.contains(it.info.userId)
        }
    }

    private suspend fun handleUserTarget(
        userId: String,
        notification: JsonNode,
        lsId: String,
        signalType: SignalType,
        childScope: CoroutineScope
    ) {
        val actor = actorManager.getActor(lsId, userId) ?: return

        when (signalType) {
            SignalType.RejectRegistrationND, SignalType.RegistrationCancelledND -> {
                actor.send(ActorTaskType.ReloadRegistration) {
                    doReloadRegistration(actor)
                }
                userAvailableStatusHandler.process(actor, UserAvailableStatus.OFFLINE)
            }

            SignalType.AcceptRegistrationND -> {
                actor.send(ActorTaskType.ReloadRegistration) {
                    doReloadRegistration(actor)
                }
            }

            else -> {}
        }
        val peer = actor.peer

        val msg = SignalMessage(
            "",
            peer.info.id,
            signalType,
            json.objectNode().putPOJO("notiData", notification)
        )

        childScope.launch {
            sendMsgToPeerChannel(peer, msg)
        }
    }

    private suspend fun sendMsgToPeerChannel(peer: Peer, msg: SignalMessage) {
        if (peer.info.status == PeerStatus.KICKED_OUT) return

        while (true) {
            logger.debug("record [{}] Sending noti to peer [{}]: [{}]", recordId, peer.info.id, peer)
            try {
                peerMan.send(peer.info.id, msg, 3000)  // waiting for someone to receive the message for 3s
                logger.debug("record [{}] Send noti to peer [{}] successful: [{}]", recordId, peer.info.id, msg)
                break
            } catch (t: PeerException) {
                logger.error("record [{}] Send noti to peer [{}] failed, ignore peer exception... ", recordId, peer.info.id, t)
                break
            } catch (t: Throwable) {
                logger.error("record [{}] Send noti to peer [{}] failed... ", recordId, peer.info.id, t)
            }
        }
    }

}
