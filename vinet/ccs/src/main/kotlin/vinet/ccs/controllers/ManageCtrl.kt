package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import jayeson.utility.concurrent.worker.batch.SharedExecutorBatchFutureWorker
import kotlinx.coroutines.*
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.toPojo
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.notidata.JoinClassND
import proto.portal.user.UserMessage
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.ActorTaskType
import vinet.ccs.actor.UserAvailableStatusHandler
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.exception.PeerException
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.NotificationServiceGateway
import vinet.ccs.gateways.UserServiceGateway
import vinet.ccs.model.*
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerInfo
import vinet.ccs.pojo.PeerStatus
import vinet.ccs.pojo.PeerType
import vinet.ccs.utility.getRoomInfoIfExist
import vinet.ccs.utility.json
import vinet.ccs.utility.requireParams
import java.time.Instant


@Singleton
class ManageCtrl constructor(
    private val db: DatabaseService,
    private val peerMan: PeerManager,
    private val classroomInfoCache: ClassroomInfoCache,
    private val batchWorker: SharedExecutorBatchFutureWorker<String, Unit>,
    private val actorManager: ActorManager,
    private val userSG: UserServiceGateway,
    private val lsessionSG: LSessionServiceGateway,
    private val notificationSG: NotificationServiceGateway,
    private val userAvailableStatusHandler: UserAvailableStatusHandler,
) : Logging {

    suspend fun fetchRoom(call: ApplicationCall) {
        if (!call.requireParams(listOf("id"))) return

        val id: String = call.parameters["id"]!!
        try {
            val room = getRoomInfoIfExist(logger, call, classroomInfoCache, id) ?: return
            val roomInfo = SyncerRoomInfoResponse(
                id = room.id,
                owner = room.owner,
                defaultCoordState = room.defaultCoordState,
                presentingUser = room.presentingUser,
                presentingCoordState = room.presentingCoordState,
                presentingPeer = room.presentingPeer,
                pinnedCoordStates = room.pinnedCoordStates,
                coordStates = db.getCoordStateByRoom(room.id).map {
                    SyncerCoordStateResponse(it.id, it.owner, it.room, it.version)
                }
            )
            call.respond(HttpStatusCode.OK, roomInfo)
        } catch (t: Throwable) {
            logger.error("fetchRoom, failed to fetch room {} ... ", id, t)
            call.respond(HttpStatusCode.InternalServerError, "failed to fetch room")
        }
    }

    /**
     * Handles the registration of a user's peer connection for a specific classroom.
     * Orchestrates validation, actor interaction, and peer creation.
     */
    suspend fun registerUserPeer(call: ApplicationCall) {
        val userId = call.parameters["userId"] ?: return call.respond(HttpStatusCode.BadRequest, "Missing 'userId' parameter.")
        val roomId = call.parameters["roomId"] ?: return call.respond(HttpStatusCode.BadRequest, "Missing 'roomId' parameter.")

        logger.info("registerUserPeer, Registering user peer: User [{}], Room [{}]", userId, roomId)

        // 1. Fetch User and LSessionRegistration concurrently
        val user: UserMessage.UserProto?
        val lsRegistration: LSessionRegistration?
        try {
            coroutineScope {
                val userDeferred = userSG.getUserByIdAsync(userId)
                val lsRegistrationDeferred = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(roomId, userId)
                user = userDeferred.await().user
                lsRegistration = lsRegistrationDeferred.await().registration.toPojo()
            }
        } catch (t: Throwable) {
            logger.error("registerUserPeer, Failed to fetch user or LSession registration: User [{}], Room [{}]", userId, roomId, t)
            return call.respond(HttpStatusCode.InternalServerError, "Failed to fetch required user/session data: ${t.message}")
        }

        // 2. Validate fetched data
        if (user == null) {
            logger.error("registerUserPeer, User not found: User [{}]", userId)
            return call.respond(HttpStatusCode.Forbidden,"User not found.")
        }
        if (lsRegistration == null) {
            logger.error("registerUserPeer, LSession registration not found: User [{}], Room [{}]", userId, roomId)
            return call.respond(HttpStatusCode.Forbidden, "user registration not found.")
        }
        if (lsRegistration.regStatus != LSRegStatus.REGISTERED) {
            logger.warn("registerUserPeer, User not registered or registration status invalid: User [{}], Room [{}], Status [{}]", userId, roomId, lsRegistration.regStatus)
            return call.respond(HttpStatusCode.Forbidden, "User registration status is not valid: ${lsRegistration.regStatus}")
        }

        // 3. Get and kick out old Actor
        val oldActor = actorManager.getActor(roomId, userId)
        oldActor?.send(ActorTaskType.KickOutPeer) {
            // Kick out old peers *within* the actor task for atomicity
            kickOutOldPeer(oldActor.peer) // Assuming kickOutOldPeer is safe to call here
        }?.join()


        // 4. Execute Registration within Actor Context and Get Result
        val resultDeferred = CompletableDeferred<RegistrationInternalResult>() // Create Deferred to hold the result

        try {
            // This block runs sequentially within the actor's context
            try {
                logger.info("registerUserPeer, Executing peer registration task for User [{}], Room [{}]", userId, roomId)

                // Call the internal registration logic
                val result = doRegisterPeerInternal(lsRegistration, user) // Use non-null asserted values

                // Update actor's latestPeer if successful
                if (result is RegistrationInternalResult.Success) {
                    val actor = result.actor
                    actorManager.addActor(actor)
                    logger.info("registerUserPeer, Add actor ${actor}: User [{}], Room [{}], Peer [{}]", userId, roomId, actor.peer.info.id)
                }

                // Complete the deferred with the result
                resultDeferred.complete(result)

            } catch (actorTaskError: Throwable) {
                // If an error occurs *inside* the actor task, complete the deferred exceptionally
                logger.error("registerUserPeer, Error executing actor task for peer registration: User [{}], Room [{}]", userId, roomId, actorTaskError)
                resultDeferred.completeExceptionally(actorTaskError)
            }

            // Now, wait for the result from the deferred that was completed inside the actor task
            // 5. Handle Result and Respond (SUCCESS PATH)
            when (val registrationResult = resultDeferred.await()) { // Suspends until resultDeferred is completed
                is RegistrationInternalResult.Success -> {
                    val actor = registrationResult.actor
                    val peerId = actor.peer.info.id
                    logger.info("registerUserPeer, Peer registration successful: User [{}], Room [{}], Peer [{}]", userId, roomId, peerId)

                    userAvailableStatusHandler.process(actor, UserAvailableStatus.ONLINE)

                    call.respond(HttpStatusCode.OK, mapOf("peerId" to peerId))
                }
                is RegistrationInternalResult.Failure -> {
                    // This case might be less likely if errors inside the actor task complete the deferred exceptionally,
                    // but handle it just in case doRegisterPeerInternal returns Failure explicitly.
                    logger.error("registerUserPeer, Peer registration failed (explicit failure): User [{}], Room [{}], Status [{}], Reason [{}]", userId, roomId, registrationResult.statusCode, registrationResult.responseBody)
                    call.respond(registrationResult.statusCode, registrationResult.responseBody)
                }
            }

        } catch (t: Throwable) {
            // Catch errors from actor.send().join(), resultDeferred.await(), or if the deferred was completed exceptionally
            logger.error("registerUserPeer, Peer registration failed overall: User [{}], Room [{}]. Reason: {}", userId, roomId, t.message, t)
            // Check if response has already been sent before attempting to send again
            if (!call.response.isSent) {
                call.respond(HttpStatusCode.InternalServerError, "Internal error during peer registration: ${t.message}")
            } else {
                logger.warn("registerUserPeer, Response already sent for failed registration: User [{}], Room [{}]", userId, roomId)
            }
        }
    }

    /**
     * Result sealed class for internal peer registration logic.
     */
    private sealed class RegistrationInternalResult {
        data class Success(val actor: UserClassroomActor) : RegistrationInternalResult()
        data class Failure(val statusCode: HttpStatusCode, val responseBody: Any) : RegistrationInternalResult()
    }

    /**
     * Internal logic for creating and registering a peer.
     * This function is intended to be called *within* the UserClassroomActor's context.
     * It performs DB operations, interacts with PeerManager, and handles presenter updates.
     * It returns a result indicating success (with the Peer) or failure (with HTTP status/body).
     * **It does NOT interact with the ApplicationCall directly.**
     */
    private suspend fun doRegisterPeerInternal(
        lsRegistration: LSessionRegistration,
        user: UserMessage.UserProto
    ): RegistrationInternalResult {
        val roomId = lsRegistration.lsId
        val userId = user.id

        // 1. Check Room Existence
        val roomInfo = try {
            classroomInfoCache.get(roomId)
        } catch (e: Throwable) {
            logger.error("doRegisterPeerInternal, Failed to get room info from cache: Room [{}]", roomId, e)
            return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError,"Failed to verify room information.")
        }
        if (roomInfo == null) {
            logger.error("doRegisterPeerInternal, Room not found during peer registration: Room [{}]", roomId)
            return RegistrationInternalResult.Failure(HttpStatusCode.Forbidden, "Room does not exist.")
        }

        // 2. Create and Insert PeerInfo
        val peerInfo = PeerInfo(
            userId = userId,
            lsRegId = lsRegistration.id,
            roomId = roomId,
            peerType = PeerType.BROWSER // Assuming BROWSER type
        )
        try {
            val inserted = db.insertPeer(peerInfo)
            if (inserted?.insertedId == null) {
                logger.error("doRegisterPeerInternal, Failed to insert peer into DB: PeerInfo [{}]", peerInfo)
                return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError,"Failed to save peer information.")
            }
            // Note: The ID is generated by MongoDB and is now in peerInfo.id
            logger.info("doRegisterPeerInternal, Inserted peer into DB: Peer [{}]", peerInfo.id)
        } catch (e: Throwable) {
            logger.error("doRegisterPeerInternal, Exception inserting peer into DB: PeerInfo [{}]", peerInfo, e)
            return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError,"Database error during peer registration.")
        }

        // 3. Add Peer to PeerManager
        val peer = try {
            peerMan.addPeer(peerInfo)
        } catch (e: Throwable) {
            logger.error("doRegisterPeerInternal, Failed to add peer to PeerManager: Peer [{}]", peerInfo.id, e)
            // Attempt cleanup? Maybe remove from DB? For now, fail.
            return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError, "Failed to activate peer session.")
        }

        // 4. Handle Presenter Update (if applicable)
        if (roomInfo.presentingUser == userId) {
            logger.info("doRegisterPeerInternal, User [{}] is the presenter for Room [{}]. Updating presenting peer to [{}].", userId, roomId, peerInfo.id)
            try {
                // Update DB and invalidate cache
                db.updatePresenting(roomId, null, peerInfo.id) // Assuming this updates the peer ID
                classroomInfoCache.invalidate(roomId)

                // Find an active syncer peer
                val syncerPeer = db.findAliveSynchronizers()
                    .sortedByDescending { it.lastSeen }
                    .find { pInfo -> peerMan.hasPeer(pInfo.id) }

                if (syncerPeer == null) {
                    logger.error("doRegisterPeerInternal, Could not find active syncer peer to notify about presenter update for Room [{}]", roomId)
                    return RegistrationInternalResult.Failure(HttpStatusCode.ServiceUnavailable, "Could not find active syncer peer to notify about presenter update.")
                } else {
                    // Send update message to syncer
                    val data = UpdatePresentingPeerMessage(roomId, peerInfo.id)
                    val signalMessage = SignalMessage(
                        peerInfo.id, // From the new peer
                        syncerPeer.id, // To the syncer
                        SignalType.roomUpdate,
                        json.pojoNode(data),
                        System.currentTimeMillis().toString()
                    )
                    // Use request with timeout, but don't block actor indefinitely
                    // Consider launching this in a separate non-blocking way if peerMan.request can block significantly
                    try {
                        logger.debug("doRegisterPeerInternal, requesting update room {}", signalMessage)
                        val response = peerMan.request(syncerPeer.id, signalMessage, 5000, 5000)
                        logger.info("doRegisterPeerInternal, Syncer peer [{}] acknowledged presenter update for Room [{}]. Response: {}", syncerPeer.id, roomId, response)
                    } catch (e: PeerException) {
                        logger.error("doRegisterPeerInternal, Syncer peer [{}] did not respond to presenter update request for Room [{}]", syncerPeer.id, roomId)
                        return RegistrationInternalResult.Failure(HttpStatusCode.ServiceUnavailable,"syncer did not respond to presenter")
                    } catch (reqErr: Throwable) {
                        logger.error("doRegisterPeerInternal, Error sending presenter update request to syncer peer [{}]: {}", syncerPeer.id, reqErr.message)
                        return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError, reqErr.message ?: "unknown error")
                    }
                }
            } catch (e: Throwable) {
                logger.error("doRegisterPeerInternal, Failed during presenter update logic for Peer [{}] ... ", peerInfo.id, e)
                return RegistrationInternalResult.Failure(HttpStatusCode.InternalServerError, e.message ?: "unknown error")
            }
        }

        // 5. Send Join Notification (Asynchronously)
        try {
            val notiData = JoinClassND(roomId, userId)
            val notification = Notification(
                userId,
                ClassroomTarget(roomId),
                "Học viên ${user.username} vừa tham gia buổi học",
                Instant.now().plusSeconds(300),
                notiData
            )
            // Launch notification saving in a separate coroutine, don't wait for it
            CoroutineScope(Dispatchers.IO).launch { // Use IO dispatcher for network call
                try {
                    notificationSG.saveNotificationAsync(notification).await()
                    logger.debug("doRegisterPeerInternal, Sent join notification for User [{}], Room [{}]", userId, roomId)
                } catch (notiError: Throwable) {
                    logger.error("Failed to send join notification for User [{}], Room [{}]: {}", userId, roomId, notiError.message)
                }
            }
        } catch (e: Throwable) {
            logger.error("doRegisterPeerInternal, Error preparing join notification for User [{}], Room [{}]: {}", userId, roomId, e.message)
        }

        // 6. Return Success
        return RegistrationInternalResult.Success(UserClassroomActor(roomId, userId, lsRegistration, user, peer))
    }

    /**
     * Kicks out existing active peers for a given user in a room.
     * Sends a kickOut message and schedules removal.
     * Should ideally run within the actor's context before adding the new peer.
     */
    private suspend fun kickOutOldPeer(oldPeer: Peer) {
        val oldPeerId = oldPeer.info.id

        val data = json.objectNode().put("message", "New connection registered, this connection is being closed.")
        val msg = SignalMessage("", oldPeerId, SignalType.kickOut, data)
        try {
            // send kickout message then remove peer
            peerMan.send(oldPeerId, msg, 1000)
        } catch (sendError: Throwable) {
            logger.error("kickOutOldPeer, Error sending kickOut message to old peer [{}]: {}", oldPeerId, sendError.message)
        } finally {
            // Always set the peer  kicked out after attempting to send kickout
            oldPeer.info.status = PeerStatus.KICKED_OUT // Mark status immediately

            // Submit for potential batch processing
            batchWorker.submit(oldPeerId)
        }
    }

    suspend fun registerSyncPeer(call: ApplicationCall) {
        val info = PeerInfo(peerType = PeerType.SYNCER)

        val insertedId = db.insertPeer(info)?.insertedId
        if (insertedId == null) {
            logger.error("registerSyncPeer, failed to insert syncer peer")
            call.respond(HttpStatusCode.InternalServerError, "cannot insert syncer peer to database")
            return
        }

        peerMan.addPeer(info)

        call.respond(mapOf("peerId" to info.id))
    }
}
