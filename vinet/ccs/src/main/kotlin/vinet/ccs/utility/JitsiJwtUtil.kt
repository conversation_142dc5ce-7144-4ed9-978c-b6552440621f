package vinet.ccs.utility

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import proto.portal.user.UserMessage
import vinet.ccs.pojo.ClassroomInfo

class JitsiJwtUtil(
    secret : String,
    private val appId : String,
    private val issuers : List<String>,
    private val audiences : List<String>,
    private val mapper : ObjectMapper
) {

    private val algorithm = Algorithm.HMAC256(secret)

    /**
     * Generate a token for a room
     */
    fun tokenForRoom(classroomInfo: ClassroomInfo, userProfile: UserMessage.UserProfileProto) : String {
        val payload = json.objectNode().set<ObjectNode>(
                        "context", json.objectNode().set<ObjectNode>(
                            "user", json.objectNode()
                                    .put("name", userProfile.username)
                                    .put("email", userProfile.email)
                                    .put("avatar", userProfile.avatarUrl)
                                    .put("role", if (classroomInfo.owner == userProfile.id) "moderator" else "participant")
                        )
                    )
            .put("room", classroomInfo.id)
            .put("appId", appId)
            .put("role", if (classroomInfo.owner == userProfile.id) "moderator" else "participant")

        var builder = JWT.create().withPayload(mapper.writeValueAsString(payload))

        issuers.forEach { i -> builder = builder.withIssuer(i) }
        audiences.forEach { a -> builder = builder.withAudience(a) }

        return builder.sign(algorithm)
    }
}