package vinet.ccs

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.logging.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.koin.core.context.GlobalContext
import org.koin.dsl.koinApplication
import org.koin.ksp.generated.module
import vinet.ccs.controllers.*
import vinet.ccs.hooks.Shutdown
import vinet.ccs.hooks.Startup
import vinet.ccs.koin.KoinApplicationComponent
import vinet.ccs.utility.defaultMapper

val logger = (object : Logging {
    override val loggerName = "Application"
}).logger

fun Application.module() {
    val koinApplication = koinApplication {
        modules(
            KoinApplicationComponent().module,
        )
        koin.declare(environment)
    }
    val appComp = GlobalContext.startKoin(koinApplication).koin

    val startup = appComp.get<Startup>()
    val shutdown = appComp.get<Shutdown>()
    shutdown.registerShutdownHook()
    startup.start()

    install(ContentNegotiation) {
        register(ContentType.Application.Json, JacksonConverter(defaultMapper))
    }

    configureStatusPages()

    val manageCtrl = appComp.get<ManageCtrl>()
    val signalCtrl = appComp.get<SignalCtrl>()
    val coordinatorStateCtrl = appComp.get<CoordinatorStateCtrl>()
    val classroomCtrl = appComp.get<ClassroomCtrl>()
    val metadataDocCtrl = appComp.get<MetadataDocCtrl>()
    val localContentCtrl = appComp.get<LocalContentCtrl>()
    val conferenceCtrl = appComp.get<ConferenceCtrl>()

    routing {
        manageRoutes(manageCtrl)
        conferenceRoutes(conferenceCtrl)
        signalRoutes(signalCtrl)
        coordinatorStateRoutes(coordinatorStateCtrl)
        classroomRoutes(classroomCtrl)
        metadataRoutes(metadataDocCtrl)
        localContentRoutes(localContentCtrl)
    }
}

private fun Application.configureStatusPages() {
    install(StatusPages) {
        status(HttpStatusCode.NotFound) { call, _ ->
//            call.respondText(text = "404: Page Not Found", status = status)
            logger.error("Http not found status {}", call.request.toLogString())
        }
        exception<Throwable> { call, cause ->
            logger.error("unknown exception... ", cause)
            call.respondText(
                text = "500: InternalServerError",
                status = io.ktor.http.HttpStatusCode.InternalServerError
            )
        }
    }
}
