package vinet.ccs.model

/**
 *
 * <AUTHOR>
 */

data class CreateJitsiJwtTokenRequest(
    val peerId: String,
    val userId: String,
    val roomId: String
)

data class PresentCoordStateRequest(
    val peerId: String,
    val coordStateId: String
)

data class CreateCoordinatorStateRequest(
    val peerId: String,
    val title: String?,
)

data class DeleteCoordinatorStateRequest(
    val peerId: String,
    val coordStateId: String,
)

data class RenameCoordinatorStateRequest(
    val peerId: String,
    val coordStateId: String,
    val title: String,
)

data class PinCoordinatorStateRequest(
    val peerId: String,
    val coordStateId: String,
)

data class UnpinCoordinatorStateRequest(
    val peerId: String,
    val coordStateId: String,
)

data class RequestPinTabRequest(
    val peerId: String,
    val coordStateId: String,
)

data class CancelRequestPinTabRequest(
    val peerId: String,
    val coordStateId: String,
)

data class ApproveRequestPinTabRequest(
    val peerId: String,
    val targetUserId: String,
    val coordStateId: String,
)

data class RejectRequestPinTabRequest(
    val peerId: String,
    val targetUserId: String,
    val coordStateId: String,
)

data class GetCoordinatorStatesRequest(
    val coordStateIds: List<String>
)

data class DuplicateCoordinatorStateRequest(
    val peerId: String,
    val coordStateId: String
)

data class DuplicateDocumentsResponse(
    // map of the source id to duplicated id
    val mapping: Map<String, String>
)

data class ReceiveCmdRequest(
    val peerId: String,
    val coordinatorId: String
)

data class LayerInfo(
    val layerId: Int,
    val layerIndex: Int,
    val positionStart: Position?,
    val positionEnd: Position?,
)

data class DefaultSetting(
    val background: Boolean,
    val backgroundColor: String,
    val shadow: Boolean,
    val shadowType: String,
    val border: Boolean,
    val borderColor: String,
    val borderType: String,
)

data class PresenterState(
    val vpZoom: Double,
    val vpPos: List<Double>,
    val vpSize: List<Double>,
)

data class AddDocsMappingAndLayersRequest(
    val peerId: String,
    val coordStateId: String,
    val coordStateVersion: Int = 1,
    val documents: List<DocInfo>,
    val defaultSetting: DefaultSetting
)

data class DocInfo(
    val channelCode: Int,
    val docGlobalId: String?,
    val docLocalId: Int,
    val layerUpdates: List<LayerInfo>?,
    val layerRemoves: List<LayerInfo>?,
    val layerAdds: List<LayerInfo>?,
)

data class RemoveMultipleDocMappingAndLayersRequest(
    val peerId: String,
    val coordStateId: String,
    val coordStateVersion: Int = 1,
    val documents: List<DocInfo>,
)

data class UpdateLayerPositionRequest(
    val peerId: String,
    val coordStateId: String,
    val channelCode: Int,
    val docLocalId: Int,
    val layerId: Int,
    val positionStart: Position?,
    val positionEnd: Position?,
    val coordStateVersion: Int = 1,
)

data class RemoveLayerRequest(
    val peerId: String,
    val coordStateId: String,
    val channelCode: Int,
    val docLocalId: Int,
    val layerId: Int,
    val coordStateVersion: Int = 1,
)

data class Position(
    val x: Double,
    val y: Double
)

data class Layer(
    val channelCode: Int,
    val layerId: Int,
    val docLocalId: Int,
    val positionStart: Position?,
    val positionEnd: Position?,
)

data class LocalContentChange(
    val channelCode: Int,
    val docLocalId: Int,
    val version: Int,
    val content: String
)

data class UpdateLocalContentRequest(
    val coordStateId: String,
    val changes: List<LocalContentChange>,
)