package vinet.ccs.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId

/**
 * <AUTHOR>
 */
data class CoordinatorState @BsonCreator constructor(
    /**
     * this is the user id of the user who created this coordinator state
     */
    @BsonRepresentation(BsonType.OBJECT_ID)
    @BsonProperty("owner")
    val owner: String,

    @BsonRepresentation(BsonType.OBJECT_ID)
    @BsonProperty("room")
    val room: String,

    @BsonProperty("title")
    var title: String,

    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    val id: String = ObjectId().toHexString(),

    /**
     * Map<channelCode_localId, globalId>
     * mapping document global id with local id.
     * - global id is the id that was generated by server at the backend when saved document successful.
     * - local id is the id that was generated by editor at the front end by registry manager.
     * - channel code is the code of editor
     */
    @BsonProperty("docMapping") val docMapping: Map<String, String> = emptyMap(),

    // map by channelCode_docLocalId_layerId
    @BsonProperty("layers") val layers: Map<String, LayerInfo> = emptyMap(),

    @BsonProperty("version") val version: Int = 1,

    @BsonProperty("defaultSetting") val defaultSetting: DefaultSetting = DefaultSetting(
        background = false,
        backgroundColor = "",
        shadow = false,
        shadowType = "",
        border = false,
        borderColor = "",
        borderType = "",
    ),

    @BsonProperty("presenterState") val presenterState: PresenterState = PresenterState(
        vpZoom = 1.0,
        vpSize = listOf(0.0, 0.0),
        vpPos = listOf(0.0, 0.0)
    ),

    // map by channelCode_docLocalId
    @BsonProperty("docSettings") val docSettings: Map<String, DocSetting> = emptyMap()
) {

    data class LayerInfo @BsonCreator constructor(

        @BsonProperty("id") var id: Int,

        @BsonProperty("docLocalId") var docLocalId: Int,

        @BsonProperty("zIndex") var zIndex: Int,

        @BsonProperty("position") var position: List<Double>? = null,
    )

    data class DefaultSetting @BsonCreator constructor(
        @BsonProperty("background") val background: Boolean,
        @BsonProperty("backgroundColor") val backgroundColor: String,

        @BsonProperty("shadow") val shadow: Boolean,
        @BsonProperty("shadowType") val shadowType: String,

        @BsonProperty("border") val border: Boolean,
        @BsonProperty("borderType") val borderType: String,
        @BsonProperty("borderColor") val borderColor: String,
    )

    data class PresenterState @BsonCreator constructor(
        @BsonProperty("vpZoom") val vpZoom: Double,
        @BsonProperty("vpPos") val vpPos: List<Double>,
        @BsonProperty("vpSize") val vpSize: List<Double>,
    )

    data class DocSetting @BsonCreator constructor(
        @BsonProperty("channelCode") val channelCode: Int,
        @BsonProperty("docLocalId") val docLocalId: Int,

        @BsonProperty("background") val background: Boolean,
        @BsonProperty("backgroundColor") val backgroundColor: String,

        @BsonProperty("shadow") val shadow: Boolean,
        @BsonProperty("shadowType") val shadowType: String,

        @BsonProperty("border") val border: Boolean,
        @BsonProperty("borderType") val borderType: String,
        @BsonProperty("borderColor") val borderColor: String,
    )
}


