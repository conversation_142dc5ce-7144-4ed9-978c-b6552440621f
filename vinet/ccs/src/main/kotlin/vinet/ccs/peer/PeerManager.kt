package vinet.ccs.peer

import common.libs.logger.Logging
import kotlinx.coroutines.*
import org.koin.core.annotation.Singleton
import vinet.ccs.db.DatabaseService
import vinet.ccs.exception.*
import vinet.ccs.model.SignalMessage
import vinet.ccs.pojo.PeerInfo
import vinet.ccs.pojo.PeerType
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.CoroutineContext

/**
 * Manages the lifecycle and communication of peers in the system.
 *
 * This class is responsible for:
 * - Storing and retrieving peer information.
 * - Sending and receiving messages to/from peers.
 * - Managing the lifecycle of peers (adding, removing, updating).
 * - Handling asynchronous operations related to peer communication.
 */
@Singleton
class PeerManager constructor(
    private val databaseService: DatabaseService,
    // Inject CoroutineContext for better testability and control
    coroutineContext: CoroutineContext = Dispatchers.Default + SupervisorJob()
) : Logging {

    // Use the injected context for the scope
    private val scope = CoroutineScope(coroutineContext)
    private val peers = ConcurrentHashMap<String, Peer>()

    // --- Helper Function for Peer Lookup ---

    /**
     * Finds a peer by its ID or throws PeerNotFound if not found.
     * Centralizes the lookup logic and error handling.
     *
     * @param peerId The ID of the peer to find.
     * @param operation A description of the operation being performed (for logging/error messages).
     * @param message Optional message context to include in error logs.
     * @return The found Peer.
     * @throws PeerNotFound if the peer with the given ID does not exist.
     */
    private fun findPeerOrThrow(peerId: String, operation: String, message: Any? = null): Peer {
        return peers[peerId] ?: run {
            val msgContext = message?.let { " message [$it]" } ?: ""
            logger.error("Peer [{}] not available when {}{}", peerId, operation, msgContext)
            throw PeerNotFound("Peer $peerId not available for $operation")
        }
    }

    // --- Core Peer Communication Methods ---

    suspend fun receive(peerId: String, timeout: Long? = null): SignalMessage {
        val peer = findPeerOrThrow(peerId, "receive")
        return peer.receive(timeout)
    }

    suspend fun send(peerId: String, message: SignalMessage, timeout: Long? = null) {
        val peer = findPeerOrThrow(peerId, "send", message)
        return peer.send(message, timeout)
    }

    fun sendAsync(peerId: String, message: SignalMessage): Job {
        // Use helper function for lookup before launching coroutine
        val peer = findPeerOrThrow(peerId, "sendAsync", message)
        return scope.launch {
            try {
                peer.send(message)
            } catch (e: Throwable) {
                logger.error("Error during sendAsync for peer [{}]: {}", peerId, e.message, e)
                // SupervisorJob ensures this failure doesn't cancel the main scope
            }
        }
    }

    /**
     * Sends a request message and suspends waiting for a reply.
     * Throws an exception if the peer is closed, inactive, kicked out,
     * if the send operation times out, or if the reply is not received within the specified timeout.
     *
     * @param message The request message (must have a requestId).
     * @param replyTimeoutMillis The maximum time to wait for the reply. Must be positive.
     * @param sendTimeoutMillis Optional timeout for the internal send operation (defaults to 5000ms).
     * @return The SignalMessage containing the reply.
     * @throws IllegalArgumentException if msg.requestId is null or replyTimeoutMillis is not positive.
     * @throws PeerKickedOutException if the peer status is KICKED_OUT.
     * @throws PeerInactiveException if the peer status is INACTIVE.
     * @throws PeerClosedException if the peer is closing, closed, or the channel closes during operation.
     * @throws PeerRequestTimeoutException if the *send* operation times out.
     * @throws PeerReplyTimeoutException if the *reply* is not received within replyTimeoutMillis.
     * @throws Throwable for other underlying errors during send or await.
     */
    suspend fun request(
        peerId: String,
        message: SignalMessage,
        sendTimeoutMillis: Long? = null, // Optional timeout for to send itself
        replyTimeoutMillis: Long? = null // Timeout for the reply
    ): SignalMessage {
        val peer = findPeerOrThrow(peerId, "request", message)
        // Assuming peer.request returns a Deferred
        return peer.request(message, sendTimeoutMillis, replyTimeoutMillis)
    }

    fun reply(peerId: String, message: SignalMessage) {
        val peer = findPeerOrThrow(peerId, "reply", message)
        peer.reply(message)
    }

    // --- Peer Lifecycle Management ---

    fun addPeer(pInfo: PeerInfo): Peer {
        // Consider checking if peer already exists and logging/handling appropriately if needed
         if (peers.containsKey(pInfo.id)) {
             logger.warn("The peer ${pInfo.id} already exists")
         }
        val peer = Peer(pInfo)
        peers[pInfo.id] = peer
        logger.info("Added peer: {}", pInfo)
        return peer
    }

    fun hasPeer(peerId: String): Boolean {
        return peers.containsKey(peerId)
    }

    fun removePeers(peerIds: List<String>) {
        peerIds.forEach { peerId -> peers.remove(peerId) }

    }

    // --- Peer Querying Methods ---

    fun getPeer(peerId: String): Peer? {
        return peers[peerId] // Use direct lookup
    }

    /**
     * Gets the SYNCER peer that was seen least recently (oldest lastSeen).
     */
    fun getFirstAliveSyncerPeer(): Peer? {
        // Use sequence operations for efficiency: filter then find minimum.
        return peers.values.asSequence()
            .filter { it.info.peerType == PeerType.SYNCER }
            .maxByOrNull { it.info.lastSeen }
    }

    // --- Peer Status Update ---

    fun updateAlive(peerId: String) {
        scope.launch {
            val peer = findPeerOrThrow(peerId, "updateAlive")
            val lastSeen = System.currentTimeMillis()

            peer.info.lastSeen = lastSeen // Update in-memory state

            // Perform potentially blocking DB operation in IO context
            try {
                databaseService.updatePeerLastSeen(peerId, lastSeen)
            } catch (e: Throwable) {
                // Log DB update errors but don't necessarily fail the whole operation
                // (in-memory state is already updated)
                logger.error("Failed to update lastSeen in database for peer [{}]: {}", peerId, e.message, e)
            }
        }
    }

    // --- Bulk Operations ---

    /**
     * Filters peers based on a predicate and sends a message (built by msgBuilder)
     * to each matching peer asynchronously.
     *
     * @param msgBuilder Function to generate the SignalMessage for a given Peer.
     * @param predicate Function to determine if a Peer should receive the message.
     * @param sendTimeout Timeout in milliseconds for each individual send operation.
     * @return A Job representing the completion of all launched send operations.
     */
    fun filterAndSendAsync(
        msgBuilder: (Peer) -> SignalMessage,
        sendTimeout: Long? = null, // Make timeout configurable
        predicate: (Peer) -> Boolean
    ): Job {
        return scope.launch { // Outer job managed by SupervisorJob
            val matchingPeers = peers.values.filter(predicate)
            if (matchingPeers.isEmpty()) {
                logger.debug("filterAndSendAsync: No peers matched the predicate.")
                return@launch // Nothing to do
            }

            logger.debug("filterAndSendAsync: Sending to {} matching peers.", matchingPeers.size)
            matchingPeers.forEach { peer ->
                // Launch a child coroutine for each send operation
                launch {
                    var message: SignalMessage? = null
                    try {
                        message = msgBuilder(peer)
                        // Pass the configurable timeout to the send operation
                        peer.send(message, sendTimeout)
                    } catch (e: Throwable) {
                        // Log errors during individual sends without crashing the outer job
                        logger.error(
                            "Failed to send message via filterAndSendAsync to peer [{}]: {}, message {}",
                            peer.info.id,
                            e.message,
                            message,
                            e // Include stack trace for debugging
                        )
                        // Do not re-throw other exceptions here, allow other sends to proceed
                    }
                }
            }
        }
    }

    // --- Utility ---

    fun shutdown() {
        logger.info("Shutting down PeerManager, closing all peers...")
        // Cancel the main scope, which cancels all launched jobs
        scope.cancel()
        // Close remaining peers individually (scope cancellation might not be instant)
        peers.values.forEach {
            try {
                it.close()
            } catch (e: Exception) {
                logger.error("Error closing peer [{}] during shutdown: {}", it.info.id, e.message)
            }
        }
        peers.clear()
        logger.info("PeerManager shutdown complete.")
    }
}