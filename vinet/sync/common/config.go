package common

import (
	"github.com/spf13/viper"

	"encoding/json"
)

type CcsConfig struct {
	Url string
}

type PeerInfo struct {
	PeerId string
}

type WebrtcConfig struct {
	Ip   string
	Port string
}

type Config struct {
	Ccs      CcsConfig
	PeerInfo PeerInfo
	Webrtc   WebrtcConfig
	viper    *viper.Viper `json:"-"`
}

func ReadConfig(file *string) *Config {
	var filename string
	if file == nil {
		filename = "conf/config.yaml"
	} else {
		filename = *file
	}

	v := viper.New()
	// read configuration file
	v.SetConfigFile(filename)
	v.AddConfigPath(".")
	v.ReadInConfig()

	var config Config
	v.Unmarshal(&config)
	config.viper = v

	return &config
}

func (c *Config) Save() {
	bytes, _ := json.Marshal(*c)
	temp := make(map[string]interface{})
	json.Unmarshal(bytes, &temp)
	c.viper.MergeConfigMap(temp)
	c.viper.WriteConfig()
}
