package portal.filestore.models

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import java.util.*

data class FileMetadata @BsonCreator constructor(
    @BsonId()
    @BsonRepresentation(org.bson.BsonType.OBJECT_ID)
    val id: String = ObjectId().toHexString(),

    /**
     * File id from GridFS collection
     */
    @BsonProperty("fileId")
    @BsonRepresentation(org.bson.BsonType.OBJECT_ID)
    val fileId: String,
    @BsonProperty("ownerId")
    @BsonRepresentation(org.bson.BsonType.OBJECT_ID)
    var ownerId: String? = null,

    @BsonProperty("originalFileName")
    val originalFileName: String,
    @BsonProperty("fileSizeByte")
    var fileSizeByte: Int,
    @BsonProperty("version")
    var version: Int = 1,

    @BsonProperty("createdAt")
    var createdAt: Date? = null,
    @BsonProperty("updatedAt")
    var updatedAt: Date? = null,
)
