package portal.filestore.dbgateway

import org.reactivestreams.Publisher
import portal.filestore.models.FileMetadata
import java.nio.ByteBuffer

interface IFileStorageGateway {
    /**
     * Uploads a file to the file store
     * @return the id of the document
     */
    suspend fun uploadFile(file: ByteArray, filename: String): String

    /**
     * Downloads a file from the file store
     * @return the file as a ByteArray
     */
    suspend fun downloadFile(fileMetadata: FileMetadata): ByteArray

    /**
     * Downloads a file from the file store to a Publisher for streaming to the client
     * @return the file as a Publisher<ByteBuffer>
     */
    suspend fun downloadToPublisher(fileMetadata: FileMetadata): Publisher<ByteBuffer>

    /**
     * Deletes a file from the file store
     */
    suspend fun deleteFile(fileId: String)
}