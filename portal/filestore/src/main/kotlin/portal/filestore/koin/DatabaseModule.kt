package portal.filestore.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import com.mongodb.reactivestreams.client.gridfs.GridFSBucket
import com.mongodb.reactivestreams.client.gridfs.GridFSBuckets
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import portal.filestore.configs.DatabaseConfig
import portal.filestore.models.FileMetadata

@Module
class DatabaseModule {
    @Single
    fun provideMongoDatabase(dbConf: DatabaseConfig): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        val pojoCodecRegistry: CodecRegistry = CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                PojoCodecProvider.builder()
                    .automatic(true)
                    .register(FileMetadata::class.java)
                    .build()
            )
        )
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    @Single
    fun provideMetadataCollection(db: MongoDatabase): MongoCollection<FileMetadata> {
        return db.getCollection("file-metadata", FileMetadata::class.java)
    }

    @Single
    fun provideGridFsBucket(dbConf: DatabaseConfig, db: MongoDatabase): GridFSBucket {
        return GridFSBuckets.create(db, dbConf.gridFsBucketName)
    }
}