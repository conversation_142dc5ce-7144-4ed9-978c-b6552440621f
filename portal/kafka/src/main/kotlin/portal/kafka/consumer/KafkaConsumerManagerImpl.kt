package portal.kafka.consumer

import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import org.koin.core.component.KoinComponent
import portal.kafka.api.IKafkaConsumerManager
import portal.kafka.api.configs.KafkaConfig

class KafkaConsumerManagerImpl constructor(
    private val kafkaConf: KafkaConfig,
) : IKafkaConsumerManager, KoinComponent {
    private val consumers = initConsumer()

    private fun initConsumer(): List<KafkaEventConsumer> {
        return kafkaConf.consumerConfigs.map { KafkaEventConsumer(it) }
    }

    override fun start(): Completable {
        return Completable.fromAction {
            consumers.forEach { it.start() }
        }
    }

    override fun stop(): Completable {
        val stopSources = consumers.map { it.stop() }

        return Completable.merge(
            Flowable.fromIterable(stopSources)
        )
    }
}