package portal.kafka.api.configs

import common.libs.logger.Logging
import java.io.FileInputStream
import java.util.*
import kotlin.system.exitProcess

abstract class KafkaPropertiesConfig(
    open val propertiesPath: String,
) : Logging {

    protected fun loadProperties(): Properties {
        val inputStream = FileInputStream(propertiesPath)
        try {
            return inputStream.use {
                val properties = Properties()
                properties.load(it)
                properties
            }
        } catch (t: Throwable) {
            logger.error("Failed to load properties from {}: ", propertiesPath, t)
            exitProcess(1)
        }
    }
}