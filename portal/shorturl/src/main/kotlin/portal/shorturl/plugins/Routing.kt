package portal.shorturl.plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.rx3.await
import org.koin.ktor.ext.inject
import portal.shorturl.logger
import portal.shorturl.models.ShortenRequest
import portal.shorturl.services.AnalyticsService
import portal.shorturl.services.RedirectResult
import portal.shorturl.services.URLService

fun Application.configureRouting() {
    val urlService: URLService by inject()
    val analyticsService: AnalyticsService by inject()

    routing {
        route("/shorturl") {
            get("") {
                call.respondText(
                    this::class.java.classLoader.getResource("index.html")?.readText()
                        ?: "<h1>index.html not found</h1>",
                    ContentType.Text.Html
                )
            }

            // API routes
            route("/api") {
                // Shorten URL
                post("/shorten") {
                    try {
                        val request = call.receive<ShortenRequest>()
                        val clientIP = call.request.headers["X-Real-IP"]
                            ?: call.request.headers["X-Forwarded-For"]
                            ?: call.request.local.remoteHost
                        val response = urlService.shortenUrl(request, clientIP).await()
                        call.respond(response)
                    } catch (e: IllegalArgumentException) {
                        logger.error("Invalid request", e)
                        call.respond(HttpStatusCode.BadRequest, mapOf("error" to (e.message ?: "Invalid request")))
                    } catch (e: Exception) {
                        logger.error("Error processing shorten request", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }

                // Get URL statistics
                get("/stats/{shortCode}") {
                    val shortCode = call.parameters["shortCode"] ?: ""
                    val clientIP = call.request.headers["X-Real-IP"]
                        ?: call.request.headers["X-Forwarded-For"]
                        ?: call.request.local.remoteHost
                    try {
                        val urlDoc = urlService.getUrlStats(shortCode, clientIP).await()
                        call.respond(urlDoc)
                    } catch (e: NoSuchElementException) {
                        logger.error("Error getting stats for $shortCode", e)
                        call.respond(HttpStatusCode.NotFound, mapOf("error" to "URL not found"))
                    } catch (e: Exception) {
                        logger.error("Error getting stats for $shortCode", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }

                // Get all URLs
                get("/urls") {
                    try {
                        val urls = urlService.getAllUrls().await()
                        call.respond(urls)
                    } catch (e: Exception) {
                        logger.error("Error getting all URLs", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }

                // Search URLs
                get("/urls/search") {
                    val query = call.request.queryParameters["q"] ?: ""
                    if (query.isBlank()) {
                        call.respond(HttpStatusCode.BadRequest, mapOf("error" to "Query parameter 'q' is required"))
                        return@get
                    }
                    try {
                        val urls = urlService.searchUrls(query).await()
                        call.respond(urls)
                    } catch (e: Exception) {
                        logger.error("Error searching URLs with query: $query", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }

                // Delete URL
                delete("/urls/{shortCode}") {
                    val shortCode = call.parameters["shortCode"] ?: ""
                    try {
                        val success = urlService.deleteUrl(shortCode).await()
                        if (success) {
                            call.respond(HttpStatusCode.OK, mapOf("message" to "URL deleted successfully"))
                        } else {
                            call.respond(HttpStatusCode.NotFound, mapOf("error" to "URL not found"))
                        }
                    } catch (e: Exception) {
                        logger.error("Error deleting URL: $shortCode", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }

                // Get analytics
                get("/analytics") {
                    try {
                        val analytics = analyticsService.getAnalytics().await()
                        call.respond(analytics)
                    } catch (e: Exception) {
                        logger.error("Error getting analytics", e)
                        call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                    }
                }
            }

            // Health check
            get("/health") {
                call.respond(HttpStatusCode.OK, mapOf("status" to "healthy", "service" to "shorturl"))
            }

            // Legacy shorten endpoint (for backward compatibility)
            post("/shorten") {
                try {
                    val request = call.receive<ShortenRequest>()
                    val clientIP = call.request.headers["X-Real-IP"]
                        ?: call.request.headers["X-Forwarded-For"]
                        ?: call.request.local.remoteHost
                    val response = urlService.shortenUrl(request, clientIP).await()
                    call.respond(response)
                } catch (e: IllegalArgumentException) {
                    logger.error("Invalid request", e)
                    call.respond(HttpStatusCode.BadRequest, mapOf("error" to (e.message ?: "Invalid request")))
                } catch (e: Exception) {
                    logger.error("Error processing shorten request", e)
                    call.respond(HttpStatusCode.BadRequest, mapOf("error" to "Invalid JSON"))
                }
            }

            // Legacy stats endpoint
            get("/stats/{shortCode}") {
                val shortCode = call.parameters["shortCode"] ?: ""
                val clientIP = call.request.headers["X-Real-IP"]
                    ?: call.request.headers["X-Forwarded-For"]
                    ?: call.request.local.remoteHost
                try {
                    val urlDoc = urlService.getUrlStats(shortCode, clientIP).await()
                    call.respond(urlDoc)
                } catch (e: NoSuchElementException) {
                    logger.error("Error getting stats for $shortCode", e)
                    call.respond(HttpStatusCode.NotFound, mapOf("error" to "URL not found"))
                } catch (e: Exception) {
                    logger.error("Error getting stats for $shortCode", e)
                    call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                }
            }

            // URL redirect
            get("/{shortCode}") {
                val shortCode = call.parameters["shortCode"] ?: ""
                val currentHost = call.request.headers["Host"] ?: ""
                val clientIP = call.request.headers["X-Real-IP"]
                    ?: call.request.headers["X-Forwarded-For"]
                    ?: call.request.local.remoteHost
                val userAgent = call.request.headers["User-Agent"] ?: ""
                val referer = call.request.headers["Referer"] ?: ""
                try {
                    when (val result =
                        urlService.redirectUrl(shortCode, currentHost, clientIP, userAgent, referer).await()) {
                        is RedirectResult.SameDomain -> {
                            val scheme = if (call.request.headers["X-Forwarded-Proto"] == "https") "https" else "http"
                            val redirectUrl = "$scheme://$currentHost${result.url}"
                            call.respondRedirect(redirectUrl, permanent = true)
                        }

                        is RedirectResult.ExternalDomain -> {
                            call.respond(
                                FreeMarkerContent(
                                    "redirect-warning.ftl",
                                    mapOf("targetUrl" to result.url)
                                )
                            )
                        }
                    }
                } catch (e: NoSuchElementException) {
                    logger.error("Error redirecting for $shortCode", e)
                    call.respond(HttpStatusCode.NotFound, mapOf("error" to "URL not found"))
                } catch (e: Exception) {
                    logger.error("Error redirecting for $shortCode", e)
                    call.respond(HttpStatusCode.InternalServerError, mapOf("error" to "Internal server error"))
                }
            }
        }
    }
}
