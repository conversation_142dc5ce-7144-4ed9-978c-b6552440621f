<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Shortener Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#667eea',
                        secondary: '#764ba2',
                        accent: '#f093fb',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444'
                    },

                    backdropBlur: {
                        xs: '2px'
                    }
                }
            }
        }
    </script>
    <style>
        /* Liquid Glass Background */
        body {
            background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.8) 0%,
            rgba(118, 75, 162, 0.8) 25%,
            rgba(240, 147, 251, 0.8) 50%,
            rgba(16, 185, 129, 0.8) 75%,
            rgba(245, 158, 11, 0.8) 100%);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><filter id="noiseFilter"><feTurbulence type="fractalNoise" baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/><feColorMatrix in="colorNoise" type="saturate" values="0"/></filter></defs><rect width="100%" height="100%" filter="url(%23noiseFilter)" opacity="0.4"/></svg>') repeat;
            opacity: 0.1;
            pointer-events: none;
            z-index: -1;
        }

        /* Liquid Glass Effects */
        .liquid-glass {
            background: rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(255, 255, 255, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .liquid-glass-card {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(16px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .liquid-glass-input {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .liquid-glass-button {
            background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.8) 0%,
            rgba(118, 75, 162, 0.8) 100%);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .liquid-glass-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
            0 8px 16px rgba(0, 0, 0, 0.3);
        }

        /* Shimmer Effect - Only for hover states */
        .shimmer:hover {
            position: relative;
            overflow: hidden;
        }

        .shimmer:hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.2),
                    transparent
            );
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Glow Effects */
        .glow-primary {
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .glow-success {
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }

        .glow-danger {
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }
    </style>
</head>
<body class="min-h-screen transition-colors duration-300">
<!-- Navigation -->
<div class="absolute top-4 right-4 flex gap-2 z-10">
    <button onclick="refreshData()"
            class="liquid-glass hover-lift text-white px-4 py-2 rounded-xl transition-all duration-300 flex items-center gap-2">
        <i class="fas fa-sync-alt"></i>
        Refresh
    </button>
</div>

<!-- Main Content -->
<div class="flex items-center justify-center min-h-screen p-4">
    <div class="liquid-glass rounded-3xl p-8 md:p-12 max-w-6xl w-full mx-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
                <i class="fas fa-chart-bar text-white text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">Dashboard</h1>
            <p class="text-gray-200 text-lg">Monitor your URL shortening analytics and performance</p>
        </div>
        <!-- Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="liquid-glass-card text-center p-6 rounded-2xl hover-lift">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mb-3 shadow-lg">
                    <i class="fas fa-link text-white"></i>
                </div>
                <h3 class="font-semibold text-white mb-1">Total URLs</h3>
                <p class="text-2xl font-bold text-blue-300" id="totalUrls">-</p>
            </div>

            <div class="liquid-glass-card text-center p-6 rounded-2xl hover-lift">
                <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-full mb-3 shadow-lg">
                    <i class="fas fa-mouse-pointer text-white"></i>
                </div>
                <h3 class="font-semibold text-white mb-1">Total Clicks</h3>
                <p class="text-2xl font-bold text-green-300" id="totalClicks">-</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <form id="urlForm" class="space-y-6 mb-8">
            <div>
                <label for="urlInput" class="block text-sm font-semibold text-white mb-2">
                    <i class="fas fa-globe mr-2 text-blue-300"></i>
                    Create Short URL:
                </label>
                <div class="relative">
                    <input
                            type="text"
                            id="urlInput"
                            placeholder="https://example.com/very/long/url"
                            required
                            class="w-full px-4 py-4 liquid-glass-input text-white placeholder-gray-300 rounded-xl focus:ring-4 focus:ring-blue-400/30 outline-none transition-all duration-300 text-lg"
                    >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                        <i class="fas fa-link text-gray-300"></i>
                    </div>
                </div>
            </div>

            <button
                    type="submit"
                    id="submitBtn"
                    class="w-full liquid-glass-button text-white py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
            >
                    <span id="btnText" class="flex items-center justify-center gap-2">
                        <i class="fas fa-compress-alt"></i>
                        Generate Short URL
                    </span>
                <div id="loading" class="hidden flex items-center justify-center gap-2">
                    <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Generating...
                </div>
            </button>
        </form>

        <!-- Success Result -->
        <div id="result" class="hidden mb-8 p-6 liquid-glass-card rounded-xl glow-success">
            <div class="flex items-center gap-2 mb-4">
                <i class="fas fa-check-circle text-green-400 text-xl"></i>
                <h3 class="text-lg font-semibold text-green-300">Short URL Generated!</h3>
            </div>

            <div class="space-y-4">
                <div class="flex flex-col sm:flex-row gap-2">
                    <input
                            type="text"
                            id="shortUrl"
                            readonly
                            class="flex-1 px-4 py-3 liquid-glass-input text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-green-400/50"
                    >
                    <div class="flex gap-2 sm:w-auto w-full">
                        <button
                                type="button"
                                id="copyBtn"
                                class="liquid-glass-button text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 flex-1 sm:flex-none"
                        >
                            <i class="fas fa-copy"></i>
                        </button>
                        <button
                                type="button"
                                id="openBtn"
                                class="liquid-glass-button text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 flex-1 sm:flex-none"
                                onclick="openShortUrl()"
                        >
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>

                <div class="text-sm text-gray-300">
                    <strong>Original:</strong>
                    <span id="originalUrl" class="break-all"></span>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div id="error" class="hidden mb-8 p-6 liquid-glass-card rounded-xl glow-danger">
            <div class="flex items-center gap-2">
                <i class="fas fa-exclamation-circle text-red-400 text-xl"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-300">Error</h3>
                    <p class="text-red-400" id="errorMessage"></p>
                </div>
            </div>
        </div>

        <!-- Top URLs Chart -->
        <div class="grid grid-cols-1 gap-8 mb-8">
            <div class="liquid-glass-card rounded-xl">
                <div class="px-6 py-4 border-b border-white/10">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                        <h2 class="text-xl font-bold text-white">
                            <i class="fas fa-trophy text-yellow-400 mr-3"></i>
                            Top Performing URLs
                        </h2>
                    </div>
                </div>

                <div class="p-6">
                    <div id="topUrlsList" class="space-y-4">
                        <!-- Top URLs will be populated here -->
                    </div>
                </div>

                <!-- Pagination for Top URLs -->
                <div class="px-6 py-4 border-t border-white/10">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-300 font-medium" id="topPaginationInfo">
                            <!-- Top pagination info will be populated here -->
                        </div>
                        <div class="flex items-center gap-2" id="topPaginationControls">
                            <!-- Top pagination controls will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- URLs List -->
        <div class="liquid-glass-card rounded-xl">
            <div class="px-6 py-4 border-b border-white/10">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <h2 class="text-xl font-bold text-white">
                        <i class="fas fa-list text-blue-400 mr-3"></i>
                        All URLs
                    </h2>
                    <div class="mt-4 sm:mt-0 flex items-center gap-4">
                        <div class="relative">
                            <input
                                    type="text"
                                    id="searchInput"
                                    placeholder="Search URLs..."
                                    class="px-4 py-2 pl-10 liquid-glass-input text-white placeholder-gray-300 rounded-xl text-sm focus:ring-4 focus:ring-blue-400/30 transition-all duration-300 w-64"
                                    oninput="handleSearch()"
                            >
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-search text-gray-300"></i>
                            </div>
                            <button
                                    id="clearSearchBtn"
                                    onclick="clearSearch()"
                                    class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-300 hover:text-white transition-colors hidden"
                            >
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <select id="limitSelect" onchange="loadUrls()"
                                class="px-4 py-2 liquid-glass-input text-white rounded-xl text-sm focus:ring-4 focus:ring-blue-400/30 transition-all duration-300">
                            <option value="10">10 per page</option>
                            <option value="25">25 per page</option>
                            <option value="50">50 per page</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div id="urlsList" class="space-y-4">
                    <!-- URLs will be populated here -->
                </div>
            </div>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-white/10">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-300 font-medium" id="paginationInfo">
                        <!-- Pagination info will be populated here -->
                    </div>
                    <div class="flex items-center gap-2" id="paginationControls">
                        <!-- Pagination controls will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay"
     class="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50 hidden">
    <div class="liquid-glass rounded-3xl p-8 flex items-center gap-4 shadow-2xl">
        <div class="animate-spin rounded-full h-8 w-8 border-4 border-blue-400 border-t-transparent"></div>
        <span class="text-white font-medium text-lg">Loading...</span>
    </div>
</div>

<!-- Toast Notification -->
<div id="toast" class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 hidden">
    <div class="liquid-glass-card rounded-xl shadow-lg p-4 flex items-center gap-3">
        <div id="toastIcon"></div>
        <span id="toastMessage" class="text-sm text-white"></span>
    </div>
</div>

<script>
    const API_BASE = '/shorturl';

    // Dark mode is permanently enabled
    let currentPage = 1;
    let currentLimit = 10;
    let totalPages = 1;
    let searchQuery = '';
    let searchTimeout = null;
    let allUrls = [];

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function () {
        refreshData();
    });

    // Show loading overlay
    function showLoading() {
        document.getElementById('loadingOverlay').classList.remove('hidden');
    }

    // Hide loading overlay
    function hideLoading() {
        document.getElementById('loadingOverlay').classList.add('hidden');
    }

    // Show toast notification
    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const icon = document.getElementById('toastIcon');
        const messageEl = document.getElementById('toastMessage');

        if (type === 'success') {
            icon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        } else if (type === 'error') {
            icon.innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
        }

        messageEl.textContent = message;
        toast.classList.remove('hidden');

        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    // Form submission for URL shortening
    const form = document.getElementById('urlForm');
    const urlInput = document.getElementById('urlInput');
    const submitBtn = document.getElementById('submitBtn');
    const btnText = document.getElementById('btnText');
    const loading = document.getElementById('loading');
    const result = document.getElementById('result');
    const error = document.getElementById('error');
    const shortUrl = document.getElementById('shortUrl');
    const originalUrl = document.getElementById('originalUrl');
    const copyBtn = document.getElementById('copyBtn');
    const errorMessage = document.getElementById('errorMessage');

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const url = urlInput.value.trim();
        if (!url) return;

        // Show loading state
        setLoading(true);
        hideMessages();

        try {
            const response = await fetch(`${API_BASE}/api/shorten`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({url: url})
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to generate short URL');
            }

            // Show success result
            showResult(data.short_url, data.original);
            showToast('Short URL generated successfully!');

            // Refresh data to update analytics
            refreshData();

        } catch (err) {
            showError(err.message);
            showToast(err.message, 'error');
        } finally {
            setLoading(false);
        }
    });

    copyBtn.addEventListener('click', async () => {
        try {
            await navigator.clipboard.writeText(shortUrl.value);
            copyBtn.innerHTML = '<i class="fas fa-check"></i>';
            copyBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
            copyBtn.classList.add('bg-green-700');

            showToast('URL copied to clipboard!');

            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                copyBtn.classList.remove('bg-green-700');
                copyBtn.classList.add('bg-green-600', 'hover:bg-green-700');
            }, 2000);
        } catch (err) {
            // Fallback for older browsers
            shortUrl.select();
            document.execCommand('copy');
            copyBtn.innerHTML = '<i class="fas fa-check"></i>';
            showToast('URL copied to clipboard!');

            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
            }, 2000);
        }
    });

    // Open short URL in new tab
    function openShortUrl() {
        const url = shortUrl.value;
        if (url) {
            window.open(url, '_blank');
        }
    }

    function setLoading(isLoading) {
        if (isLoading) {
            submitBtn.disabled = true;
            btnText.classList.add('hidden');
            loading.classList.remove('hidden');
        } else {
            submitBtn.disabled = false;
            btnText.classList.remove('hidden');
            loading.classList.add('hidden');
        }
    }

    function showResult(shortUrlValue, originalUrlValue) {
        shortUrl.value = shortUrlValue;
        originalUrl.textContent = originalUrlValue;

        result.classList.remove('hidden');
        error.classList.add('hidden');
    }

    function showError(message) {
        errorMessage.textContent = message;
        error.classList.remove('hidden');
        result.classList.add('hidden');
    }

    function hideMessages() {
        result.classList.add('hidden');
        error.classList.add('hidden');
    }

    // Refresh all data
    async function refreshData() {
        showLoading();
        try {
            await Promise.all([
                loadAnalytics(),
                loadUrls(),
                loadTopUrls()
            ]);
        } catch (error) {
            console.error('Error refreshing data:', error);
            showToast('Error loading data', 'error');
        } finally {
            hideLoading();
        }
    }

    // Load analytics data
    async function loadAnalytics() {
        try {
            const response = await fetch(`${API_BASE}/api/analytics`);
            const data = await response.json();

            document.getElementById('totalUrls').textContent = data.totalUrls.toLocaleString();
            document.getElementById('totalClicks').textContent = data.totalClicks.toLocaleString();
        } catch (error) {
            console.error('Error loading analytics:', error);
        }
    }

    // Load URLs with pagination
    async function loadUrls(page = 1) {
        currentPage = page;
        currentLimit = parseInt(document.getElementById('limitSelect').value);

        // If there's an active search, use search instead
        if (searchQuery) {
            searchUrls(page);
            return;
        }

        try {
            const response = await fetch(`${API_BASE}/api/urls?page=${currentPage}&limit=${currentLimit}`);
            if (!response.ok) {
                throw new Error('Failed to load URLs');
            }

            const data = await response.json();
            totalPages = data.totalPages;
            allUrls = data;

            updateUrlsList(data);
            updatePagination(data);
        } catch (error) {
            console.error('Error loading URLs:', error);
            document.getElementById('urlsList').innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-3"></i>
                        <p class="text-red-600 dark:text-red-400 font-medium">Error loading URLs</p>
                        <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Please try again later</p>
                    </div>
                `;
        }
    }

    // Load top URLs
    async function loadTopUrls() {
        try {
            const response = await fetch(`${API_BASE}/api/analytics`);
            const data = await response.json();
            updateTopUrlsList(data.topUrls);
        } catch (error) {
            console.error('Error loading top URLs:', error);
        }
    }

    // Update URLs list
    function updateUrlsList(urls) {
        const container = document.getElementById('urlsList');
        container.innerHTML = '';

        // Handle both array and object with urls property
        const urlArray = Array.isArray(urls) ? urls : (urls.urls || []);

        if (urlArray.length === 0) {
            container.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-link text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 text-lg font-medium">No URLs found</p>
                        <p class="text-gray-400 dark:text-gray-500 text-sm mt-2">Create your first short URL above!</p>
                    </div>
                `;
            return;
        }

        urlArray.forEach((url, index) => {
            const shortCode = url.short.split('/').pop();
            const createdDate = new Date(url.created).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
            const truncatedOriginal = url.original.length > 60 ?
                url.original.substring(0, 60) + '...' : url.original;

            const card = document.createElement('div');
            card.className = 'glass-effect bg-white/50 dark:bg-gray-700/50 rounded-xl p-6 border border-white/30 dark:border-gray-600/30 hover-lift transition-all duration-300 group';

            card.innerHTML = `
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start gap-3">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-blue-400 to-blue-600 text-white shadow-md group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-link text-sm"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-1 truncate" title="${url.original}">
                                        ${truncatedOriginal}
                                    </h3>
                                    <div class="flex items-center gap-2 mb-2">
                                        <span class="text-sm text-primary dark:text-accent font-mono bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-lg">
                                            ${url.short}
                                        </span>
                                        <button onclick="copyUrl('${url.short}')" class="text-gray-400 hover:text-primary dark:hover:text-accent transition-colors duration-200 hover:scale-110" title="Copy URL">
                                            <i class="fas fa-copy text-xs"></i>
                                        </button>
                                        <button onclick="openUrl('${url.short}')" class="text-gray-400 hover:text-green-500 dark:hover:text-green-400 transition-colors duration-200 hover:scale-110" title="Open URL">
                                            <i class="fas fa-external-link-alt text-xs"></i>
                                        </button>
                                    </div>
                                    <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-calendar-alt"></i>
                                            ${createdDate}
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-mouse-pointer"></i>
                                            ${url.clicks} clicks
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <div class="text-center">
                                <div class="text-2xl font-bold bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent">
                                    ${url.clicks}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 font-medium">Clicks</div>
                            </div>
                            <button onclick="deleteUrl('${shortCode}')" class="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200 hover:scale-110" title="Delete URL">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                    </div>
                `;

            container.appendChild(card);
        });
    }

    // Update pagination
    function updatePagination(data) {
        const info = document.getElementById('paginationInfo');
        const controls = document.getElementById('paginationControls');

        const start = (data.page - 1) * data.limit + 1;
        const end = Math.min(data.page * data.limit, data.total);

        info.innerHTML = `
                <span class="flex items-center gap-2">
                    <i class="fas fa-info-circle text-primary"></i>
                    Showing <span class="font-bold text-primary">${start}</span> to <span class="font-bold text-primary">${end}</span> of <span class="font-bold text-primary">${data.total}</span> results
                </span>
            `;

        controls.innerHTML = '';

        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.className = [
            "px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 border-2 border-gray-200 dark:border-gray-600 rounded-l-xl hover:bg-white dark:hover:bg-gray-600 transition-all duration-300 backdrop-blur-sm",
            data.page === 1 ? "cursor-not-allowed opacity-50" : "hover-lift"
        ].join(" ");
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = data.page === 1;
        prevBtn.onclick = () => data.page > 1 && loadUrls(data.page - 1);
        controls.appendChild(prevBtn);

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, data.page - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(data.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = [
                "px-4 py-2 text-sm font-medium border-t-2 border-b-2 border-r-2 border-gray-200 dark:border-gray-600 transition-all duration-300 backdrop-blur-sm",
                i === data.page
                    ? "gradient-bg text-white border-primary shadow-lg"
                    : "text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 hover:bg-white dark:hover:bg-gray-600 hover-lift"
            ].join(" ");
            pageBtn.textContent = i;
            pageBtn.onclick = () => loadUrls(i);
            controls.appendChild(pageBtn);
        }

        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.className = [
            "px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 border-2 border-gray-200 dark:border-gray-600 rounded-r-xl hover:bg-white dark:hover:bg-gray-600 transition-all duration-300 backdrop-blur-sm",
            data.page === data.totalPages ? "cursor-not-allowed opacity-50" : "hover-lift"
        ].join(" ");
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = data.page === data.totalPages;
        nextBtn.onclick = () => data.page < data.totalPages && loadUrls(data.page + 1);
        controls.appendChild(nextBtn);
    }

    // Update top URLs list
    function updateTopUrlsList(topUrls) {
        const container = document.getElementById('topUrlsList');
        container.innerHTML = '';

        if (topUrls.length === 0) {
            container.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-trophy text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                        <p class="text-gray-500 dark:text-gray-400 font-medium">No top URLs yet</p>
                        <p class="text-gray-400 dark:text-gray-500 text-sm mt-1">Start creating URLs to see top performers!</p>
                    </div>
                `;
            return;
        }

        topUrls.forEach((url, index) => {
            const truncatedOriginal = url.original.length > 35 ?
                url.original.substring(0, 35) + '...' : url.original;

            const item = document.createElement('div');
            item.className = 'glass-effect bg-white/50 dark:bg-gray-700/50 rounded-xl p-4 border border-white/30 dark:border-gray-600/30 hover-lift transition-all duration-300';

            const rankColors = [
                'from-yellow-400 to-yellow-600',
                'from-gray-400 to-gray-600',
                'from-orange-400 to-orange-600',
                'from-blue-400 to-blue-600',
                'from-purple-400 to-purple-600'
            ];

            item.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3 flex-1 min-w-0">
                            <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-br ${rankColors[index] || 'from-gray-400 to-gray-600'} text-white rounded-full text-sm font-bold shadow-lg">
                                ${index + 1}
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 dark:text-white truncate" title="${url.original}">
                                    ${truncatedOriginal}
                                </p>
                                <div class="flex items-center gap-2 mt-1">
                                    <p class="text-xs text-primary dark:text-accent font-mono bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded">
                                        ${url.short}
                                    </p>
                                    <button onclick="copyUrl('${url.short}')" class="text-gray-400 hover:text-primary dark:hover:text-accent transition-colors duration-200 hover:scale-110" title="Copy URL">
                                        <i class="fas fa-copy text-xs"></i>
                                    </button>
                                    <button onclick="openUrl('${url.short}')" class="text-gray-400 hover:text-green-500 dark:hover:text-green-400 transition-colors duration-200 hover:scale-110" title="Open URL">
                                        <i class="fas fa-external-link-alt text-xs"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="text-right ml-3">
                            <div class="text-lg font-bold bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent">
                                ${url.clicks}
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 font-medium">clicks</div>
                        </div>
                    </div>
                `;

            container.appendChild(item);
        });
    }

    // Shorten URL
    async function shortenUrl() {
        const urlInput = document.getElementById('urlInput');
        const url = urlInput.value.trim();

        if (!url) {
            showToast('Please enter a URL', 'error');
            return;
        }

        try {
            const response = await fetch(`${API_BASE}/api/shorten`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({url: url})
            });

            if (!response.ok) {
                throw new Error('Failed to shorten URL');
            }

            const data = await response.json();

            document.getElementById('shortUrlResult').value = data.short_url;
            document.getElementById('shortenResult').classList.remove('hidden');
            urlInput.value = '';

            showToast('URL shortened successfully!');

            // Refresh data to show the new URL
            setTimeout(() => {
                refreshData();
            }, 1000);

        } catch (error) {
            console.error('Error shortening URL:', error);
            showToast('Error shortening URL', 'error');
        }
    }

    // Copy URL to clipboard
    async function copyUrl(url) {
        try {
            await navigator.clipboard.writeText(url);
            showToast('URL copied to clipboard!');
        } catch (error) {
            console.error('Error copying URL:', error);
            showToast('Error copying URL', 'error');
        }
    }

    // Open URL in new tab
    function openUrl(url) {
        window.open(url, '_blank', 'noopener,noreferrer');
    }

    // Copy to clipboard from result
    function copyToClipboard() {
        const input = document.getElementById('shortUrlResult');
        input.select();
        document.execCommand('copy');
        showToast('URL copied to clipboard!');
    }

    // Delete URL
    async function deleteUrl(shortCode) {
        if (!confirm('Are you sure you want to delete this URL?')) {
            return;
        }

        try {
            const response = await fetch(`${API_BASE}/api/urls/${shortCode}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error('Failed to delete URL');
            }

            showToast('URL deleted successfully!');
            refreshData();

        } catch (error) {
            console.error('Error deleting URL:', error);
            showToast('Error deleting URL', 'error');
        }
    }

    // Search functionality
    function handleSearch() {
        const searchInput = document.getElementById('searchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        searchQuery = searchInput.value.trim();

        // Show/hide clear button
        if (searchQuery) {
            clearBtn.classList.remove('hidden');
        } else {
            clearBtn.classList.add('hidden');
        }

        // Debounce search
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        searchTimeout = setTimeout(() => {
            if (searchQuery) {
                searchUrls(1);
            } else {
                // Reset to show all URLs
                loadUrls(1);
            }
        }, 300);
    }

    // Search URLs using backend API
    async function searchUrls(page = 1) {
        currentPage = page;
        currentLimit = parseInt(document.getElementById('limitSelect').value);

        try {
            const response = await fetch(`${API_BASE}/api/urls/search?q=${encodeURIComponent(searchQuery)}&page=${currentPage}&limit=${currentLimit}`);
            if (!response.ok) {
                throw new Error('Failed to search URLs');
            }

            const data = await response.json();
            totalPages = data.totalPages;

            updateUrlsList(data.urls);
            updateSearchPagination(data);
        } catch (error) {
            console.error('Error searching URLs:', error);
            document.getElementById('urlsList').innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-3"></i>
                        <p class="text-red-600 dark:text-red-400 font-medium">Error searching URLs</p>
                        <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Please try again later</p>
                    </div>
                `;
        }
    }

    // Update pagination for search results
    function updateSearchPagination(data) {
        const info = document.getElementById('paginationInfo');
        const controls = document.getElementById('paginationControls');

        const start = (data.page - 1) * data.limit + 1;
        const end = Math.min(data.page * data.limit, data.total);

        info.innerHTML = `
                <span class="flex items-center gap-2">
                    <i class="fas fa-search text-primary"></i>
                    Search results: <span class="font-bold text-primary">${start}</span> to <span class="font-bold text-primary">${end}</span> of <span class="font-bold text-primary">${data.total}</span> for "<span class="font-bold text-primary">${data.query}</span>"
                </span>
            `;

        controls.innerHTML = '';

        if (data.totalPages <= 1) {
            return;
        }

        // Previous button
        const prevBtn = document.createElement('button');
        prevBtn.className = `px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 border-2 border-gray-200 dark:border-gray-600 rounded-l-xl hover:bg-white dark:hover:bg-gray-600 transition-all duration-300 backdrop-blur-sm ${
            data.page === 1 ? 'cursor-not-allowed opacity-50' : 'hover-lift'
        }`;
        prevBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
        prevBtn.disabled = data.page === 1;
        prevBtn.onclick = () => data.page > 1 && searchUrls(data.page - 1);
        controls.appendChild(prevBtn);

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, data.page - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(data.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = [
                "px-4 py-2 text-sm font-medium border-t-2 border-b-2 border-r-2 border-gray-200 dark:border-gray-600 transition-all duration-300 backdrop-blur-sm",
                i === data.page
                    ? "gradient-bg text-white border-primary shadow-lg"
                    : "text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 hover:bg-white dark:hover:bg-gray-600 hover-lift"
            ].join(" ");
            pageBtn.textContent = i;
            pageBtn.onclick = () => searchUrls(i);
            controls.appendChild(pageBtn);
        }

        // Next button
        const nextBtn = document.createElement('button');
        nextBtn.className = [
            "px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 bg-white/50 dark:bg-gray-700/50 border-2 border-gray-200 dark:border-gray-600 rounded-r-xl hover:bg-white dark:hover:bg-gray-600 transition-all duration-300 backdrop-blur-sm",
            data.page === data.totalPages ? "cursor-not-allowed opacity-50" : "hover-lift"
        ].join(" ");
        nextBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
        nextBtn.disabled = data.page === data.totalPages;
        nextBtn.onclick = () => data.page < data.totalPages && searchUrls(data.page + 1);
        controls.appendChild(nextBtn);
    }

    // Legacy client-side filter function (kept for fallback)
    function filterUrls() {
        if (!searchQuery || !allUrls.length) {
            return;
        }

        const filteredUrls = allUrls.filter(url => {
            return url.original.toLowerCase().includes(searchQuery.toLowerCase()) ||
                url.short.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (url.title && url.title.toLowerCase().includes(searchQuery.toLowerCase()));
        });

        // Create filtered data object
        const filteredData = {
            urls: filteredUrls,
            page: 1,
            limit: currentLimit,
            total: filteredUrls.length,
            totalPages: Math.ceil(filteredUrls.length / currentLimit)
        };

        updateUrlsList(filteredData.urls);
        updatePagination(filteredData);
    }

    function clearSearch() {
        const searchInput = document.getElementById('searchInput');
        const clearBtn = document.getElementById('clearSearchBtn');

        searchInput.value = '';
        searchQuery = '';
        clearBtn.classList.add('hidden');

        // Reset to show all URLs
        loadUrls(1);
    }

    // Handle Enter key in search input
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    handleSearch();
                }
            });
        }
    });
</script>
</body>
</html>

