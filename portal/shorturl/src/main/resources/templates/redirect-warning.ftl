<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>External Redirect Warning</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#64748B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center transition-colors duration-300">
    <!-- Dark Mode Toggle -->
    <button onclick="toggleDarkMode()" class="fixed top-4 right-4 p-3 rounded-lg bg-white dark:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700" title="Toggle dark mode">
        <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path class="dark:hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
            <path class="hidden dark:block" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
        </svg>
    </button>

    <div class="max-w-md w-full mx-4">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center transition-colors duration-300">
            <div class="mb-6">
                <div class="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">External Redirect Warning</h1>
                <p class="text-gray-600 dark:text-gray-300 mb-4">You are about to be redirected to an external website:</p>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 mb-4">
                    <p class="text-sm text-gray-800 dark:text-gray-200 break-all font-mono">${targetUrl}</p>
                </div>
            </div>
            
            <div class="mb-6">
                <div class="text-4xl font-bold text-primary mb-2" id="countdown">5</div>
                <p class="text-gray-600 dark:text-gray-300">Redirecting automatically in <span id="countdown-text">5</span> seconds...</p>
            </div>
            
            <div class="space-y-3">
                <button onclick="redirectNow()" class="w-full bg-primary hover:bg-secondary text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                    Continue Now
                </button>
                <button onclick="goBack()" class="w-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-semibold py-3 px-6 rounded-lg transition duration-200">
                    Go Back
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // Dark mode functionality
        function toggleDarkMode() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
            } else {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
            }
        }

        // Initialize dark mode based on saved preference
        function initDarkMode() {
            const savedMode = localStorage.getItem('darkMode');
            if (savedMode === 'true' || (!savedMode && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        }

        // Initialize dark mode on page load
        initDarkMode();

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('darkMode')) {
                if (e.matches) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }
            }
        });

        let countdown = 5;
        const targetURL = '${targetUrl}';
        
        function updateCountdown() {
            document.getElementById('countdown').textContent = countdown;
            document.getElementById('countdown-text').textContent = countdown;
            
            if (countdown <= 0) {
                redirectNow();
                return;
            }
            
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
        
        function redirectNow() {
            window.location.href = targetURL;
        }
        
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        
        // Start countdown
        updateCountdown();
    </script>
</body>
</html>
