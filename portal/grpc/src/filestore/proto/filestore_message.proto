syntax = 'proto3';

package proto.portal.filestore;

import "google/rpc/status.proto";

message UploadTokenRequest {
    repeated string allow_types = 1;
    int32 max_size = 2;
    optional string upload_user_id = 3;
    optional string override_file_url = 4;
    optional bool include_download_url = 5;
}

message UploadTokenResponse {
    google.rpc.Status status = 1;
    string upload_token = 2;
    optional string download_url = 3;
}