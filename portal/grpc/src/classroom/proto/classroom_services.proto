syntax = 'proto3';

package proto.portal.classroom;

import "classroom_messages.proto";
import "google/protobuf/empty.proto";

service ClassroomService {
  rpc CreateClassroomActivity   (CreateClassroomActivityRequest)
      returns (CreateClassroomActivityResponse);
  rpc LoadClassroomActivity   (LoadClassroomActivityRequest)
      returns (LoadClassroomActivityResponse);
  rpc GetClassroomActivityById    (GetClassroomActivityByIdRequest)
      returns (GetClassroomActivityByIdResponse);
  rpc UpdateClassroomActivityStatus   (UpdateClassroomActivityStatusRequest)
      returns (UpdateClassroomActivityStatusResponse);
  rpc UpdateQuickQuestionActivity   (UpdateQuickQuestionActivityRequest)
      returns (UpdateQuickQuestionActivityResponse);
  rpc UpdateRequestPresentationActivity   (UpdateRequestPresentationActivityRequest)
      returns (UpdateRequestPresentationActivityResponse);
  rpc GetLatestOngoingPresentationRequestActivity   (GetLatestOngoingPresentationRequestActivityRequest)
      returns (GetLatestOngoingPresentationRequestActivityResponse);
  rpc EndClassroom    (EndClassroomRequest)
      returns (EndClassroomResponse);
}
