{"notificationPackages": ["portal.notification.pojo", "portal.notification.pojo.notidata"], "serverConf": {"port": 1155}, "dbConf": {"connectionString": "mongodb://localhost:27017", "dbName": "viclass"}, "kafkaConf": {"processPoolSize": 10, "producerConfigs": [{"producerName": "notifications-producer", "propertiesPath": "conf/kafka.producer.properties", "eventToTopicMap": {"NotificationEvent": ["notifications"]}}]}, "mailerConf": {"sender": "<EMAIL>", "senderName": "viclass", "maxAttempts": 3, "retryAfterSeconds": 15, "sendingBuffer": 100, "retryingBuffer": 100}}