package portal.notification.pojo.email

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty

data class EmailTemplate @BsonCreator constructor(
    @BsonProperty("templateId")
    val templateId: String,

    @BsonProperty("content")
    val content: String,

    /**
     * ID of the referred templates.
     * Useful for reusable parts like email header, footer, logos,...etc...
     */
    @BsonProperty("referTemplateIds")
    val referTemplateIds: List<String> = listOf(),

    @BsonProperty("html")
    val isHtml: Boolean = true,
)