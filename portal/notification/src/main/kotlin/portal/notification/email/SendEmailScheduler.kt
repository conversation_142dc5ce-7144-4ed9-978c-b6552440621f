package portal.notification.email

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class SendEmailScheduler constructor(
    private val sendEmailService: SendEmailService
) {
    private val sendingScope = CoroutineScope(Dispatchers.IO)
    private val retryingScope = CoroutineScope(Dispatchers.IO)

    fun startSchedulers() {
        // consume sending emails
        sendingScope.launch {
            sendEmailService.sendingChannel.consumeEach {
                sendEmailService.sendEmail(it)
            }
        }

        // consume sending emails
        retryingScope.launch {
            sendEmailService.retryingChannel.consumeEach {
                sendEmailService.retryEmail(it)
            }
        }
    }

    fun stopSchedulers() {
        sendingScope.cancel()
        retryingScope.cancel()
    }
}