package portal.notification

import common.libs.logger.Logging
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.core.context.GlobalContext
import portal.notification.koin.koinApplication

object Launcher : Logging {

    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching notification service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        GlobalContext.startKoin(koinApplication)

        val server = GlobalContext.getKoinApplicationOrNull()!!.koin.get<NotificationServer>()

        // start server
        server.start()


        //Add a shutdown hook so that if the JVM is stopped the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down notification service")
            server.shutdown()
            logger.info("Shutdown notification service")
        })


        server.blockUntilShutdown()
    }
}