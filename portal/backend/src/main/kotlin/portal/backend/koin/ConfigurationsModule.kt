package portal.backend.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.captcha.CaptchaConf
import common.libs.captcha.CaptchaUtil
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.jackson.*
import io.lettuce.core.RedisClient
import io.lettuce.core.api.sync.RedisCommands
import jayeson.utility.JacksonConfig
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import portal.backend.configs.*

@Module
class ConfigurationsModule {

    private val config: ConfigBean

    private val CONFIG_PATH = "conf/config.json"
    private val CONFIG_VAR = "ViClassBackendConf"

    init {
        config = loadConfig()
    }

    companion object {
        private const val CONFIG_PATH = "conf/config.json"
        private const val CONFIG_VAR = "ViClassBackendConf"
    }

    private fun loadConfig(): ConfigBean {
        return try {
            val config: ConfigBean = JacksonConfig.readConfig(
                CONFIG_PATH, CONFIG_VAR, ConfigBean::class.java, jacksonObjectMapper()
            )
            config
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }

    @Single
    fun provideConfigBean(): ConfigBean {
        return config
    }

    @Single
    fun provideServiceExplorerConfig(): ServiceExplorerConfig {
        return config.seConf
    }

    @Single
    fun provideCacheServiceConfig(): CacheServiceConfig {
        return config.cacheServiceConf
    }

    @Single
    fun provideJobrunrConfig(): JobRunrServiceConfig {
        return config.jobRunrServiceConf
    }


    @Single
    fun provideProfileDocMetadataConfig(): ProfileDocMetadataConfig {
        return config.profileDocMetadataConfig
    }

    @Single
    fun provideDeleteShareWithMeConfiguration(): DeleteShareWithMeConfig {
        return config.deleteShareWithMeConf
    }


    @Single
    fun provideHttpClient(): HttpClient {
        return HttpClient(CIO) {
            install(ContentNegotiation) {
                jackson {}
            }
        }
    }

    @Single
    fun provideRedisCommands(): RedisCommands<String, String> {
        return RedisClient.create("${config.cacheServiceConf.host}:${config.cacheServiceConf.port}").connect().sync()
    }

    @Single
    fun provideCaptchaConfig(): CaptchaConf {
        return config.captchaConf
    }

    @Single
    fun provideCaptchaUtil(httpClient: HttpClient, config: CaptchaConf): CaptchaUtil {
        return CaptchaUtil(httpClient, config)
    }
}
