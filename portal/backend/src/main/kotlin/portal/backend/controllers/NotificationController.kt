package portal.backend.controllers

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.ktor.router.requireLogin
import org.koin.ktor.ext.inject
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.gateways.ILSessionServiceGateway
import portal.backend.gateways.NotificationServiceGateway
import portal.backend.models.LoadClassroomNotificationModel
import portal.backend.controllers.router.retrieveProfile

fun Application.NotificationController() {
    val lsessionSG: ILSessionServiceGateway by inject()
    val notificationSG: NotificationServiceGateway by inject()
    val mapper = jacksonObjectMapper()

    routing {
        route("/api/notification") {
            requireLogin {
                retrieveProfile {
                    post("/fetch") {
                        try {
                            val model = call.receive<LoadClassroomNotificationModel>()
                            val profile = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val result = notificationSG.loadClassroomNotification(model.lsId, profile.user.id)

                            return@post call.respond(HttpStatusCode.OK, mapper.readTree(result.toString()))
                        } catch (t: Throwable) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "message" to t.message, "backendErrorCode" to Status.INTERNAL.code.value()
                                )
                            )
                        }
                    }
                }
            }

            requireLogin {
                get("{notiId}/fetch ") {
                    val notiId = call.parameters.getOrFail("notiId")
                    val result = notificationSG.loadNotificationById(notiId)

                    return@get call.respond(HttpStatusCode.OK, mapper.readTree(result))
                }
            }
        }
    }
}