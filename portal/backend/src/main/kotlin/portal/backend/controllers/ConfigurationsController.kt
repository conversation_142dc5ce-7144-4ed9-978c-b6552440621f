package portal.backend.controllers

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.koin.ktor.ext.inject
import portal.backend.gateways.IConfigurationsServiceGateway
import proto.portal.configurations.ConfigurationsMessages


fun Application.configurationsController() {
    val confService: IConfigurationsServiceGateway by inject()
    val mapper = jacksonObjectMapper()

    routing {
        route("/api") {
            post("/config/fetch") {
                try {
                    val json = call.receiveNullable<JsonNode>()
                    val fields = json?.get("fields")?.elements()?.asSequence()?.map { it.asText() }?.toList()

                    try {
                        val conf = confService.loadAll()
                        val node: ObjectNode = mapper.createObjectNode()

                        val map = conf.configsMap.filterKeys {
                            return@filterKeys (fields.isNullOrEmpty() || fields.contains(it))
                        }

                        for (entry: Map.Entry<String, ConfigurationsMessages.DataConfigurationsProto> in map) {
                            val jsonNode = mapper.readTree(entry.value.values)
                            val s: String = entry.key
                            node.set<JsonNode>(s, jsonNode)
                        }

                        return@post call.respond(HttpStatusCode.OK, node)
                    } catch (t: Throwable) {
                        return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                    }
                } catch (t: Throwable) {
                    return@post call.respond(HttpStatusCode.BadRequest, message = t.message.toString())
                }
            }
        }
    }
}