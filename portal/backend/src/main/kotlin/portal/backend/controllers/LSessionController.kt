package portal.backend.controllers

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.typesafe.config.Config
import common.libs.captcha.CaptchaUtil
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.ktor.router.requireLogin
import kotlinx.coroutines.future.await
import org.bson.types.ObjectId
import org.koin.ktor.ext.inject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.backend.configs.ConfigBean
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.ILSessionServiceGateway
import portal.backend.gateways.IUserServiceGateway
import portal.backend.gateways.NotificationServiceGateway
import portal.backend.models.BriefUserProfile
import portal.backend.models.LSessionContainer
import portal.backend.models.LSessionSummary
import portal.backend.models.SearchLSessionSummaryForm
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.*
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.MultiTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.UserTarget
import portal.notification.pojo.notidata.AcceptRegistrationND
import portal.notification.pojo.notidata.RejectRaiseHandND
import portal.notification.pojo.notidata.RejectRegistrationND
import proto.portal.lsession.LsessionMessage
import proto.portal.user.UserMessage
import java.time.Instant


fun Application.LSessionController() {
    val logger: Logger = LoggerFactory.getLogger(javaClass.name)

    val lsService: ILSessionServiceGateway by inject()
    val userService: IUserServiceGateway by inject()
    val config: Config by inject()
    val configBean: ConfigBean by inject()
    val notificationSG: NotificationServiceGateway by inject()
    val captcha: CaptchaUtil by inject()
    val mapper = jacksonObjectMapper()

    fun buildSummaryListResult(
        summariesRes: LsessionMessage.GetAllLSessionsSummaryResponse,
        profilesRes: UserMessage.GetUserProfileByIdsResponse,
    ): ArrayNode {
        val summaries = summariesRes.lsessionsList
        val profilesMap = profilesRes.userProfileList.associateBy { it.id }

        val arr = summaries.filter { profilesMap.containsKey(it.details.creatorId) }.map {
            val profileProto = profilesMap[it.details.creatorId]!!
            val creator = BriefUserProfile(profileProto.id, profileProto.username, profileProto.avatarUrl)

            val summary = LSessionSummary(
                it.lsId,
                it.details.title,
                it.details.grade,
                it.details.subject,
                it.details.imgUrl,
                it.details.startDate * 1000 + it.details.startTime * 1000,
                it.details.expectedDuration,
                it.settings.maxRegistration,
                if (it.details.hasState()) it.details.state.toPojo() else null,
                if (it.hasRegistration()) it.registration.id else null,
                if (it.hasRegistration()) LSRegStatus.valueOf(it.registration.regStatus) else null
            )

            LSessionContainer(creator, summary)
        }.foldRight(mapper.createArrayNode() as ArrayNode) { ls, arr ->
            arr.addPOJO(ls)
            arr
        }

        return arr
    }

    fun saveNotification(notification: Notification) {
        notificationSG.saveNotification(notification).handle { _, t ->
            if (t != null) {
                println("failed to save notification ${t}")
            }
        }
    }

    routing {
        route("/api/lsession") {
            requireLogin {
                retrieveProfile {
                    /**
                     * Api to create a new classroom
                     */
                    post("/create") {
                        try {
                            val json = call.receive<JsonNode>()

                            if (configBean.enableCaptchaForCreateDocLSession) {
                                if (!json.has("captcha")) return@post call.respond(
                                    HttpStatusCode.BadRequest, message = "Missing captcha"
                                )
                                val isVerifiedCaptcha: Boolean = captcha.isVerified(json.get("captcha").asText())
                                if (!isVerifiedCaptcha) return@post call.respond(
                                    HttpStatusCode.BadRequest, "captcha error"
                                )
                            }

                            if (!json.has("details") || !json.has("settings")) return@post call.respond(
                                HttpStatusCode.BadRequest,
                                message = "Missing details or settings of the learning session"
                            )

                            val details = mapper.treeToValue(json.get("details"), LSessionDetails::class.java)
                            val settings = mapper.treeToValue(json.get("settings"), ClassroomSettings::class.java)

                            val registrationPojo = LSessionRegistration(
                                ObjectId().toHexString(),
                                details.id,
                                details.creatorId,
                                details.creatorId,
                                LSRegStatus.REGISTERED,
                                System.currentTimeMillis(),
                                ClassroomUserState(
                                    shareScreenStatus = ShareScreenStatus.NONE,
                                    raiseHandStatus = RaiseHandStatus.PRESENTING,
                                    availableStatus = UserAvailableStatus.OFFLINE,
                                    joinedTime = System.currentTimeMillis()
                                )
                            )

                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            details.creatorId = profileResponse.user.id
                            val res = lsService.createLSession(details, settings)

                            if (res.status.code != Status.Code.OK.value()) {
                                return@post call.respond(
                                    HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to res.status.code)
                                )
                            }
                            lsService.createSessionRegistration(registrationPojo)

                            return@post call.respond(HttpStatusCode.OK, mapOf("lSessionId" to details.id))

                        } catch (t: Throwable) {
                            logger.error("create lsession exception...", t)
                            return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                        }
                    }
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Api to update details of a classroom (i.g. title, settings, etc.)
                     */
                    post("/update") {
                        try {
                            val json = call.receive<JsonNode>()

                            if (!json.has("details") || !json.has("settings")) return@post call.respond(
                                HttpStatusCode.BadRequest,
                                message = "Missing details or settings of the learning session"
                            )

                            val details = mapper.treeToValue(json.get("details"), LSessionDetails::class.java)
                            val settings = mapper.treeToValue(json.get("settings"), ClassroomSettings::class.java)
                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            if (details.creatorId != profileResponse.user.id) {
                                return@post call.respond(
                                    HttpStatusCode.Unauthorized, message = "Users can only update their own sessions."
                                )
                            } else {
                                val detailRes = lsService.getLSession(details.id)

                                if (settings.maxRegistration > 0 && detailRes.lsessionDetails.state.registered > settings.maxRegistration) {
                                    return@post call.respond(
                                        HttpStatusCode.BadRequest,
                                        message = "Buổi học đã hết chỗ, vui lòng cập nhật lại số học viên tối đa"
                                    )
                                } else {
                                    val res = lsService.updateSessionDetails(details, settings)

                                    if (!res.isOk) {
                                        return@post call.respond(
                                            HttpStatusCode.InternalServerError,
                                            mapOf("backendErrorCode" to res.code.value())
                                        )
                                    } else {
                                        return@post call.respond(HttpStatusCode.OK, mapOf("lSessionId" to details.id))
                                    }
                                }
                            }
                        } catch (t: Throwable) {
                            logger.error("update lsession exception...", t)
                            return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                        }
                    }
                }
            }

            /**
             * Get classroom details by ID
             */
            get("/{lsId}/fetch") {
                try {
                    val lsId: String = call.parameters.getOrFail("lsId")
                    val res = lsService.getLSession(lsId)

                    if (res.status.code != Status.Code.OK.value()) {
                        return@get call.respond(
                            HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to res.status.code)
                        )
                    }

                    val pojo = res.lsessionDetails.toPojo()
                    pojo.id = res.lsId

                    return@get call.respond(HttpStatusCode.OK, mapper.writeValueAsString(pojo))
                } catch (t: Throwable) {
                    logger.error("load lsession exception...", t)
                    return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                }
            }

            /**
             * Get classroom settings by ID
             */
            get("/{lsId}/setting/fetch") {
                try {
                    val lsId: String = call.parameters.getOrFail("lsId")
                    val res = lsService.getClassroomSettings(lsId)

                    if (res.status.code != Status.Code.OK.value()) {
                        return@get call.respond(
                            HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to res.status.code)
                        )
                    }

                    return@get call.respond(HttpStatusCode.OK, mapper.writeValueAsString(res.settings.toPojo()))
                } catch (t: Throwable) {
                    logger.error("load lsession setting exception...", t)
                    return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                }
            }

            /**
             * Get summaries of all classrooms of the current user
             */
            post("/summaries") {
                try {
                    val opt = kotlin.runCatching { call.receiveNullable<SearchLSessionSummaryForm>() }.getOrNull()
                        ?: return@post call.respond(
                            HttpStatusCode.BadRequest, mapOf("backendErrorCode" to Status.Code.INVALID_ARGUMENT.value())
                        )

                    val res = lsService.getAllLSessionSummary(opt)
                    val ids = res.lsessionsList.map { it.details.creatorId }
                    val result = userService.getUserProfileByIds(ids)

                    return@post call.respond(HttpStatusCode.OK, buildSummaryListResult(res, result))
                } catch (t: Throwable) {
                    logger.error("search lsession exception...", t)
                    return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                }
            }

            requireLogin {
                /**
                 * upload classroom avatar
                 * @deprecated
                 */
                post("{lsId}/avatar/upload ") {
                    try {
                        val sessionId: String = call.parameters.getOrFail("lsId")

                        val body = call.receiveMultipart()

                        var fileDescription = ""
                        var fileName = ""
                        var fileBytes: ByteArray? = null
                        val folder: String = config.getString("avatar.session.folder")


                    } catch (t: Throwable) {
                        logger.error("upload lsession avatar exception...", t)
                        return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                    }
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Approve a classroom registration to allow it to join
                     */
                    get("/registration/{regId}/approve") {
                        try {
                            val regId: String = call.parameters.getOrFail("regId")
                            val regIdRes = lsService.getLSessionRegistrationById(regId)
                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            if (regIdRes.registration.lsOwner != profileResponse.user.id) return@get call.respond(
                                HttpStatusCode.Forbidden,
                                message = "You don't have permission to accept this registration"
                            )

                            if (!regIdRes.hasRegistration()) return@get call.respond(
                                HttpStatusCode.BadRequest,
                                message = "The user have not sent registration to this session"
                            )

                            val registration = regIdRes.registration
                            val oldStatus = LSRegStatus.valueOf(registration.regStatus)
                            val newStatus = LSRegStatus.REGISTERED

                            if (oldStatus == newStatus) return@get call.respond(
                                HttpStatusCode.OK, mapper.createObjectNode()
                            )

                            var error = false

                            if (!listOf(LSRegStatus.WAITING_CONFIRMED, LSRegStatus.REJECTED).contains(oldStatus)) {
                                error = true
                            }

                            if (error) return@get call.respond(
                                HttpStatusCode.BadRequest,
                                message = "Trang thái yêu cầu đăng ký buổi học đã thay đổi, tải lại trang để cập nhật"
                            )

                            val result = lsService.getSessionOnAllById(registration.lsId)
                            val setting = result.classroomSettings
                            val detail = result.lsessionDetails

                            if (setting.maxRegistration > 0 && detail.state.registered >= setting.maxRegistration) {
                                return@get call.respond(
                                    HttpStatusCode.BadRequest,
                                    message = "Buổi học đã hết chỗ, vui lòng cập nhật lại số học viên tối đa"
                                )
                            } else {
                                val updateRes = lsService.updateRegistrationRegStatus(regId, newStatus)

                                if (updateRes.hasStatus() && updateRes.status.code == Status.Code.OK.value()) {
                                    // init and save notification
                                    val extendedData = AcceptRegistrationND(detail.id, registration.userId)
                                    val notification = Notification(
                                        profileResponse.user.id,
                                        MultiTarget(
                                            listOf(UserTarget(registration.userId), ClassroomTarget(detail.id))
                                        ),
                                        "User ${registration.userId} have been approved registration for LSession ${detail.id}",
                                        Instant.now().plusSeconds(300),
                                        extendedData,
                                        false
                                    )
                                    saveNotification(notification)

                                    return@get call.respond(HttpStatusCode.OK, mapper.createObjectNode())
                                } else return@get call.respond(
                                    HttpStatusCode.InternalServerError, mapper.createObjectNode().put(
                                        "backendErrorCode", updateRes.status.code
                                    )
                                )
                            }
                        } catch (t: Throwable) {
                            logger.error("approve registration lsession exception...", t)
                            return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                        }
                    }
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Reject a classroom registration or kick-out an existing classroom member
                     */
                    get("/registration/{regId}/reject") {
                        val regId: String = call.parameters.getOrFail("regId")

                        try {
                            val mbIdRes = lsService.getLSessionRegistrationById(regId)
                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            if (mbIdRes.registration.lsOwner != profileResponse.user.id) return@get call.respond(
                                HttpStatusCode.Forbidden,
                                message = "You don't have permission to reject this registration"
                            )

                            if (!mbIdRes.hasRegistration()) return@get call.respond(
                                HttpStatusCode.BadRequest,
                                message = "The user have not sent registration to this session"
                            )

                            val registration = mbIdRes.registration
                            val oldStatus = LSRegStatus.valueOf(registration.regStatus)
                            val newStatus = LSRegStatus.REJECTED

                            if (oldStatus == newStatus) return@get call.respond(
                                HttpStatusCode.OK, mapper.createObjectNode()
                            )

                            if (!listOf(LSRegStatus.WAITING_CONFIRMED, LSRegStatus.REGISTERED).contains(oldStatus)) {
                                return@get call.respond(
                                    HttpStatusCode.BadRequest,
                                    message = "Trang thái yêu cầu đăng ký buổi học đã thay đổi, tải lại trang để cập nhật"
                                )
                            }

                            val userAvailableStatus = UserAvailableStatus.valueOf(registration.state.userStatus)

                            if (userAvailableStatus != UserAvailableStatus.OFFLINE) {
                                val result = lsService.updateUserAvailableStatusAsync(
                                    registration.id, registration.lsId, UserAvailableStatus.OFFLINE
                                ).await()
                                if (result.status.code != Status.Code.OK.value()) {
                                    return@get call.respond(
                                        HttpStatusCode.InternalServerError, mapper.createObjectNode()
                                            .put("backendErrorCode", result.status.code)
                                            .put("message", "Cập nhật trạng thái tham gia thất bại")
                                    )
                                }
                            }

                            val raiseHandStatus = RaiseHandStatus.valueOf(registration.state.status)
                            if (raiseHandStatus != RaiseHandStatus.NONE) {
                                val result = lsService.updateRaiseHandStatusAsync(
                                    registration.id, RaiseHandStatus.NONE
                                ).await()
                                if (result.status.code != Status.Code.OK.value()) {
                                    return@get call.respond(
                                        HttpStatusCode.InternalServerError, mapper.createObjectNode()
                                            .put("backendErrorCode", result.status.code)
                                            .put("message", "Cập nhật trạng thái hoạt động thất bại")
                                    )
                                }

                                val notiData = RejectRaiseHandND(registration.lsId, registration.userId)

                                val notification = Notification(
                                    profileResponse.user.id,
                                    ClassroomTarget(registration.lsId),
                                    "Học viên ${profileResponse.user.username} vừa bỏ tay xuống",
                                    Instant.now().plusSeconds(300),
                                    notiData
                                )
                                saveNotification(notification)
                            }

                            val updateRes = lsService.updateRegistrationRegStatus(regId, newStatus)

                            if (updateRes.hasStatus() && updateRes.status.code == Status.Code.OK.value()) {
                                // init and save notification
                                val extendedData = RejectRegistrationND(registration.lsId, registration.userId)
                                val notification = Notification(
                                    profileResponse.user.id,
                                    MultiTarget(
                                        listOf(
                                            UserTarget(registration.userId), ClassroomTarget(registration.lsId)
                                        )
                                    ),
                                    "User ${registration.userId} have been rejected registration for LSession ${registration.lsId}",
                                    Instant.now().plusSeconds(300),
                                    extendedData,
                                    false
                                )
                                saveNotification(notification)
                                return@get call.respond(HttpStatusCode.OK, mapper.createObjectNode())
                            } else return@get call.respond(
                                HttpStatusCode.InternalServerError, mapper.createObjectNode().put(
                                    "backendErrorCode", updateRes.status.code
                                )
                            )
                        } catch (t: Throwable) {
                            logger.error("reject registration {} lsession exception...", regId, t)
                            return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                        }
                    }
                }
            }
        }
    }
}
