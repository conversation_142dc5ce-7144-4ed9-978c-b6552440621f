package portal.backend.gateways

import io.grpc.ManagedChannel
import portal.backend.models.GetUploadTokenDto
import proto.portal.filestore.FileStoreServiceGrpcKt
import proto.portal.filestore.FilestoreMessage

class FileStoreServiceGateway(
    private var channel: ManagedChannel,
) : IFileStoreServiceGateway {

    override suspend fun getUploadToken(
        userId: String?,
        dto: GetUploadTokenDto,
        includeDownloadUrl: Boolean
    ): FilestoreMessage.UploadTokenResponse {
        val stub = FileStoreServiceGrpcKt.FileStoreServiceCoroutineStub(channel)
        val request = FilestoreMessage.UploadTokenRequest.newBuilder()
            .setMaxSize(dto.maxFileSize)
            .addAllAllowTypes(dto.allowFileTypes)
            .setIncludeDownloadUrl(includeDownloadUrl)

        if (userId != null)
            request.setUploadUserId(userId)
        if (dto.overrideFileUrl != null)
            request.setOverrideFileUrl(dto.overrideFileUrl)

        return stub.getUploadToken(request.build())
    }
}