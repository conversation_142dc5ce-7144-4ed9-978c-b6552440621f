package portal.beta.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import io.lettuce.core.RedisClient
import io.lettuce.core.api.sync.RedisCommands
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.beta.configuration.CacheServiceConf
import portal.beta.configuration.DatabaseConfig
import portal.beta.pojo.BetaInvitationPojo

@Module
@ComponentScan("portal.beta.dbgateway")
class DatabaseModule {
    companion object {
        const val MONGO_COLLECTION_BETA_INVITATIONS_PROFILE_POJO = "MongoCollectionBetaInvitationsProfilePojo"
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        val pojoCodecRegistry: CodecRegistry = CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                PojoCodecProvider.builder()
                    .automatic(true)
                    .build()
            )
        )
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    @Singleton
    @Named(MONGO_COLLECTION_BETA_INVITATIONS_PROFILE_POJO)
    fun provideBetaInvitationsCollection(database: MongoDatabase): MongoCollection<BetaInvitationPojo> {
        return database.getCollection("beta_invitations", BetaInvitationPojo::class.java)
    }

    @Singleton
    fun provideRedisCommands(cacheServiceConf: CacheServiceConf): RedisCommands<String, String> {
        return RedisClient.create("${cacheServiceConf.host}:${cacheServiceConf.port}").connect().sync()
    }
}
