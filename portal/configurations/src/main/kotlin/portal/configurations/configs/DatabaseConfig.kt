package portal.configurations.configs

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty

/**
 *
 * @property connectionString String
 *
 * <AUTHOR>
 */
data class DatabaseConfig @JsonCreator constructor(
    @JsonProperty("connectionString") val connectionString: String,
    @JsonProperty("dbName") val dbName: String,
    @JsonProperty("fieldMap") val fieldMap: Map<String, String>
)
