package portal.configurations

import common.libs.logger.Logging
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.core.context.GlobalContext.getKoinApplicationOrNull
import org.koin.core.context.GlobalContext.startKoin
import portal.configurations.koin.koinApplication

object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching configurations service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        startKoin(koinApplication)
        val server: ConfigurationsServer = getKoinApplicationOrNull()!!.koin.get<ConfigurationsServer>()

        // start account manager server
        server.start()

        //Add a shutdown hook so that if the JVM is stopped the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down configurations service")
            server.shutdown()
            logger.info("Shutdown configurations service")
        })

        server.blockUntilShutdown()
    }
}