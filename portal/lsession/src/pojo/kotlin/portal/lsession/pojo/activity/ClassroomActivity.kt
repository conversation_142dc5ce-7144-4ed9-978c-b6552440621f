package portal.lsession.pojo.activity

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import java.time.Instant

data class ClassroomActivity @BsonCreator constructor(
    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,

    @BsonProperty("createdBy")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val createdBy: String,

    @BsonProperty("data")
    val data: ActivityData,

    @BsonProperty("status")
    val status: ActivityStatus = ActivityStatus.ON_GOING,

    @BsonProperty("createdTime")
    val createdTime: Instant = Instant.now(),

    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    val id: String = ObjectId().toHexString(),
)
