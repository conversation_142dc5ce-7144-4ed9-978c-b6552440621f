package portal.lsession.koin

import org.koin.core.qualifier.named
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.koin.ksp.generated.module
import org.koin.logger.slf4jLogger
import portal.lsession.dbgateway.ClassroomDBGateway
import portal.lsession.dbgateway.ClassroomSettingGateway
import portal.lsession.dbgateway.SessionGateway
import portal.lsession.dbgateway.SessionRegistrationGateway
import portal.lsession.server.ClassroomService
import portal.lsession.server.Server
import portal.lsession.server.SessionService



val koinApplication = koinApplication {
    slf4jLogger()
    modules(
        ConfigurationModule().module,
        DatabaseModule().module,
        module {
            single<ClassroomDBGateway> {ClassroomDBGateway(get(named(DatabaseModule.CLASSROOM_ACTIVITIES_COLLECTION)))}
            single<ClassroomSettingGateway> { ClassroomSettingGateway(get(named(DatabaseModule.CLASSROOM_SETTING_COLLECTION))) }
            single<SessionGateway> { SessionGateway(get(named(DatabaseModule.SESSION_DETAILS_COLLECTION))) }
            single<SessionRegistrationGateway> { SessionRegistrationGateway(get(named(DatabaseModule.SESSION_REGISTRATION_COLLECTION))) }
            single<ClassroomService> { ClassroomService(get(), get(), get()) }
            single<SessionService> { SessionService(get(), get(), get()) }
            single<Server> {Server(get(), get(), get())}
        }
    )
}
