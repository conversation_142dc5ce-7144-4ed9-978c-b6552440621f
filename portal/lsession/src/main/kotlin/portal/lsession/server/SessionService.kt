package portal.lsession.server

import com.mongodb.DuplicateKeyException
import common.libs.logger.Logging
import io.grpc.Status
import io.reactivex.rxjava3.core.Single
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx3.await
import org.bson.types.ObjectId
import portal.datastructures.lsession.*
import portal.lsession.dbgateway.ClassroomSettingGateway
import portal.lsession.dbgateway.SessionGateway
import portal.lsession.dbgateway.SessionRegistrationGateway
import portal.lsession.pojo.*
import proto.portal.lsession.LSessionServiceGrpcKt
import proto.portal.lsession.LsessionMessage.*

/**
 *
 * <AUTHOR>
 */
class SessionService constructor(
    private val sessionGateway: SessionGateway,
    private val classroomSettingGateway: ClassroomSettingGateway,
    private val sessionRegistrationGateway: SessionRegistrationGateway,
) : LSessionServiceGrpcKt.LSessionServiceCoroutineImplBase(), Logging {

    override suspend fun createSession(request: CreateSessionRequest): CreateSessionResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = CreateSessionResponse.newBuilder()

        val lsDetailReq = request.lsessionDetails
        val clrSettingReq = request.classroomSettings

        try {
            val session = lsDetailReq.toPojo().apply {
                this.state = LSessionState(LSessionStatus.NOT_STARTED)
            }
            val lsId = sessionGateway.insertOne(session).await().insertedId!!.asObjectId().value
            builder.lsId = lsId.toHexString()

            try {
                val classroomSetting = clrSettingReq.toPojo().apply {
                    this.id = lsId.toHexString()
                }
                classroomSettingGateway.insertOne(classroomSetting).await()
                code = Status.Code.OK
            } catch (t: Throwable) {
                logger.error("save classroom setting {} failed... ", lsId, t)
                sessionGateway.deleteSessionById(lsId).await()
                code = Status.Code.INTERNAL
                message = "save classroom setting failed cause ${t.message}"
                return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message))
                    .build()
            }

        } catch (t: Throwable) {
            logger.error("save session failed...", t)
            message = t.message ?: ""
            if (message.startsWith("E11000")) code = Status.Code.ALREADY_EXISTS
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Get full session information including its registration data
     */
    override suspend fun getSessionOnAllById(request: GetSessionOnAllByIdRequest): GetSessionOnAllByIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId

        val builder = GetSessionOnAllByIdResponse.newBuilder()
            .setLsId(lsId)

        try {
            val lsDetail = sessionGateway.findSessionById(ObjectId(lsId)).await()
            builder.lsessionDetails = LSessionDetailsProto.newBuilder().fromPojo(lsDetail)

            val clrSetting = classroomSettingGateway.findClassroomSettingById(ObjectId(lsId)).await()
            builder.classroomSettings = ClassroomSettingsProto.newBuilder().fromPojo(clrSetting)

            sessionRegistrationGateway.findRegistrationsByLsId(ObjectId(lsId)).asFlow().collect { mb ->
                builder.putRegistrations(mb.id, LSessionRegistrationProto.newBuilder().fromPojo(mb))
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session on all by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session $lsId"
        } catch (t: Throwable) {
            logger.error("get session on all by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionDetailsById(request: GetSessionDetailByIdRequest): GetSessionDetailByIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val builder = GetSessionDetailByIdResponse.newBuilder()
            .setLsId(lsId)

        try {
            val lsDetail = sessionGateway.findSessionById(ObjectId(lsId)).await()
            builder.lsessionDetails = LSessionDetailsProto.newBuilder().fromPojo(lsDetail)
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session detail by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session detail for $lsId"
        } catch (t: Throwable) {
            logger.error("get session detail by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    private suspend fun getLatestLSession(): GetSessionDetailByIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = GetSessionDetailByIdResponse.newBuilder()

        try {
            val lsDetail = sessionGateway.getLatestLSession().await()
            builder.lsId = lsDetail.id
            builder.lsessionDetails = LSessionDetailsProto.newBuilder().fromPojo(lsDetail)
            code = Status.Code.OK
        } catch (t: Throwable) {
            logger.error("get latest session detail failed...", t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override fun getSessionDetailsByIds(requests: Flow<GetSessionDetailByIdRequest>): Flow<GetSessionDetailByIdResponse> {
        return requests.map {
            getSessionDetailsById(it)
        }
    }

    override suspend fun getSessionDetailsByUserId(request: GetSessionDetailByUserIdRequest): GetSessionDetailByUserIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val userId = request.userId
        val builder = GetSessionDetailByUserIdResponse.newBuilder()
            .setUserId(userId)

        try {
            val registrations = sessionRegistrationGateway.findRegistrationsByUserId(ObjectId(userId)).asFlow().toList()
            sessionGateway.findSessionByIds(registrations.map { ObjectId(it.id) }).asFlow().collect { lsDetail ->
                builder.putLsessionDetails(
                    lsDetail.id,
                    LSessionDetailsProto.newBuilder().fromPojo(lsDetail)
                )
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session detail by user id {} failed, not found", userId)
            code = Status.Code.NOT_FOUND
            message = "Not found session detail for user $userId"
        } catch (t: Throwable) {
            logger.error("get session detail by user id {} failed...", userId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionDetailsByOwnerId(request: GetSessionDetailByOwnerIdRequest): GetSessionDetailByOwnerIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val ownerId = request.lsOwnerId
        val builder = GetSessionDetailByOwnerIdResponse.newBuilder()
            .setLsOwnerId(ownerId)

        try {
            sessionGateway.findSessionByOwnerId(ObjectId(ownerId)).asFlow().collect { lsDetail ->
                builder.putLsessionDetails(
                    lsDetail.id,
                    LSessionDetailsProto.newBuilder().fromPojo(lsDetail)
                )
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session detail by ownerId {} failed, not found", ownerId)
            code = Status.Code.NOT_FOUND
            message = "Not found session detail for $ownerId"
        } catch (t: Throwable) {
            logger.error("get session detail by ownerId {} failed...", ownerId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getClassroomSettingsById(request: GetClassroomSettingByIdRequest): GetClassroomSettingByIdResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val builder = GetClassroomSettingByIdResponse.newBuilder()
            .setLsId(lsId)

        try {
            val clrSetting = classroomSettingGateway.findClassroomSettingById(ObjectId(lsId)).await()
            builder.settings = ClassroomSettingsProto.newBuilder().fromPojo(clrSetting)
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get classroom setting by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found classroom setting for $lsId"
        } catch (t: Throwable) {
            logger.error("get classroom setting by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionRegistrationByLsIdAndUserId(request: GetSessionRegistrationByLsIdAndUserIdRequest): GetSessionRegistrationResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val userId = request.userId
        val builder = GetSessionRegistrationResponse.newBuilder()

        try {
            val mb = sessionRegistrationGateway.findRegistrationsByUserIdAndLsId(ObjectId(lsId), ObjectId(userId)).await()
            builder.registration = LSessionRegistrationProto.newBuilder().fromPojo(mb)
            builder.setRegId(mb.id)
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session registration by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session registration for $lsId"
        } catch (t: Throwable) {
            logger.error("get session registration by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionRegistrationsByLsId(request: GetSessionRegistrationsByLsIdRequest): GetSessionRegistrationsResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val builder = GetSessionRegistrationsResponse.newBuilder()
            .setLsId(lsId)

        try {
            sessionRegistrationGateway.findRegistrationsByLsId(ObjectId(lsId)).asFlow().collect { mb ->
                builder.putRegistrations(mb.id, LSessionRegistrationProto.newBuilder().fromPojo(mb))
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session registration by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session registration for $lsId"
        } catch (t: Throwable) {
            logger.error("get session registration by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionRegistrationsByLsIdAndStatus(request: GetSessionRegistrationsByLsIdAndStatusRequest): GetSessionRegistrationsResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val builder = GetSessionRegistrationsResponse.newBuilder()
            .setLsId(lsId)
        val status = request.regStatusList.toList().map { LSRegStatus.valueOf(it) }

        try {
            sessionRegistrationGateway.findRegistrationsByLsIdWithRegStatus(ObjectId(lsId), status).asFlow().collect { mb ->
                builder.putRegistrations(
                    mb.id,
                    LSessionRegistrationProto.newBuilder().fromPojo(mb)
                )
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session registration by id {} with status {} failed, not found", lsId, status)
            code = Status.Code.NOT_FOUND
            message = "Not found session registration for $lsId"
        } catch (t: Throwable) {
            logger.error("get session registration by id {} with status {} failed...", lsId, status, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getAllLSessionSummaryByUserId(request: SearchLSessionSummaryByUserIdRequest): GetAllLSessionsSummaryResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = GetAllLSessionsSummaryResponse.newBuilder()

        return try {
            val lsRegistrationsSrc =
                if (ObjectId.isValid(request.userId))
                    sessionRegistrationGateway.findRegistrationsByUserId(
                        ObjectId(request.userId),
                        request.regStatusList.map { LSRegStatus.valueOf(it) }
                    ).toList()
                else Single.just(emptyList())

            val lsDetailsSrc = lsRegistrationsSrc.map { reg -> reg.map { ObjectId(it.lsId) } }.flatMap { lsId ->
                sessionGateway.findSessionByIds(
                    lsId,
                    request.lsStatusList.map { LSessionStatus.valueOf(it) }
                ).toList()
            }

            val clrSettingsSrc = lsDetailsSrc.map { ls -> ls.map { ObjectId(it.id) } }.flatMap {
                classroomSettingGateway.findClassroomSettingByIds(it).toList()
            }

            val lsessions = Single.zip(lsDetailsSrc, clrSettingsSrc, lsRegistrationsSrc) { lsDetails, clrSettings, lsRegistrations->
                val lsDetailsMap = lsDetails.associateBy { it.id }
                val clrSettingsMap = clrSettings.associateBy { it.id }
                val lsRegistrationsMap = lsRegistrations.associateBy { it.lsId }
                lsDetailsMap
                        .mapValues {
                            val lsDetailsProto = LSessionDetailsProto.newBuilder().fromPojo(it.value)
                            val clrSettingsProto = clrSettingsMap.get(it.key)?.let {
                                ClassroomSettingsProto.newBuilder().fromPojo(it)
                            }
                            val builder = GetAllLSessionsSummaryResponse.LSessionProto.newBuilder()
                                    .setLsId(it.key)
                                    .setDetails(lsDetailsProto)

                            if (clrSettingsProto != null) builder.setSettings(clrSettingsProto)

                            if (lsRegistrationsMap.containsKey(it.key)) {
                                val lsRegistration = lsRegistrationsMap[it.key]!!
                                val lsRegistrationProto = LSessionRegistrationProto.newBuilder().fromPojo(lsRegistration)
                                builder.registration = lsRegistrationProto
                            }

                            builder.build()
                        }.values
            }.await()

            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

            responseBuilder.addAllLsessions(lsessions)
                    .setStatus(statusBuilder)
                    .build()
        } catch (t: Throwable) {
            logger.error("failed to get all lsessions: ", t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to get all lsessions"
            responseBuilder.setStatus(statusBuilder).build()
        }
    }

    override suspend fun updateSessionOnAll(request: UpdateSessionOnAllRequest): UpdateSessionOnAllResponse {
        var code = Status.Code.INTERNAL
        var message = ""

        val lsId = request.lsId
        val lsDetailsProto = request.lsDetails
        val registrations = request.registrationsMap.entries.asFlow()
        val settingsProto = request.classroomSettings

        val builder = UpdateSessionOnAllResponse.newBuilder()
            .setLsId(lsId)

        try {
            val details = lsDetailsProto.toBsonUpdater()
            val settings = settingsProto.toBsonUpdater()
            sessionGateway.updateSessionById(ObjectId(lsId), details).await()
            classroomSettingGateway.updateClassroomSettingById(ObjectId(lsId), settings).await()

            registrations.collect {
                val registrationProto = it.value
                val newStatus = LSRegStatus.valueOf(registrationProto.regStatus)
                val registration = sessionRegistrationGateway.updateStatusById(ObjectId(registrationProto.regId), newStatus).await()
                if (registration.regStatus != newStatus && (newStatus == LSRegStatus.REGISTERED || registration.regStatus == LSRegStatus.REGISTERED)) {
                    val delta = if (newStatus == LSRegStatus.REGISTERED) 1 else -1
                    sessionGateway.updateRegistered(ObjectId(registration.lsId), delta).await()
                }
            }

            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("update session on all by lsId {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session on all by lsId $lsId"
        } catch (t: Throwable) {
            logger.error("update session on all by {} lsId failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun updateSessionDetails(request: UpdateSessionDetailsRequest): UpdateSessionDetailsResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val lsDetailProto = request.lsDetails
        val builder = UpdateSessionDetailsResponse.newBuilder()
            .setLsId(lsId)

        try {
            val details = lsDetailProto.toBsonUpdater()
            sessionGateway.updateSessionById(ObjectId(lsId), details).await()
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("update session details by lsId {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found session details by lsId $lsId"
        } catch (t: Throwable) {
            logger.error("update session details by lsId {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun updateSessionRegistrations(request: UpdateSessionRegistrationsRequest): UpdateSessionRegistrationsResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val registrations = request.registrationsMap.entries.asFlow()
        val builder = UpdateSessionRegistrationsResponse.newBuilder()
            .setLsId(lsId)

        try {
            registrations.collect {
                val registrationProto = it.value
                val newStatus = LSRegStatus.valueOf(registrationProto.regStatus)
                val registration = sessionRegistrationGateway.updateStatusById(ObjectId(registrationProto.regId), newStatus).await()
                if (registration.regStatus != newStatus && (newStatus == LSRegStatus.REGISTERED || registration.regStatus == LSRegStatus.REGISTERED)) {
                    val delta = if (newStatus == LSRegStatus.REGISTERED) 1 else -1
                    sessionGateway.updateRegistered(ObjectId(registration.lsId), delta).await()
                }
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("update lsession registration by lsId {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found lsession registration by lsId $lsId"
        } catch (t: Throwable) {
            logger.error("update lsession registration by lsId {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun updateClassroomSettings(request: UpdateClassroomSettingRequest): UpdateClassroomSettingResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val lsId = request.lsId
        val settingsProto = request.classroomSettings
        val builder = UpdateClassroomSettingResponse.newBuilder()
            .setLsId(lsId)

        try {
            val settings = settingsProto.toBsonUpdater()
            classroomSettingGateway.updateClassroomSettingById(ObjectId(lsId), settings).await()
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("update classroom setting by id {} failed, not found", lsId)
            code = Status.Code.NOT_FOUND
            message = "Not found classroom setting for $lsId"
        } catch (t: Throwable) {
            logger.error("update classroom setting by id {} failed...", lsId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun createSessionRegistration(request: CreateSessionRegistrationRequest): CreateSessionRegistrationResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = CreateSessionRegistrationResponse.newBuilder()

        try {
            val lsRegistration = LSessionRegistration(
                lsId = request.lsId,
                lsOwnerUserId = request.lsOwnerId,
                userId = request.userId,
                regStatus = LSRegStatus.valueOf(request.regStatus),
                regTime = request.regTime,
                userState = ClassroomUserState(
                    ShareScreenStatus.NONE,
                    if (request.lsOwnerId != request.userId) RaiseHandStatus.NONE else RaiseHandStatus.PRESENTING,
                    UserAvailableStatus.OFFLINE
                )
            )
            val ins = sessionRegistrationGateway.insertOne(lsRegistration).await()
            builder.regId = ins.insertedId!!.asObjectId().value.toHexString()
            code = Status.Code.OK
        } catch (t: DuplicateKeyException) {
            logger.error("save session registration {} failed...", request, t)
            message = t.message ?: ""
            code = Status.Code.ALREADY_EXISTS
        } catch (t: Throwable) {
            logger.error("save session registration {} failed...", request, t)
            message = t.message ?: ""
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun getSessionRegistrationById(request: GetSessionRegistrationByIdRequest): GetSessionRegistrationResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val regId = request.regId
        val builder = GetSessionRegistrationResponse.newBuilder()
            .setRegId(regId)

        try {
            val lsMem = sessionRegistrationGateway.findRegistrationById(ObjectId(regId)).await()
            builder.registration = LSessionRegistrationProto.newBuilder().fromPojo(lsMem)
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("get session registration by id {} failed, not found", regId)
            code = Status.Code.NOT_FOUND
            message = "Not found session registration for $regId"
        } catch (t: Throwable) {
            logger.error("get session registration by id {} failed...", regId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Status.Code.NOT_FOUND
        }
        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override fun getSessionRegistrationsByIds(requests: Flow<GetSessionRegistrationByIdRequest>): Flow<GetSessionRegistrationResponse> {
        return requests.map {
            getSessionRegistrationById(it)
        }
    }

    override suspend fun updateRegistrationStatus(request: UpdateRegistrationStatusRequest): UpdateRegistrationStatusResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val regId = request.regId
        val builder = UpdateRegistrationStatusResponse.newBuilder()
            .setRegId(regId)

        try {
            val newStatus = LSRegStatus.valueOf(request.newStatus)
            val regTime: Long? = if (newStatus == LSRegStatus.WAITING_CONFIRMED) request.regTime else null
            val registration = sessionRegistrationGateway.updateStatusById(ObjectId(regId), newStatus, regTime).await()
            if (registration.regStatus != newStatus && (newStatus == LSRegStatus.REGISTERED || registration.regStatus == LSRegStatus.REGISTERED)) {
                val delta = if (newStatus == LSRegStatus.REGISTERED) 1 else -1
                sessionGateway.updateRegistered(ObjectId(registration.lsId), delta).await()
            }
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error(
                "update session registration by id {} to {} failed, not found",
                regId, request.newStatus
            )
            code = Status.Code.NOT_FOUND
            message = "Not found session registration for $regId"
        } catch (t: Throwable) {
            logger.error(
                "update session registration by id {} from {} failed...",
                regId, request.newStatus, t
            )
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code =
                Status.Code.INVALID_ARGUMENT
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun updateSessionAvatar(request: UpdateSessionAvatarRequest): UpdateSessionAvatarResponse {

        var code = Status.Code.INTERNAL
        val builder = UpdateSessionAvatarResponse.newBuilder()

        try {
            val ins = sessionGateway.updateImgUrl(ObjectId(request.lsId), request.imgUrl).await()
            code = Status.Code.OK
        } catch (e: NoSuchElementException) {
            logger.error("Update session avatar failed. lsId {} not found", request.lsId)
            code = Status.Code.NOT_FOUND
        } catch (t: Throwable) {
            logger.error("Unexpected error occurs. Unable to update image url for id {}", request.lsId, t)
            val message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code =
                Status.Code.INVALID_ARGUMENT
        }
        builder.status = com.google.rpc.Status.newBuilder().setCode(code.value()).build()
        return builder.build()
    }

    override suspend fun updateRaiseHandStatus(request: UpdateRaiseHandStatusRequest): UpdateRaiseHandStatusResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateRaiseHandStatusResponse.newBuilder()

        val regId = request.regId
        val status = request.status

        try {
            logger.debug("[{}] processing update classroom registration status request", regId)
            sessionRegistrationGateway.updateRaiseHandStatus(regId, RaiseHandStatus.valueOf(status)).await()
            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

        } catch (t: Throwable) {
            logger.error("failed to update classroom registration status for {} ... ", regId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to update classroom registration status"
            responseBuilder.setStatus(statusBuilder).build()
        }
        return responseBuilder.setStatus(statusBuilder).build()
    }

    override suspend fun updateShareScreenStatus(request: UpdateShareScreenStatusRequest): UpdateShareScreenStatusResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateShareScreenStatusResponse.newBuilder()

        val regId = request.regId
        val status = request.status

        try {
            logger.debug("[{}] processing update classroom registration status request", regId)
            sessionRegistrationGateway.updateShareScreenStatus(regId, ShareScreenStatus.valueOf(status)).await()
            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

        } catch (t: Throwable) {
            logger.error("failed to update classroom registration status for {} ... ", regId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to update classroom registration status"
            responseBuilder.setStatus(statusBuilder).build()
        }
        return responseBuilder.setStatus(statusBuilder).build()
    }

    override suspend fun updateUserAvailableStatus(request: UpdateUserAvailableStatusRequest): UpdateUserAvailableStatusResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateUserAvailableStatusResponse.newBuilder()

        val regId = request.regId
        val status = request.status

        try {
            logger.debug("[{}] processing update classroom registration user status request", regId)
            sessionRegistrationGateway.updateClrRegistrationUserStatus(regId, UserAvailableStatus.valueOf(status)).await()
            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

        } catch (t: Throwable) {
            logger.error("failed to update classroom registration user status for {} ... ", regId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to update classroom user registration status"
            responseBuilder.setStatus(statusBuilder).build()
        }
        return responseBuilder.setStatus(statusBuilder).build()
    }

    override suspend fun updateRequestPinTab(request: UpdateRequestPinTabRequest): UpdateRequestPinTabResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateRequestPinTabResponse.newBuilder()

        val regId = request.regId
        val tabId = request.tabId
        val status = if (request.hasStatus()) RequestPinTabStatus.valueOf(request.status) else null
        val tabName = if (request.hasTabName()) request.tabName else null

        try {
            if (status == RequestPinTabStatus.PENDING) {
                logger.debug("[{}] processing insert request pin tab request", regId)
                sessionRegistrationGateway.updatePinTabStatus(regId, tabId, RequestPinTabStatus.PENDING, tabName)
            } else {
                logger.debug("[{}] processing update request pin tab request", regId)
                sessionRegistrationGateway.updatePinTabStatus(regId, tabId, status, tabName)
            }
            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

        } catch (t: Throwable) {
            logger.error("failed to update request pin tab status for {} ... ", regId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to update request pin tab status"
            responseBuilder.setStatus(statusBuilder).build()
        }
        return responseBuilder.setStatus(statusBuilder).build()
    }

    override suspend fun updateSessionState(request: UpdateLSessionStateRequest): UpdateLSessionStateResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = UpdateLSessionStateResponse.newBuilder()
        responseBuilder.lsId = request.lsId

        return try {
            val bson = request.state.toBsonUpdater()
            val latest = sessionGateway.findAndUpdateByLsId(ObjectId(request.lsId), bson).await()

            val latestState = latest.state?.toProto()

            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

            responseBuilder.setStatus(statusBuilder).setState(latestState).build()
        } catch (t: Throwable) {
            logger.error("failed to update lsession state: ", t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to update lsession state"
            responseBuilder.setStatus(statusBuilder).build()
        }
    }

    override suspend fun cancelAllRaisingHand(request: CancelAllRaisingHandRequest): CancelAllRaisingHandResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = CancelAllRaisingHandResponse.newBuilder()
        val lsId: String = request.lsId
        try {
            logger.debug("[{}] processing cancel all raising hand request", request.lsId)
            sessionRegistrationGateway.cancelAllRaisingHand(lsId, RaiseHandStatus.NONE).await()
            statusBuilder.code = Status.Code.OK.value()
            statusBuilder.message = "successful"

        } catch (t: Throwable) {
            logger.error("failed to cancel raising hand for session {} ... ", lsId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to canceling raising hand"
            responseBuilder.setStatus(statusBuilder).build()
        }
        return responseBuilder.setStatus(statusBuilder).build()
    }
}
