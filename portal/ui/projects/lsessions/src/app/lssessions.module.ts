import { CommonModule, DocumentServiceModule, MemberServiceModule, UserService, UserServiceModule, ConfigServiceModule,
  USER_PROFILE, ENVIRONMENT } from '@viclass/portal.common';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { LSessionsRoutingModule } from './lsessions-routing.module';
import { LSessionsComponent } from './lsessions.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SearchbarComponent } from './searchbar/searchbar.component';
import { RemoveableTagComponent } from './removeable-tag/removeable-tag.component';
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { FullscreenOverlayContainer, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { SessionCreatePageComponent } from './session-create-page/session-create-page.component';
import { LsessionDetailsComponent } from './lsession-details/lsession-details.component';
import { SessionListComponent } from './session-list/session-list.component';
import { ReactiveFormsModule } from '@angular/forms';
import { WhiteboardGridOptionsComponent } from './whiteboard-grid-options/whiteboard-grid-options.component';
import { SessionEditPageComponent } from './session-edit-page/session-edit-page.component';
import { LsessionDetailsContentComponent } from './lsession-details-content/lsession-details-content.component';
import { GeneralTabComponent } from './lsession-details-content/general-tab/general-tab.component';
import { DescriptionTabComponent } from './lsession-details-content/description-tab/description-tab.component';
import { LSessionMemberListComponent } from './lsession-member-list/lsession-member-list.component';
import { DocumentTabComponent } from './lsession-details-content/document-tab/document-tab.component';
import { SettingsTabComponent } from './lsession-details-content/settings-tab/settings-tab.component';
import { PageComponent } from './page/page.component';

import { environment } from '../environments/environment';
import { SessionFormComponent } from './session-form/session-form.component';
import { LSessionMemberTagComponent } from './lsession-member-list/lsession-member-tag/lsession-member-tag.component';
import {MatButtonModule} from "@angular/material/button";
import {MatMenuModule} from "@angular/material/menu";
import {MatSelectModule} from "@angular/material/select";

@NgModule({
  declarations: [
    LSessionsComponent,
    SearchbarComponent,
    RemoveableTagComponent,
    SessionCreatePageComponent,
    LsessionDetailsComponent,
    WhiteboardGridOptionsComponent,
    SessionListComponent,
    SessionEditPageComponent,
    LsessionDetailsContentComponent,
    GeneralTabComponent,
    DescriptionTabComponent,
    LSessionMemberListComponent,
    DocumentTabComponent,
    SettingsTabComponent,
    PageComponent,
    SessionFormComponent,
    LSessionMemberTagComponent
  ],
  imports: [
    BrowserModule,
    CommonModule,
    LSessionsRoutingModule,
    ReactiveFormsModule,

    // MATERIAL
    OverlayModule,

    // Server api
    UserServiceModule,
    ConfigServiceModule,
    DocumentServiceModule,
    MemberServiceModule,
    MatButtonModule,
    MatMenuModule,
    MatSelectModule

  ],
  providers: [
    {provide: MAT_DATE_LOCALE, useValue: 'vi-VN'},
    {
      provide: MAT_DATE_FORMATS,
      useValue: {
        parse: {
          dateInput: ['l', 'LL'],
        },
        display: {
          dateInput: 'L',
          monthYearLabel: 'MMMM YYYY',
          dateA11yLabel: 'LL',
          monthYearA11yLabel: 'MMMM YYYY',
        },
      }
    },
    { provide: OverlayContainer, useClass: FullscreenOverlayContainer },
    { provide: USER_PROFILE, useFactory: (service : UserService) => service.curUserProfile, deps: [UserService] },
    { provide: ENVIRONMENT, useValue: environment }

  ],
  bootstrap: [LSessionsComponent]
})
export class LSessionsModule { }
