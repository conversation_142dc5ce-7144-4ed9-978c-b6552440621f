<div class="d-flex align-items-top whiteboard-options">
    <i class="vcon-general vcon_wb_bg_white" (click)="setSelection('WHITE')" [ngClass]="{ 'active': selected == 'WHITE' }"></i>
    <i class="vcon-general vcon_wb_bg_black ms-3" (click)="setSelection('BLACK')" [ngClass]="{ 'active': selected == 'BLACK' }"></i>
    <i class="vcon-general vcon_wb_bg_white-grid ms-3" (click)="setSelection('WHITE_GRID')" [ngClass]="{ 'active': selected == 'WHITE_GRID' }"></i>
    <i class="vcon-general vcon_wb_bg_black-grid ms-3" (click)="setSelection('BLACK_GRID')" [ngClass]="{ 'active': selected == 'BLACK_GRID' }"></i>
</div>
