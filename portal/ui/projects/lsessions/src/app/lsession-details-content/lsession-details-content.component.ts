import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import * as _ from "lodash"
import {MatDialog} from "@angular/material/dialog";
import {
  Document, LSessionDetails, LSessionMemberViewModel, UserProfile, LSessionSettings, ConfigService, DialogShareComponent,
  MemberRegistrationAction
} from '@viclass/portal.common';

@Component({
  selector: 'ls-lsession-details-content',
  templateUrl: './lsession-details-content.component.html',
  styleUrls: ['./lsession-details-content.component.sass']
})
export class LsessionDetailsContentComponent implements OnInit {

  @Input() lsDetails: LSessionDetails
  @Input() settings: LSessionSettings
  @Input() documents: Document[] = []
  @Input() members: LSessionMemberViewModel[] = []
  @Input() creator: UserProfile
  @Input() isPreview = false
  @Input() loggedInUser: UserProfile
  @Input() curMember: LSessionMemberViewModel

  @Output()
  regChange = new EventEmitter<MemberRegistrationAction>()

  selectedTab: "general-tab" | "description-tab" | "document-tab" | "member-tab" | "settings-tab" = "general-tab"

  subjects: any;
  grades : any;

  constructor(
    private configService : ConfigService,
    private matDialog: MatDialog,
  ) {
    this.subjects = _.reduce(this.configService.subjects(), (sum : any, item : any) => { sum[item._id] = item.value; return sum; }, {})
    this.grades = _.reduce(this.configService.grades(), (sum : any, item : any) => { sum[item._id] = item.value; return sum; }, {})

  }

  ngOnInit(): void {

  }

  sendRegister() {
    if (this.curMember && this.curMember.id && this.curMember.regStatus)
      this.sendReRegister()
    else this.regChange.emit("register")
  }

  sendReRegister() {
    this.regChange.emit("re-register")
  }

  sendUnregister() {
    this.regChange.emit("unregister")
  }

  share() {
    this.matDialog.open(DialogShareComponent, {
      data: {
        content: `/lsessions/lsession-details/${this.lsDetails.id}`
      }
    })
  }

  isCreator(): boolean {
    return this.loggedInUser && this.creator && this.loggedInUser.id == this.creator.id
  }

  isSentRegister(): boolean {
    if (!this.loggedInUser || !this.curMember) return false
    return this.curMember?.regStatus == "REGISTERED" || this.curMember?.regStatus == "WAITING_CONFIRMED"
  }

  isRegistered(): boolean {
    if (!this.loggedInUser || !this.curMember) return false
    return this.curMember?.regStatus == "REGISTERED"
  }

  isRejected(): boolean {
    if (!this.loggedInUser || !this.curMember) return false
    return this.curMember?.regStatus == "REJECTED"
  }

  isWaitingConfirm(): boolean {
    if (!this.loggedInUser || !this.curMember) return false
    return this.curMember?.regStatus == "WAITING_CONFIRMED"
  }

  isLoggedIn(): boolean {
    return this.loggedInUser && true;
  }

  isFullSlot() {
    if (!this.settings || this.settings.maxMember < 0) return false

    return this.lsDetails && this.lsDetails.state && this.lsDetails.state.registered >= this.settings.maxMember
  }

  isEnd(): boolean {
    return this.lsDetails.state.status == "ENDED"
  }


}
