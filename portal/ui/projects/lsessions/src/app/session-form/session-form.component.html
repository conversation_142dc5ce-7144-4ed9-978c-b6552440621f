<ng-template fflow let-fum [fflowBy]="buildForm" [fflowSubmit]="submitSession" [fflowNoNav]="true" #formFlow="fflow">
    <form class="content session-form" [formGroup]="fum" [(ferror)]="formError" [ferrcoord]="formFlow" [ngClass]="previewMode ? 'd-none' : 'd-block'">
        <button class="invisible" [form-flow-submit]="formFlow" #submitButton></button>
        <div class="row" formGroupName="details">
            <div class="col-xl-6 col" >
                <div class="sectionbox">
                    <span class="sectionbox-title">Thông tin buổi học <span class="text-o1">*</span></span>
                    <div class="sectionbox-content">
                        <div class="row">
                            <div class="col">
                                <input class="form-control form-control-sm" formControlName="title" [(ferror)]="titleError" placeholder="Nhập tiêu đề buổi học"/>
                                <span class="text-error ms-15px" *ngIf="titleError" >! {{titleError.msg}}</span>
                            </div>
                        </div>
                        <div class="row mt-15px">
                            <div class="col-5 d-flex">
                                <span class="me-2" style="white-space: nowrap">Ảnh đại diện</span>
                                <label><input type="file" class="d-none" formControlName="imgUrl" accept=".jpg,.jpeg,.png,.bmp" [(ferror)]="imgUrlError" (change)="setImgUrl($event)" #sessionAvatar/><a class="vcon-link"><i class="vcon-general vcon_general_upload"></i></a></label>
                                <span class="ms-1 active filename-display" *ngIf="sessionAvatar.files.length > 0 || initialAvatarUrl">{{avatarFileName()}}</span>
                                <span class="text-error ms-15px" *ngIf="imgUrlError" >! {{imgUrlError.msg}}</span>
                            </div>
                            <div class="col-7 d-flex">
                                <div>
                                  <mat-select [(value)]="khoi_lop" class="form-select" formControlName="grade" [(ferror)]="gradeError">
                                    <mat-option disabled value="" hidden>Khối lớp</mat-option>
                                    <mat-option *ngFor="let g of grades()" [value]="g._id">{{g.value}}</mat-option>
                                  </mat-select>
                                  <span class="text-error ms-15px" *ngIf="gradeError" >! {{gradeError.msg}}</span>
                                </div>
                                <div class="ms-10px">
                                  <mat-select [(value)]="mon_hoc" class="form-select" formControlName="subject" [(ferror)]="subjectError">
                                    <mat-option disabled value="" hidden>Môn học</mat-option>
                                    <mat-option *ngFor="let s of subjects()" [value]="s._id">{{s.value}}</mat-option>
                                  </mat-select>
                                  <span class="text-error ms-15px" *ngIf="subjectError" >! {{subjectError.msg}}</span>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-15px">
                            <div class="col-5 d-flex">
                                <common-date-time-picker (dateTimePicked)="dateTimePicked($event)" [initial]="initialPickedTime()"></common-date-time-picker>
                                <input type="hidden" formControlName="startTime" />
                                <input type="hidden" formControlName="startDate" />
                            </div>
                            <div class="col-7">
                                <div class="d-flex">
                                    <span class="me-2">Dự kiến</span>
                                    <input type="text" value="0" class="form-control form-control text-center" style="width:60px" formControlName="expectedDuration" [(ferror)]="expectedDurationError"/>
                                    <span class="ms-2">phút</span>
                                </div>
                                <span class="text-error ms-15px" *ngIf="expectedDurationError" >! {{expectedDurationError.msg}}</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-xl-6 col">
                <div class="sectionbox">
                    <span class="sectionbox-title">Mô tả <span class="text-o1">*</span></span>
                    <div class="sectionbox-content">
                        <textarea class="h-100 form-control form-control-sm" placeholder="Mô tả nội dung - mục tiêu lớp học" formControlName="description" [(ferror)]="descriptionError"></textarea>
                        <span class="text-error ms-15px" *ngIf="descriptionError" >! {{descriptionError.msg}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-30px" formGroupName="settings">
            <div class="col-xl-6 col">
                <div class="sectionbox">
                    <span class="sectionbox-title">Cài đặt</span>
                    <div class="sectionbox-content" >
                        <div class="row">
                            <div class="col-xl-5 col-6">
                                <div class="d-flex align-items-top session-settings">
                                    <span class="me-auto">Camera</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" formControlName="camera">
                                    </div>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Audio</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" formControlName="audio">
                                    </div>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Hộp thoại chát</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" formControlName="chatBox">
                                    </div>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Tự động ghi lớp</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" formControlName="autoRecord">
                                    </div>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Tùy chọn bảng trắng</span>
                                </div>
                                <div class="mt-15px">
                                    <input type="hidden" formControlName="boardType" #boardType />
                                    <ls-whiteboard-grid-options (onSelect)="selectBoardType($event)" [selected]="boardType.value"></ls-whiteboard-grid-options>
                                </div>
                            </div>
                            <div class="col-xl-6 offset-xl-1 col-6">
                                <div class="d-flex align-items-top session-settings">
                                    <span class="me-auto">Giới hạn số lượng học viên</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" #isStudentLimitInput [checked]="isStudentLimit" (change)="switchStudentLimit($event)" />
                                    </div>
                                </div>
                                <div *ngIf="isStudentLimitInput.checked" class="mt-15px d-flex align-items-center">
                                    <span class="me-2">Tối đa</span>
                                    <input class="form-control form-control-sm text-center" value="25" style="width:57px" formControlName="maxMember" />
                                    <span class="ms-2">học viên</span>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Lọc từ xấu / thô tục / phân biệt chủng tộc</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" type="checkbox" formControlName="chattingFilter">
                                    </div>
                                </div>
                                <div class="mt-15px d-flex align-items-top session-settings">
                                    <span class="me-auto">Học viên được tham gia lớp học bất cứ lúc nào</span>
                                    <div class="form-check form-switch ms-1">
                                        <input class="form-check-input" checked type="checkbox" #isNoEntryTimeLimitInput [checked]="isNoEntryTimeLimit" (change)="switchNoEntryTimeLimit($event)"/>
                                    </div>
                                </div>
                                <div *ngIf="!isNoEntryTimeLimitInput.checked" class="mt-15px d-flex align-items-center">
                                    <span class="me-2">Sau <input class="form-control form-control-sm d-inline-block text-center" formControlName="rejectLateMemberAfterMinute" value="15" style="width:57px"/> phút, học viên vào trễ sẽ không được tham gia</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col">
                <div class="sectionbox">
                    <span class="sectionbox-title">Cài đặt thông báo</span>
                    <div class="sectionbox-content">
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-30px" *ngIf="isEditing">
            <div class="col">
                <div class="sectionbox">
                    <span class="sectionbox-title">Học viên</span>
                    <div class="sectionbox-content">
                        <div class="border" style="border-radius: 5px; border-top-width: 1px;">
                            <ls-member-list [members]="sessionMembers" [isEditing]="isEditing" (approve)="approveMember.emit($event)" (reject)="rejectMember.emit($event)"></ls-member-list>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-30px">
            <div class="col">
                <div class="sectionbox">
                    <span class="sectionbox-title">Tài liệu</span>
                </div>
            </div>
        </div>
    </form>

</ng-template>

<div class="preview-lsession container" [ngClass]="previewMode ? 'd-block' : 'd-none'" *ngIf="previewMode">
  <ls-lsession-details-content
    [isPreview]="previewMode"
    [lsDetails]="lsDetails"
    [settings]="lsSettings"
    [creator]="getLoggedInUser()"
    [loggedInUser]="getLoggedInUser()">
  </ls-lsession-details-content>
</div>
