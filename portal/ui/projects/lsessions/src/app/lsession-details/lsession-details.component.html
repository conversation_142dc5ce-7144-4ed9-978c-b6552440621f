<div class="actionbar" *ngIf="lsDetails">
  <div class="col">
      <div class="content d-flex align-items-center px-xl-0 px-md-5">
          <div class="me-auto ls-details-breadcrumb">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{environment.homePage}}">ViClass</a></li>
                <li class="breadcrumb-item"><a href="/lsessions">Buổi học</a></li>
                <li class="breadcrumb-item"><a href="/lsessions">{{subjects[lsDetails.subject]}}</a></li>
                <li class="breadcrumb-item"><a href="/lsessions">{{grades[lsDetails.grade]}}</a></li>
                <li class="breadcrumb-item" aria-current="page">{{lsDetails.title}}</li>
              </ol>
            </nav>
          </div>
      </div>
  </div>
</div>
<div class="ls-details-container container" *ngIf="lsDetails">
  <ls-lsession-details-content
    [lsDetails]="lsDetails"
    [settings]="lsSettings"
    [creator]="creator"
    [members]="members"
    [documents]="documents"
    [loggedInUser]="loggedInUser"
    [curMember]="curMember"
    (regChange)="sendRegistrationThisSession($event)"
  ></ls-lsession-details-content>
</div>
<div class="lsessions-summary px-xxl-70px px-xl-40px px-lg-40px" *ngIf="summaries && summaries.length > 0">
  <div class="center-x max-w-lg-870px max-w-xl-1170px" >
    <div>
      <div class="title">
        <span *ngIf="isCreator()">Buổi học của bạn</span>
        <span *ngIf="!isCreator()">Có thể bạn quan tâm</span>
      </div>
      <div class="sessions" >
        <div *ngFor="let s of summaries; let i = index" class="session-item">
          <div *ngIf="i<4">
            <common-lsession-summary
              [userProfile]="loggedInUser"
              [creator]="s.creator"
              [lsession]="s.session"
              (regChange)="sendRegistrationLSession($event, s.session)"
            ></common-lsession-summary>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
