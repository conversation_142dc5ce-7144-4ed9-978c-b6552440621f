plugins {
    id "hnct.build"
    id "kotlin"
    id "com.google.devtools.ksp"
}

dependencies {

    internal {
        implementation([id: ':portal.grpc', src: ['auth']], "viclass:portal.grpc-auth:1.0.0", true)
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
    }

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"
    implementation "io.grpc:grpc-netty:$grpcVs"

    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:2.12.2"

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    // implementation "org.koin:koin-java:2.0.1"
}

group = 'viclass'
version = '1.0.0'
