package portal.user.server

import common.libs.logger.Logging
import io.grpc.Status
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import portal.user.dbgateway.UserProfileGateway
import portal.user.dbgateway.UserRegistrationGateway
import portal.user.pojo.*
import portal.user.utils.isRegVerified
import proto.portal.user.UserMessage
import proto.portal.user.UserMessage.TokenInfo

@Singleton
class SocialRegistrationService(
    private val userRegistrationGateway: UserRegistrationGateway,
    private val userProfileGateway: UserProfileGateway,
    private val socialVerifierService: SocialVerifierService,
) : Logging {

    suspend fun addMissingSocialEmail(request: UserMessage.AddMissingSocialEmailRequest): UserMessage.AddMissingSocialEmailResponse {
        val builder = UserMessage.AddMissingSocialEmailResponse.newBuilder()
        if (request.registrationId.isEmpty() || request.email.isEmpty()) {
            return builder.buildStatus("Missing registrationId or email", Status.Code.INVALID_ARGUMENT)
        }

        val registration: UserRegistrationPojo
        try {
            registration = userRegistrationGateway.findRegistrationById(request.registrationId).await()
        } catch (t: Throwable) {
            return builder.buildStatus("Registration not found", Status.Code.NOT_FOUND)
        }

        if (registration.regType == RegistrationType.EMAIL || registration.regType == RegistrationType.OTHER) {
            return builder.buildStatus("Registration type unsupported")
        }

        if (registration.email.isNotEmpty()) {
            return builder.buildStatus("Social email already filled")
        }

        try {
            this.userRegistrationGateway.setEmailForSocialRegistration(registration.id, request.email, false)
                ?: throw RuntimeException("The updated registration returned null")
            return builder.buildStatus("Add missing email succeed", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("Add missing email for social registration {} failed...", registration.id, t)
            return builder.buildStatus("Add missing email failed")
        }
    }

    /**
     * Get type of all verified registrations linked with the profile email
     */
    suspend fun getLinkedRegistrations(request: UserMessage.GetLinkedRegistrationsRequest): UserMessage.GetLinkedRegistrationsResponse {
        var code: Status.Code
        var message = ""
        val builder = UserMessage.GetLinkedRegistrationsResponse.newBuilder()

        try {
            val registrations = userRegistrationGateway.getLinkedRegistrations(request.profileEmail).await()
            val verifiedRegNames: List<String> = registrations.filter { it.isRegVerified() }.map { it.regType.name }

            code = Status.Code.OK
            builder.addAllRegistrationName(verifiedRegNames)
        } catch (t: Throwable) {
            code = Status.Code.NOT_FOUND
            message = t.message ?: "get linked registration error"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    suspend fun verifySocialToken(request: UserMessage.VerifySocialTokenRequest): UserMessage.VerifySocialTokenResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = UserMessage.VerifySocialTokenResponse.newBuilder();
        try {
            val tokenInfo: SocialLoginInfo = this.socialVerifierService.verifyUser(request.provider, request.authToken)
            val registration = userRegistrationGateway.getSocialRegistration(tokenInfo.socialId, tokenInfo.provider)

            builder
                .setRegistrationId(registration?.id ?: "")
                .setRegistrationEmail(registration?.email ?: "")
                .setVerified(registration?.isRegVerified() ?: false)
                .setTokenInfo(
                    UserMessage.TokenInfo.newBuilder()
                        .setProvider(tokenInfo.provider)
                        .setSocialId(tokenInfo.socialId)
                        .setEmail(tokenInfo.email ?: "")
                        .setEmailVerified(tokenInfo.emailVerified)
                        .setUsername(tokenInfo.username)
                        .setAvatarUrl(tokenInfo.avatarUrl)
                        .setFirstName(tokenInfo.firstName)
                        .setLastName(tokenInfo.lastName)
                )

            code = Status.Code.OK
        } catch (t: Throwable) {
            logger.error("verify social token error...", t)
            message = t.message ?: "verify social token error"
        }

        return builder.setStatus(
            com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)
        )
            .build()
    }

    suspend fun addSocialRegistration(request: UserMessage.AddSocialRegistrationRequest): UserMessage.AddSocialRegistrationResponse {
        val builder = UserMessage.AddSocialRegistrationResponse.newBuilder()

        val tokenInfo = request.tokenInfo
        val userReg: UserRegistrationPojo = when (RegistrationType.from(tokenInfo.provider)) {
            RegistrationType.GOOGLE -> GoogleRegistrationPojo(
                username = tokenInfo.username,
                email = tokenInfo.email ?: "",
                socialId = tokenInfo.socialId,
                verified = tokenInfo.emailVerified
            )

            RegistrationType.FACEBOOK -> FacebookRegistrationPojo(
                username = tokenInfo.username,
                email = tokenInfo.email ?: "",
                socialId = tokenInfo.socialId,
                verified = tokenInfo.emailVerified
            )

            else -> {
                return builder.buildStatus("Unknown social registration provider", Status.Code.INVALID_ARGUMENT)
            }
        }

        // insert new registration
        try {
            val insUser = userRegistrationGateway.insertOne(userReg).await()
            val registrationId = insUser.insertedId?.asObjectId()?.value?.toHexString()
                ?: return builder.buildStatus("save social registration failed")

            return builder.setRegistrationId(registrationId)
                .buildStatus("Add social registration succeed", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("save registration {} failed...", userReg, t)
            return builder.buildStatus(t.message ?: "save social registration failed")
        }
    }

    suspend fun unlinkRelinkSocialRegistration(request: UserMessage.UnlinkRelinkSocialRegistrationRequest): UserMessage.UnlinkRelinkSocialRegistrationResponse {
        val builder = UserMessage.UnlinkRelinkSocialRegistrationResponse.newBuilder()
        val regId: String = request.registrationId
        val tokenInfo: TokenInfo = request.tokenInfo

        try {
            val registration = userRegistrationGateway.setEmailForSocialRegistration(regId, tokenInfo.email, true)
                ?: throw RuntimeException("The updated registration returned null")

            val profile = userProfileGateway.findUserProfileByEmail(registration.email)
            if (profile != null) {
                return builder.setProfileId(profile.id).buildStatus("Relink account success", Status.Code.OK)
            }

            val userProfileReg = UserProfilePojo(
                username = tokenInfo.username,
                email = tokenInfo.email,
                phone = registration.phone,
                avatarUrl = tokenInfo.avatarUrl,
            )

            val result = userProfileGateway.insertOne(userProfileReg).await()
            val profileId = result.insertedId?.asObjectId()?.value?.toHexString()
                ?: throw RuntimeException("can not insert new profile")

            return builder.setProfileId(profileId)
                .buildStatus("Unlink account success. New profile created", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("Unlink/Relink for registration {} failed...", regId, t)
            return builder.buildStatus("Unlink/Relink failed")
        }
    }

    suspend fun unlinkSocial(request: UserMessage.UnlinkSocialRequest): UserMessage.UnlinkSocialResponse {
        val social = RegistrationType.fromSocial(request.social.toString()) ?: throw RuntimeException("Wrong social")
        val response = UserMessage.UnlinkSocialResponse.newBuilder()

        try {
            val linkedRegistrations =
                userRegistrationGateway.getLinkedRegistrations(request.email).await().filter { it.isRegVerified() }
            val socialRegistration = linkedRegistrations.firstOrNull { it.regType === social }

            if (socialRegistration == null)
                throw RuntimeException("Not found social profile")
            if (linkedRegistrations.count() == 1)
                throw RuntimeException("Can not unlink the last user registration")

            if (userRegistrationGateway.deleteRegistrationById(socialRegistration.id)
                    .await().deletedCount.toInt() == 0
            ) {
                throw RuntimeException("Delete record failed")
            }

            return response.buildStatus("Social registration unlinked successfully")
        } catch (t: Throwable) {
            logger.error("Unlink for {} registration failed...", social, t)
            return response.buildStatus("Unlink failed")
        }
    }
}

private fun UserMessage.UnlinkSocialResponse.Builder.buildStatus(
    message: String = "",
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.UnlinkSocialResponse = this.setStatus(
    com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)
).build()

private fun UserMessage.AddMissingSocialEmailResponse.Builder.buildStatus(
    message: String = "",
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.AddMissingSocialEmailResponse = this.setStatus(
    com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)
).build()

private fun UserMessage.AddSocialRegistrationResponse.Builder.buildStatus(
    message: String = "",
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.AddSocialRegistrationResponse = this.setStatus(
    com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)
).build()

private fun UserMessage.UnlinkRelinkSocialRegistrationResponse.Builder.buildStatus(
    message: String = "",
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.UnlinkRelinkSocialRegistrationResponse = this.setStatus(
    com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)
).build()
