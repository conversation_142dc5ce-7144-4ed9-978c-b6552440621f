package portal.user.configuration

import common.libs.jwt.JwtConfig

/**
 * @property serverConf Server
 * @property dbConf DatabaseConfig
 *
 * <AUTHOR>
 */
data class Configuration(
    val serverConf: ServerConfig,
    val dbConf: DatabaseConfig,
    val seConf: ServiceExplorerConfig,
    val emailConf: NotificationEmailConfig,
    val socialLoginConf: SocialLoginConfig,
    val jwtConf: JwtConfig,
    val resetPasswordConf: ResetPasswordConfig,
    val cacheServiceConf: CacheServiceConf
)
