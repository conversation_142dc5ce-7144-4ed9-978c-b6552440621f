package portal.user.pojo

import org.bson.codecs.pojo.annotations.*
import org.bson.types.ObjectId
import java.util.*

/**
 *  Model for the email registration flow
 */
@BsonDiscriminator(key = "regType", value = "EMAIL")
class EmailRegistrationPojo @BsonCreator constructor(
    @BsonId id: String = ObjectId().toHexString(),
    @BsonProperty("email") email: String,
    @BsonProperty("username") username: String = "",
    @BsonProperty("phone") phone: String = "",

    @BsonProperty("password") val password: String,
    @BsonProperty("status") val status: EmailRegistrationStatus,
    @BsonProperty("verificationCode") val verificationCode: String? = null,
    @BsonProperty("lastSent") var lastSent: Date? = null,
): UserRegistrationPojo(RegistrationType.EMAIL, id, email, username, phone)