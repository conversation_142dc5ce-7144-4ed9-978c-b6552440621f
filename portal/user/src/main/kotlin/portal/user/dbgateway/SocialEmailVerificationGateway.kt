package portal.user.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.client.model.Updates
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import portal.user.koin.DatabaseModule
import portal.user.pojo.FacebookRegistrationPojo
import portal.user.pojo.RegistrationType
import portal.user.pojo.UserRegistrationPojo
import portal.user.utils.MongoUtils
import java.time.Instant
import java.time.temporal.ChronoUnit

/**
 *  Handle data for the email validation flow
 */
@org.koin.core.annotation.Singleton
class SocialEmailVerificationGateway constructor(
    @Named(DatabaseModule.MONGO_COLLECTION_USER_REGISTRATION_POJO) private val collection: MongoCollection<UserRegistrationPojo>,
) : IEmailVerificationGateway, Logging {

    // All social types (Facebook, Google) share the same fields name
    private val verifiedField = FacebookRegistrationPojo::verified.name
    private val verificationCodeField = FacebookRegistrationPojo::verificationCode.name
    private val lastSentField = FacebookRegistrationPojo::lastSent.name

    /**
     * Save the verification code for the email with timestamp to determine the time it can be sent again
     */
    override fun setVerificationCode(registrationId: String, verificationCode: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match email and not EMAIL
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.ne(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // valid verification status
                    Filters.ne(UserRegistrationPojo::email.name, ""),
                    Filters.eq(verifiedField, false)
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(verificationCodeField, verificationCode.uppercase()),
                    Updates.currentDate(lastSentField),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Set verification code for ID {} failed ... ", registrationId, it)
        }.firstOrError()
    }

    /**
     * Find and validate the email with verification code
     */
    override fun verifyEmail(registrationId: String, verificationCode: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match ID and match registration type (not EMAIL)
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.ne(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // match verification code
                    Filters.eq(verificationCodeField, verificationCode.uppercase()),
                    // verification status == VERIFICATION_PENDING && lastSent is not expired (30 minutes)
                    Filters.eq(verifiedField, false),
                    Filters.gte(lastSentField, Instant.now().minus(30, ChronoUnit.MINUTES)),
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(verifiedField, true),
                    Updates.unset(verificationCodeField),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Verify ID {} failed with verificationCode {} ... ", registrationId, verificationCode, it)
        }.firstOrError()
    }

    override fun forceVerify(registrationId: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match ID and match registration type (not EMAIL)
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.ne(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // verification status == VERIFICATION_PENDING && lastSent is not expired (30 minutes)
                    Filters.eq(verifiedField, false),
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(verifiedField, true),
                    Updates.unset(verificationCodeField),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Verify ID {} failed ... ", registrationId, it)
        }.firstOrError()
    }
}