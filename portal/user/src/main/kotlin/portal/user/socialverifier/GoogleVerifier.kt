package portal.user.socialverifier

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import portal.user.pojo.RegistrationType
import portal.user.pojo.SocialLoginInfo

@org.koin.core.annotation.Singleton
class GoogleVerifier(
    private val verifier: GoogleIdTokenVerifier,
) : IBaseVerifier {

    override fun verifyUser(token: String): SocialLoginInfo {
        if (token.isEmpty()) throw IllegalArgumentException("token is empty")

        val idToken: GoogleIdToken = this.verifier.verify(token)
        val payload: GoogleIdToken.Payload = idToken.payload

        val userId: String = payload.subject
        val email: String = payload.email
        val emailVerified: Boolean = payload.emailVerified
        val name = payload["name"] as String
        val picture = payload["picture"] as String
        val familyName = payload["family_name"] as? String ?: ""
        val givenName = payload["given_name"] as? String ?: ""

        return SocialLoginInfo(
            provider = RegistrationType.GOOGLE.name,
            socialId = userId,
            email = email,
            avatarUrl = picture,
            username = name,
            firstName = familyName,
            lastName = givenName,
            emailVerified = emailVerified,
        )
    }

}