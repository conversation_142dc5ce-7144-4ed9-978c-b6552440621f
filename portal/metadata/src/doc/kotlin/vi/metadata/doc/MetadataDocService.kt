package vi.metadata.doc

import common.libs.logger.Logging
import io.grpc.Status
import io.grpc.StatusRuntimeException
import kotlinx.coroutines.flow.Flow
import org.bson.BsonDocument
import vi.metadata.doc.dbgateway.MetadataDeleteJobsGateway
import vi.metadata.doc.dbgateway.MetadataDocGateway
import vi.metadata.doc.pojo.MetadataDeleteJobs
import vi.metadata.doc.pojo.MetadataDocDocument
import vi.metadata.doc.pojo.MetadataField
import vi.metadata.doc.proto.MetadataDocMessage
import vi.metadata.doc.proto.MetadataDocMessage.MetadataDocProto
import vi.metadata.doc.proto.MetadataDocServiceGrpcKt


/**
 *
 * <AUTHOR>
 */
class MetadataDocService constructor(
    private val db: MetadataDocGateway,
    private val metadataDelecteJobsGateway: MetadataDeleteJobsGateway,
) : MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineImplBase(), Logging {
    override suspend fun createMetadataDoc(request: MetadataDocMessage.CreateMetadataDocRequest): MetadataDocMessage.MetadataDocResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)

        return try {
            val metadataProto = request.metadata
            val metadata = MetadataDocDocument(
                docId = metadataProto.docId,
                editorType = metadataProto.editorType.toString(),
                metadataType = metadataProto.metadataType,
                metadataDetails = BsonDocument.parse(metadataProto.metadataDetails)
            )

            db.insert(metadata) ?: run {
                logger.error("[{}] failed to create doc metadata cause cannot insert db ", request.requestId)
                throw Status.INTERNAL.withCause(RuntimeException("cannot insert db"))
                    .withDescription("failed to create doc metadata")
                    .augmentDescription(request.requestId)
                    .asRuntimeException()
            }

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.metadata = metadataProto
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.build()
            logger.info("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to create doc metadata by... ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to create doc metadata")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun updateMetadataDoc(request: MetadataDocMessage.UpdateMetadataDocRequest): MetadataDocMessage.MetadataDocResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocResponse.newBuilder()
        return try {
            val newlyDoc = db.updateDocData(request.docId, BsonDocument.parse(request.metadataDetails)) ?: run {
                logger.error(
                    "[{}] failed to update doc metadata cause not found doc {} in db ",
                    request.requestId,
                    request.docId
                )
                throw Status.INTERNAL.withCause(RuntimeException("cannot update doc in db"))
                    .withDescription("failed to update doc metadata")
                    .augmentDescription(request.requestId)
                    .asRuntimeException()
            }

            responseBuilder
                .setMetadata(MetadataDocProto.newBuilder().buildMetaDoc(newlyDoc))
                .setStatus(statusBuilder.setCode(Status.OK.code.value()))
                .build()
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to update doc metadata by... ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update doc metadata")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun updateMetadataDocById(request: MetadataDocMessage.UpdateMetadataDocByIdRequest): MetadataDocMessage.MetadataDocResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocResponse.newBuilder()
        return try {
            val newlyDoc = db.updateDocDataById(request.id, BsonDocument.parse(request.metadataDetails)) ?: run {
                logger.error(
                    "[{}] failed to update doc metadata cause not found doc {} in db ",
                    request.requestId,
                    request.id
                )
                throw Status.INTERNAL.withCause(RuntimeException("cannot update doc in db"))
                    .withDescription("failed to update doc metadata")
                    .augmentDescription(request.requestId)
                    .asRuntimeException()
            }

            responseBuilder
                .setMetadata(MetadataDocProto.newBuilder().buildMetaDoc(newlyDoc))
                .setStatus(statusBuilder.setCode(Status.OK.code.value()))
                .build()
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to update doc metadata by... ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update doc metadata")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun duplicateMetadataDoc(request: MetadataDocMessage.DuplicateMetadataDocRequest): MetadataDocMessage.MetadataDocResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocResponse.newBuilder()

        return try {
            val metadata = db.duplicate(
                request.metadataDetailsList.map { MetadataField(it.fieldName, it.dataType, it.value) },
                request.docId,
                request.newDocId,
                request.metadataType
            ) ?: run {
                logger.error("[{}] failed to duplicate metadata doc... ", request)
                throw Status.NOT_FOUND
                    .withDescription("failed to duplicate metadata doc, not found doc info")
                    .asRuntimeException()
            }

            val metadataProto = metadata.let {
                MetadataDocProto.newBuilder().buildMetaDoc(it)
                    .build()
            }
            responseBuilder
                .setStatus(statusBuilder.setCode(Status.OK.code.value()))
                .setMetadata(metadataProto)
                .build()

        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to duplicate metadata doc... ", request, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to duplicate metadata doc")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun loadMetadataDoc(request: MetadataDocMessage.LoadMetadataDocRequest): MetadataDocMessage.MetadataDocsResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocsResponse.newBuilder()

        return try {
            val metadata = db.load(
                request.dataFilterList.map { MetadataField(it.fieldName, it.dataType, it.value) },
                if (request.hasDocId()) request.docId.value else null,
                if (request.hasMetadataType()) request.metadataType.value else null,
                if (request.hasEditorType()) request.editorType.value else null,
                if (request.hasLimit()) request.limit.value else null,
                if (request.hasOffset()) request.offset.value else null,
                if (request.hasTextSearch()) request.textSearch.value else null
            )

            responseBuilder.addAllMetadata(metadata.map {
                MetadataDocProto.newBuilder().buildMetaDoc(it).build()
            })

            if (request.hasShouldCount() && request.shouldCount) {
                // for some features like search & pagination
                val totalCount = db.count(
                    request.dataFilterList.map { MetadataField(it.fieldName, it.dataType, it.value) },
                    if (request.hasDocId()) request.docId.value else null,
                    if (request.hasMetadataType()) request.metadataType.value else null,
                    if (request.hasEditorType()) request.editorType.value else null,
                    if (request.hasTextSearch()) request.textSearch.value else null
                )
                responseBuilder.setTotalCount(com.google.protobuf.UInt32Value.of(totalCount))
            }

            responseBuilder.setStatus(statusBuilder.setCode(Status.OK.code.value())).build()
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to load metadata doc... ", request, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to load metadata doc")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }


    override suspend fun deleteManyMetadataDocs(request: MetadataDocMessage.DeleteManyMetadataDocsRequest): MetadataDocMessage.DeleteManyMetadataDocsResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.DeleteManyMetadataDocsResponse.newBuilder()
        return try {
            db.deleteManyDocData(
                request.docId,
                request.metadataType,
                request.editorType,
                request.dataFiltersList.map { MetadataField(it.fieldName, it.dataType, it.value) })
            responseBuilder.setStatus(statusBuilder.setCode(Status.OK.code.value())).build()
        } catch (t: Throwable) {
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to delete many docs")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    override suspend fun deleteMetadataDoc(request: MetadataDocMessage.DeleteMetadataDocRequest): MetadataDocMessage.MetadataDocResponse {
        return super.deleteMetadataDoc(request)
    }

    override fun deleteMetadataDocs(requests: Flow<MetadataDocMessage.DeleteMetadataDocRequest>): Flow<MetadataDocMessage.MetadataDocResponse> {
        return super.deleteMetadataDocs(requests)
    }

    override suspend fun updateMetadataDocField(request: MetadataDocMessage.UpdateMetadataDocFieldRequest): MetadataDocMessage.MetadataDocResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = MetadataDocMessage.MetadataDocResponse.newBuilder()

        return try {
            val metadata = db.patchDocData(
                request.metadataDetailsList.map { MetadataField(it.fieldName, it.dataType, it.value) },
                request.docId,
                request.editorType,
                request.metadataType
            ) ?: run {
                logger.error("[{}] failed to update metadata doc field... ", request)
                throw Status.NOT_FOUND
                    .withDescription("failed to update metadata doc field, not found doc info")
                    .asRuntimeException()
            }

            val metadataProto = metadata.let {
                MetadataDocProto.newBuilder().buildMetaDoc(it)
                    .build()
            }
            responseBuilder
                .setStatus(statusBuilder.setCode(Status.OK.code.value()))
                .setMetadata(metadataProto)
                .build()

        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] exception to update metadata doc field... ", request, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to update metadata doc field")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    fun MetadataDocProto.Builder.buildMetaDoc(metadata: MetadataDocDocument): MetadataDocProto.Builder {
        return this.setId(metadata.id).setDocId(metadata.docId)
            .setMetadataType(metadata.metadataType)
            .setEditorType(metadata.editorType)
            .setMetadataDetails(metadata.metadataDetails.toJson())
    }
}