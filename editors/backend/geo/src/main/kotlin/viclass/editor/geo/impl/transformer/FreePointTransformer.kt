package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.transformdata.FreePointTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.transformer.Transformer

/**
 * param store is a position = [x, y, z]
 */
class FreePointTransformer constructor(): Transformer<FreePointTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: FreePointTransformData, pos: Vector3D) {
        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(ParamKind.PK_Value) as ParamStoreArray
        ps.values = pos.toArray().map { it.toString() }
    }
}