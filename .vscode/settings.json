{"cSpell.allowCompoundWords": true, "cSpell.autoFormatConfigFile": false, "cSpell.diagnosticLevel": "Hint", "cSpell.enabled": true, "cSpell.language": "en,en-US", "cSpell.words": ["assistive", "AZERTY", "colem<PERSON>", "Cortexjs", "frak", "Genfrac", "grapheme", "hskip", "iframes", "infty", "latexify", "mathbb", "mathbf", "Mathematica", "mathrm", "mhchem", "msubsup", "overriden", "qwertz", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "unstyled", "vlist", "zindex", "mbin", "mclose", "minner", "mopen", "mord", "mpunct", "mrel", "numer", "prec", "ssml", "textord", "xmlns"], "editor.formatOnSave": true, "editor.tabSize": 2, "eslint.format.enable": true, "eslint.lintTask.enable": true, "eslint.options": {"overrideConfigFile": ".eslintrc.json"}, "typescript.tsdk": "./node_modules/typescript/lib", "prettier.configPath": "./node_modules/@cortex-js/prettier-config/index.json"}