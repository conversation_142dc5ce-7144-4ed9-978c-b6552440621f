name: 🏷️　Label(<PERSON> Size)
on: pull_request_target
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: bubkoo/use-app-token@v1
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.PRIVATE_KEY }}
          variable_name: bot_token
      - uses: pascalgn/size-label-action@v0.1.1
        env:
          GITHUB_TOKEN: "${{ env.bot_token }}"
          IGNORED: "!.gitignore\npnpm-lock.yaml"
