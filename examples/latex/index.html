<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8" />
    <title>MathLive LaTeX Editor</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="stylesheet" href="../style.css" />
    <link rel="stylesheet" href="/dist/mathlive-static.css" />
    <link
      crossorigin
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,400;0,700;1,400;1,700&family=IBM+Plex+Sans:ital,wght@0,400;0,700;1,400;1,700&display=swap"
    />
    <style>
      /* .buttonbar button { */
      button {
        background: none;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-radius: 4px;
        color: #0066ce;
        fill: currentColor;
        position: relative;
        height: 36px;
        line-height: 36px;
        margin: 0 18px 0 0;
        min-width: 64px;
        padding: 0 16px;
        display: inline-block;
        overflow: hidden;
        will-change: box-shadow;
        transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),
          background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
          color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        outline: none;
        cursor: pointer;
        text-decoration: none;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        user-select: none;

        font-size: 16px;
        letter-spacing: 0.08929em;
        text-transform: uppercase;
        box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2),
          0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12);
      }

      button:first-child {
        margin-left: 0;
      }

      button:hover {
        background-color: rgba(0, 0, 0, 0.08);
      }

      button:active {
        background-color: white;
      }

      button.primary,
      button.active {
        color: white;
        fill: currentColor;
        background: #0066ce;
      }

      button.primary:hover {
        background-color: rgba(0, 102, 206, 0.58);
      }

      button.primary:active {
        color: #0066ce;
        fill: currentColor;
        background-color: white;
      }

      button.round {
        width: 64px;
        height: 64px;
        border-radius: 50%;
      }

      .buttonbar {
        margin-top: 1em;
      }

      #inputs {
        display: flex;
        flex-flow: column;
      }

      #mf,
      #latex {
        width: 100%;
      }

      #latex {
        font-size: 16px;
      }

      #latex::selection {
        background: rgba(240, 198, 116, 0.99);
        color: #333;
        opacity: 1;
      }

      math-field {
        border: 1px solid #eee;
        border-radius: 5px;
        padding: 5px;
        background: #fff;
        font-size: 24px;
      }

      h2 {
        margin: 1em 0 0.5em 0;
        padding: 0;
      }
    </style>
  </head>

  <body>
    <header>
      <h1>MathLive LaTeX Editor</h1>
    </header>
    <main>
      <div id="inputs">
        <h2>Formula</h2>
        <math-field id="mf"></math-field>
        <h2>LaTeX</h2>
        <textarea
          class="output"
          id="latex"
          autocapitalize="off"
          autocomplete="off"
          autocorrect="off"
          spellcheck="false"
        ></textarea>
      </div>
      <div id="output"></div>
      <div class="buttonbar">
        <button id="save-to-png">Save to PNG</button>
      </div>
    </main>

    <script type="module">
      import { convertLatexToMarkup } from '/dist/mathlive.mjs';
      // import 'https://unpkg.com/mathlive?module';
      import * as htmlToImage from './html-to-image/index.mjs';

      const mf = document.getElementById('mf');
      mf.addEventListener('input', (evt) => {
        document.getElementById('latex').value = mf.getValue();
      });

      document.getElementById('latex').addEventListener('input', (ev) => {
        mf.setValue(ev.target.value);
      });

      document.getElementById('save-to-png').addEventListener('click', (ev) => {
        const div = document.getElementById('output');
        div.innerHTML = convertLatexToMarkup(
          document.getElementById('mf').value
        );
        htmlToImage.toPng(div).then((data) => {
          var link = document.createElement('a');
          link.download = 'formula.png';
          link.href = data;
          link.click();
        });
      });

      function escapeHtml(string) {
        return String(string).replace(/[&<>"'`=/\u200b]/g, function (s) {
          return (
            {
              '&': '&amp;',
              '<': '&lt;',
              '>': '&gt;',
              '"': '&quot;',
              "'": '&#39;',
              '/': '&#x2F;',
              '`': '&#x60;',
              '=': '&#x3D;',
              '\u200b': '&amp;#zws;',
            }[s] || s
          );
        });
      }
    </script>
  </body>
</html>
