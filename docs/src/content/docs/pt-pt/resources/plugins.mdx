---
title: Plugins e Integrações
description: Descubra ferramentas da comunidade e integrações que estendem o Starlight!
sidebar:
  order: 1
---

:::tip[Inclua o seu!]
Já construiu um plugin ou uma ferramenta para o Starlight?
Então abra um PR e adicione o seu link nesta página!
:::

## Plugins

Os [plugins](/pt-pt/reference/plugins/) podem personalizar ou estender a configuração do Starlight, a interface do utilizador e o comportamento, sendo também fáceis de compartilhar e de reutilizar.
Estenda o seu site com os plugins oficiais suportados pela equipa Starlight e com plugins da comunidade mantidos por outros utilizadores Starlight.

### Plugins oficiais

<CardGrid>
	<LinkCard
		href="/pt-br/guides/site-search/#algolia-docsearch"
		title="Algolia DocSearch"
		description="Substitua o fornecedor de pesquisa Pagefind pelo Algolia DocSearch."
	/>
</CardGrid>

### Plugins da comunidade

<CardGrid>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-links-validator"
		title="starlight-links-validator"
		description="Verifique links incorretos nas suas páginas do Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-typedoc"
		title="starlight-typedoc"
		description="Crie páginas Starlight a partir de TypeScript utilizando o TypeDoc."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-blog"
		title="starlight-blog"
		description="Adicione um blog ao seu site de documentação."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-openapi"
		title="starlight-openapi"
		description="Crie páginas de documentação a partir de especificações OpenAPI/Swagger."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-obsidian"
		title="starlight-obsidian"
		description="Publique cofres de Obsidian no seu site Starlight."
	/>
	<LinkCard
		href="https://astro-ghostcms.xyz/intro/starlight/install/"
		title="starlight-ghostcms"
		description="Adicione os artigos do seu blog GhostCMS ao lado das páginas Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-image-zoom"
		title="starlight-image-zoom"
		description="Adicione funções de zoom à sua documentação do Starlight."
	/>
		<LinkCard
		href="https://github.com/lorenzolewis/starlight-utils"
		title="starlight-utils"
		description="Extenda o Starlight com uma coleção de utilitários comuns."
	/>

</CardGrid>

## Ferramentas e integrações da comunidade

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

Estas ferramentas e integrações da comunidade podem ser usadas para adicionar funcionalidades ao seu site Starlight.

<CardGrid>
	<LinkCard
		href="https://www.feelback.dev/blog/new-astro-starlight-integration/"
		title="FeelBack"
		description="Adicione um sistema de feedback de utilizador às páginas da documentação."
	/>
	<LinkCard
		href="https://github.com/val-town/notion-to-astro"
		title="notion-to-astro"
		description="Converta exportações do Notion para documentos do Starlight"
	/>
	<LinkCard
		href="https://github.com/mattjennings/astro-live-code"
		title="astro-live-code"
		description="Apresente blocos de código MDX como componentes interativos"
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-i18n"
		title="starlight-i18n"
		description="Extensão para o Visual Studio Code que ajuda a traduzir páginas do Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-package-managers"
		title="starlight-package-managers"
		description="Mostra de forma rápida comandos npm relacionados para vários gestores de packages."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-showcases"
		title="starlight-showcases"
		description="Conjunto de componentes do Starlight para cosntruir páginas de demonstração."
	/>
</CardGrid>
