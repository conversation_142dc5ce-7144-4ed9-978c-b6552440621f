---
title: Plugins and Integrations
description: Discover community tools like plugins and integrations that extend Starlight!
sidebar:
  order: 1
---

:::tip[Add your own!]
Have you built a plugin or a tool for Starlight?
Open a PR adding a link to this page!
:::

## Plugins

[Plugins](/reference/plugins/) can customize Starlight configuration, UI, and behavior, while also being easy to share and reuse.
Extend your site with official plugins supported by the Starlight team and community plugins maintained by Starlight users.

### Official plugins

<CardGrid>
	<LinkCard
		href="/guides/site-search/#algolia-docsearch"
		title="Algolia DocSearch"
		description="Replace Pagefind, the default search provider, with Algolia DocSearch."
	/>
</CardGrid>

### Community plugins

<CardGrid>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-links-validator"
		title="starlight-links-validator"
		description="Check for broken links in your Starlight pages."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-typedoc"
		title="starlight-typedoc"
		description="Generate Starlight pages from TypeScript using TypeDoc."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-blog"
		title="starlight-blog"
		description="Add a blog to your documentation site."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-openapi"
		title="starlight-openapi"
		description="Create documentation pages from OpenAPI/Swagger specifications."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-obsidian"
		title="starlight-obsidian"
		description="Publish Obsidian vaults in your Starlight site."
	/>
	<LinkCard
		href="https://astro-ghostcms.xyz/intro/starlight/install/"
		title="starlight-ghostcms"
		description="Add your GhostCMS blog posts alongside your Starlight Docs"
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-image-zoom"
		title="starlight-image-zoom"
		description="Add zoom capabilities to your documentation images."
	/>
	<LinkCard
		href="https://github.com/lorenzolewis/starlight-utils"
		title="starlight-utils"
		description="Extend Starlight with a collection of common utilities."
	/>
	<LinkCard
		href="https://github.com/trueberryless/starlight-view-modes"
		title="starlight-view-modes"
		description="Add different view mode capabilities to your documentation website."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-versions"
		title="starlight-versions"
		description="Version your Starlight documentation pages."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-theme-rapide"
		title="starlight-theme-rapide"
		description="Starlight theme inspired by the Visual Studio Code Vitesse theme."
	/>
</CardGrid>

## Community tools and integrations

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

These community tools and integrations can be used to add features to your Starlight site.

<CardGrid>
	<LinkCard
		href="https://www.feelback.dev/blog/new-astro-starlight-integration/"
		title="FeelBack"
		description="Add a user feedback system to your docs pages."
	/>
	<LinkCard
		href="https://github.com/val-town/notion-to-astro"
		title="notion-to-astro"
		description="Convert Notion exports to Astro Starlight docs"
	/>
	<LinkCard
		href="https://github.com/mattjennings/astro-live-code"
		title="astro-live-code"
		description="Render your MDX code blocks as interactive components"
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-i18n"
		title="starlight-i18n"
		description="Visual Studio Code extension to help translate Starlight pages."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-package-managers"
		title="starlight-package-managers"
		description="Quickly display npm related commands for multiple package managers."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-showcases"
		title="starlight-showcases"
		description="Set of Starlight components to author showcase pages."
	/>
</CardGrid>
