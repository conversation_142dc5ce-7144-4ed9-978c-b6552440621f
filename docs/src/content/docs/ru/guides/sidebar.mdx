---
title: Боковая панель
description: Узнайте, как установить и настроить навигационные ссылки в боковой панели сайта Starlight.
---

import { FileTree } from '@astrojs/starlight/components';
import SidebarPreview from '~/components/sidebar-preview.astro';

Хорошо организованная боковая панель — ключ к хорошей документации, поскольку это один из основных способов навигации пользователей по вашему сайту. Starlight предоставляет полный набор опций для настройки макета и содержимого боковой панели.

## Стандартная боковая панель

По умолчанию Starlight автоматически генерирует боковую панель на основе структуры файловой системы вашей документации, используя свойство `title` каждого файла в качестве элемента боковой панели.

Например, при следующей структуре файлов:

<FileTree>

- src/
  - content/
    - docs/
      - constellations/
        - andromeda.md
        - orion.md
      - stars/
        - betelgeuse.md

</FileTree>

Будет автоматически сгенерирована следующая боковая панель:

<SidebarPreview
	config={[
		{
			label: 'constellations',
			items: [
				{ label: 'Андромеда', link: '' },
				{ label: 'Орион', link: '' },
			],
		},
		{
			label: 'stars',
			items: [{ label: 'Бетельгейзе', link: '' }],
		},
	]}
/>

Узнайте больше об автоматически генерируемых боковых панелях в разделе [Автогенерируемые группы](#автогенерируемые-группы).

## Добавление ссылок и групп ссылок

Чтобы настроить свои [ссылки](#ссылки) и [группы ссылок](#группы) (внутри сворачиваемого заголовка) в боковой панели, используйте свойство [`starlight.sidebar`](/ru/reference/configuration/#sidebar) в `astro.config.mjs`.

Комбинируя ссылки и группы, вы можете создавать разнообразные макеты боковой панели.

### Ссылки

Добавьте ссылку на внутреннюю или внешнюю страницу, используя объект со свойствами `label` и `link`.

```js "label:" "link:"
starlight({
	sidebar: [
		// Ссылка на страницу луны Ганимед.
		{ label: 'Ганимед', link: '/moons/ganymede/' },
		// Внешняя ссылка на веб-сайт NASA.
		{ label: 'NASA', link: 'https://www.nasa.gov/' },
	],
});
```

Конфигурация выше создаёт следующую боковую панель:

<SidebarPreview
	config={[
		{ label: 'Ганимед', link: '' },
		{ label: 'NASA', link: 'https://www.nasa.gov/' },
	]}
/>

### Группы

Вы можете структурировать вашу боковую панель, группируя связанные ссылки вместе под раскрывающимся заголовком.
Группы могут содержать как ссылки, так и другие подгруппы.

Добавьте группу, используя объект со свойствами `label` и `items`.
`label` будет использован как заголовок для группы.
Добавляйте ссылки или подгруппы в массив `items`.

```js /^\s*(label:|items:)/
starlight({
	sidebar: [
		// Группа ссылок с названием «Созвездия».
		{
			label: 'Созвездия',
			items: [
				{ label: 'Карина', link: '/constellations/carina/' },
				{ label: 'Центавр', link: '/constellations/centaurus/' },
				// Вложенная группа ссылок для сезонных созвездий.
				{
					label: 'Сезонные',
					items: [
						{ label: 'Андромеда', link: '/constellations/andromeda/' },
						{ label: 'Орион', link: '/constellations/orion/' },
						{ label: 'Малая Медведица', link: '/constellations/ursa-minor/' },
					],
				},
			],
		},
	],
});
```

Вышеуказанная конфигурация генерирует следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Созвездия',
			items: [
				{ label: 'Карина', link: '' },
				{ label: 'Центавр', link: '' },
				{
					label: 'Сезонные',
					items: [
						{ label: 'Андромеда', link: '' },
						{ label: 'Орион', link: '' },
						{ label: 'Малая Медведица', link: '' },
					],
				},
			],
		},
	]}
/>

### Автогенерируемые группы

Starlight может автоматически генерировать группу в вашей боковой панели, основываясь на директориях в вашей документации.
Это полезно, когда вы не хотите вручную вводить каждый элемент боковой панели в группе.

По умолчанию страницы сортируются в алфавитном порядке в соответствии со свойством [`slug`](/ru/reference/overrides/#slug) или именем файла.

Добавьте автогенерируемую группу, используя объект со свойствами `label` и `autogenerate`. Ваша конфигурация `autogenerate` должна указывать `directory`, которая будет использоваться для записей боковой панели. Например, со следующей конфигурацией:

```js "label:" "autogenerate:"
starlight({
	sidebar: [
		{
			label: 'Созвездия',
			// Автогенерация группы ссылок для директории 'constellations'.
			autogenerate: { directory: 'constellations' },
		},
	],
});
```

И следующей структурой файлов:

<FileTree>

- src/
  - content/
    - docs/
      - constellations/
        - carina.md
        - centaurus.md
        - seasonal/
          - andromeda.md

</FileTree>

Будет сгенерирована следующая боковая панель:

<SidebarPreview
	config={[
		{
			label: 'Созвездия',
			items: [
				{ label: 'Карина', link: '' },
				{ label: 'Центавр', link: '' },
				{
					label: 'seasonal',
					items: [{ label: 'Андромеда', link: '' }],
				},
			],
		},
	]}
/>

#### Настройка сгенерированных ссылок через метаданные

Используйте [поле `sidebar`](/ru/reference/frontmatter/#sidebar) в метаданных страниц для настройки автоматически генерируемых ссылок.

Параметры в метаданных для боковой панели позволяют [установить метку](/ru/reference/frontmatter/#label) или добавить [значок](/ru/reference/frontmatter/#badge) к ссылке, [скрыть](/ru/reference/frontmatter/#hidden) ссылку из боковой панели или определить её [порядок](/ru/reference/frontmatter/#order) в общем списке.

```md "sidebar:"
---
# src/content/docs/example.md
title: Моя страница
sidebar:
  # Установить текст для ссылки
  label: Текст в боковой панели
  # Установить порядок для ссылки (меньшие числа отображаются выше)
  order: 2
  # Добавить значок к ссылке
  badge:
    text: Новое
    variant: tip
---
```

Автоматически созданная группа, включающая страницу с вышеуказанными метаданными, сгенерирует следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Руководства',
			items: [
				{ label: 'Моя страница', link: '' },
				{
					label: 'Текст в боковой панели',
					link: '',
					badge: { text: 'Новое', variant: 'tip' },
				},
				{ label: 'Другая страница', link: '' },
			],
		},
	]}
/>

:::note
Конфигурация `sidebar` в метаданных используется только для автоматически генерируемых ссылок и будет проигнорирована для вручную определённых ссылок.
:::

## Значки

Ссылки также могут включать свойство `badge` для отображения значка рядом с текстом ссылки.

```js {10,17}
starlight({
	sidebar: [
		{
			label: 'Звёзды',
			items: [
				// Ссылка со значком «Сверхгигант».
				{
					label: 'Персей',
					link: '/stars/persei/',
					badge: 'Сверхгигант',
				},
			],
		},
		// Автогенерируемая группа со значком "Устарело".
		{
			label: 'Луны',
			badge: 'Устарело',
			autogenerate: { directory: 'moons' },
		},
	],
});
```

Конфигурация выше создаст следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Звёзды',
			items: [
				{
					label: 'Персей',
					link: '',
					badge: { text: 'Сверхгигант', variant: 'default' },
				},
			],
		},
		{
			label: 'Луны',
			badge: { text: 'Устарело', variant: 'default' },
			items: [
				{
					label: 'Ио',
					link: '',
				},
				{
					label: 'Европа',
					link: '',
				},
				{
					label: 'Ганимед',
					link: '',
				},
			],
		},
	]}
/>

### Варианты значков и индивидуальная стилизация

Настройте стиль значка, используя объект со свойствами `text`, `variant` и `class`.

`text` представляет содержимое для отображения (например, «Новое»).
По умолчанию значок будет использовать акцентный цвет вашего сайта. Чтобы использовать встроенный стиль значка, установите для свойства `variant` одно из следующих значений: `note`, `tip`, `danger`, `caution` или `success`.

Кроме того, можно создать собственный стиль значка, задав свойству `class` имя класса CSS.

```js {10}
starlight({
	sidebar: [
		{
			label: 'Звёзды',
			items: [
				// Ссылка с жёлтым значком «Заглушка»
				{
					label: 'Сириус',
					link: '/stars/sirius/',
					badge: { text: 'Заглушка', variant: 'caution' },
				},
			],
		},
	],
});
```

Конфигурация выше создаст следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Звёзды',
			items: [
				{
					label: 'Сириус',
					link: '',
					badge: { text: 'Заглушка', variant: 'caution' },
				},
			],
		},
	]}
/>

## Пользовательские HTML-атрибуты

Ссылки также могут включать свойство `attrs` для добавления пользовательских HTML-атрибутов к элементу ссылки.

В следующем примере `attrs` используется для добавления атрибута `target="_blank"`, чтобы ссылка открывалась в новой вкладке, а также для применения атрибута `style`, чтобы курсивом выделить метку ссылки:

```js {10}
starlight({
	sidebar: [
		{
			label: 'Ресурсы',
			items: [
				// Внешняя ссылка на сайт NASA, открывающаяся в новой вкладке.
				{
					label: 'NASA',
					link: 'https://www.nasa.gov/',
					attrs: { target: '_blank', style: 'font-style: italic' },
				},
			],
		},
	],
});
```

Конфигурация выше создаст следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Ресурсы',
			items: [
				{
					label: 'NASA',
					link: 'https://www.nasa.gov/',
					attrs: {
						target: '_blank',
						style: 'font-style: italic',
					},
				},
			],
		},
	]}
/>

## Интернационализация

Используйте свойство `translations` для записей ссылок и групп, чтобы перевести метку ссылки или группы для каждого поддерживаемого языка, указав тег языка [BCP-47](https://www.w3.org/International/questions/qa-choosing-language-tags), например, `"en"`, `"ru"` или `"zh-CN"` в качестве ключа, и перевод метки — в качестве значения.
Свойство `label` будет использоваться для локали по умолчанию и для языков без перевода.

```js {5-7,11-13,18-20}
starlight({
	sidebar: [
		{
			label: 'Созвездия',
			translations: {
				'pt-BR': 'Constelações',
			},
			items: [
				{
					label: 'Андромеда',
					translations: {
						'pt-BR': 'Andrômeda',
					},
					link: '/constellations/andromeda/',
				},
				{
					label: 'Скорпион',
					translations: {
						'pt-BR': 'Escorpião',
					},
					link: '/constellations/scorpius/',
				},
			],
		},
	],
});
```

При просмотре документации на бразильском португальском языке будет сгенерирована следующая боковая панель:

<SidebarPreview
	config={[
		{
			label: 'Constelação',
			items: [
				{ label: 'Andrômeda', link: '' },
				{ label: 'Escorpião', link: '' },
			],
		},
	]}
/>

## Сворачиваемые группы

Группы ссылок могут быть свёрнуты по умолчанию, если установить свойство `collapsed` в `true`.

```js {5-6}
starlight({
	sidebar: [
		{
			label: 'Созвездия',
			// Сворачивание группы по умолчанию
			collapsed: true,
			items: [
				{ label: 'Андромеда', link: '/constellations/andromeda/' },
				{ label: 'Орион', link: '/constellations/orion/' },
			],
		},
	],
});
```

Конфигурация выше создает следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Созвездия',
			collapsed: true,
			items: [
				{ label: 'Андромеда', link: '' },
				{ label: 'Орион', link: '' },
			],
		},
	]}
/>

[Автогенерируемые группы](#автогенерируемые-группы) учитывают значение `collapsed` родительской группы:

```js {5-6}
starlight({
	sidebar: [
		{
			label: 'Созвездия',
			// Сворачивание группы и её автогенерируемых подгрупп по умолчанию.
			collapsed: true,
			autogenerate: { directory: 'constellations' },
		},
	],
});
```

Конфигурация выше создает следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Созвездия',
			collapsed: true,
			items: [
				{ label: 'Карина', link: '' },
				{ label: 'Центавр', link: '' },
				{
					label: 'seasonal',
					collapsed: true,
					items: [{ label: 'Андромеда', link: '' }],
				},
			],
		},
	]}
/>

Это поведение может быть переопределено путём установки свойства `autogenerate.collapsed`.

```js {5-7} "collapsed: true"
starlight({
	sidebar: [
		{
			label: 'Созвездия',
			// Не сворачивать группу «Созвездия», но сворачивать её
			// автоматически сгенерированные подгруппы.
			collapsed: false,
			autogenerate: { directory: 'constellations', collapsed: true },
		},
	],
});
```

Конфигурация выше создает следующую боковую панель:

<SidebarPreview
	config={[
		{
			label: 'Созвездия',
			items: [
				{ label: 'Карина', link: '' },
				{ label: 'Центавр', link: '' },
				{
					label: 'seasonal',
					collapsed: true,
					items: [{ label: 'Андромеда', link: '' }],
				},
			],
		},
	]}
/>
