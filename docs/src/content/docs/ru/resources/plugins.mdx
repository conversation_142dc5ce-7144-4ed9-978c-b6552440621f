---
title: Плагины и интеграции
description: Откройте для себя инструменты сообщества, такие как плагины и интеграции, которые расширяют возможности Starlight!
sidebar:
  order: 1
---

:::tip[Добавьте собственную работу!]
Вы создали плагин или инструмент для Starlight?
Откройте PR, добавив ссылку на эту страницу!
:::

## Плагины

[Плагины](/ru/reference/plugins/) могут настраивать конфигурацию, пользовательский интерфейс и поведение Starlight, а также легко распространяться и использоваться повторно.
Расширяйте свой сайт с помощью официальных плагинов, поддерживаемых командой Starlight, и плагинов сообщества, поддерживаемых пользователями Starlight.

### Официальные плагины

<CardGrid>
	<LinkCard
		href="/ru/guides/site-search/#algolia-docsearch"
		title="Algolia DocSearch"
		description="Замена Pagefind, провайдера поиска по умолчанию, на Algolia DocSearch."
	/>
</CardGrid>

### Плагины сообщества

<CardGrid>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-links-validator"
		title="starlight-links-validator"
		description="Поиск нерабочих ссылок на страницах Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-typedoc"
		title="starlight-typedoc"
		description="Генерация страниц Starlight из TypeScript с помощью TypeDoc."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-blog"
		title="starlight-blog"
		description="Добавление блога на сайт документации."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-openapi"
		title="starlight-openapi"
		description="Создание страниц документации на основе спецификаций OpenAPI/Swagger."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-obsidian"
		title="starlight-obsidian"
		description="Публикация хранилищ Obsidian на сайте Starlight."
	/>
	<LinkCard
		href="https://astro-ghostcms.xyz/intro/starlight/install/"
		title="starlight-ghostcms"
		description="Добавление записей в блог GhostCMS вместе с документами Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-image-zoom"
		title="starlight-image-zoom"
		description="Добавление возможности масштабирования к изображениям вашей документации."
	/>
	<LinkCard
		href="https://github.com/lorenzolewis/starlight-utils"
		title="starlight-utils"
		description="Расширение Starlight с помощью набора распространённых утилит."
	/>
	<LinkCard
		href="https://github.com/trueberryless/starlight-view-modes"
		title="starlight-view-modes"
		description="Добавление различных режимов просмотра на ваш сайт документации."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-versions"
		title="starlight-versions"
		description="Отображение версий для страниц документации Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-theme-rapide"
		title="starlight-theme-rapide"
		description="Тема Starlight, вдохновлённая темой Visual Studio Code Vitesse."
	/>
</CardGrid>

## Инструменты и интеграции от сообщества

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

Эти инструменты и интеграции сообщества можно использовать для добавления функций на ваш сайт Starlight.

<CardGrid>
	<LinkCard
		href="https://www.feelback.dev/blog/new-astro-starlight-integration/"
		title="FeelBack"
		description="Добавление системы обратной связи с пользователями на страницы ваших документов."
	/>
	<LinkCard
		href="https://github.com/val-town/notion-to-astro"
		title="notion-to-astro"
		description="Преобразование экспорта Notion в документы Astro Starlight."
	/>
	<LinkCard
		href="https://github.com/mattjennings/astro-live-code"
		title="astro-live-code"
		description="Отображение блоков кода MDX в виде интерактивных компонентов."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-i18n"
		title="starlight-i18n"
		description="Расширение Visual Studio Code для перевода страниц Starlight."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-package-managers"
		title="starlight-package-managers"
		description="Быстрое отображение команд, связанных с npm, для нескольких менеджеров пакетов."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-showcases"
		title="starlight-showcases"
		description="Набор компонентов Starlight для создания страниц-витрин."
	/>
</CardGrid>
