---
title: Référence de configuration
description: Une vue d'ensemble de toutes les options de configuration prises en charge par Starlight.
---

## Configuration de l'intégration `starlight`

Starlight est une intégration construite sur le framework web [Astro](https://astro.build). Vous pouvez configurer votre projet dans le fichier de configuration `astro.config.mjs` :

```js
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'Mon délicieux site de docs',
		}),
	],
});
```

Vous pouvez passer les options suivantes à l'intégration `starlight`.

### `title` (obligatoire)

**Type :** `string | Record<string, string>`

Définissez le titre de votre site web. Il sera utilisé dans les métadonnées et dans le titre de l'onglet du navigateur.

La valeur peut être une chaîne de caractères, ou pour les sites multilingues, un objet avec des valeurs pour chacune des différentes locales.
Lorsque vous utilisez la forme objet, les clés doivent être des étiquettes d'identification BCP-47 (par exemple `fr`, `ar`, ou `zh-CN`) :

```ts
starlight({
	title: {
		fr: 'Mon délicieux site de docs',
		de: 'Meine bezaubernde Dokumentationsseite',
	},
});
```

### `description`

**Type :** `string`

Définissez la description de votre site web. Utilisée dans les métadonnées partagées avec les moteurs de recherche dans la balise `<meta name="description">` si `description` n'est pas définie dans le frontmatter d'une page.

### `logo`

**Type :** [`LogoConfig`](#logoconfig)

Définit une image de logo à afficher dans la barre de navigation à côté ou à la place du titre du site. Vous pouvez soit définir une seule propriété `src`, soit définir des sources d'images séparées pour `light` et `dark`.

```js
starlight({
	logo: {
		src: './src/assets/my-logo.svg',
	},
});
```

#### `LogoConfig`

```ts
type LogoConfig = { alt?: string; replacesTitle?: boolean } & (
	| { src: string }
	| { light: string; dark: string }
);
```

### `tableOfContents`

**Type :** `false | { minHeadingLevel?: number; maxHeadingLevel?: number; }`  
**Par défaut :** `{ minHeadingLevel: 2; maxHeadingLevel: 3; }`

Configurez la table des matières affichée à droite de chaque page. Par défaut, les titres `<h2>` et `<h3>` seront inclus dans cette table des matières.

### `editLink`

**Type :** `{ baseUrl: string }`

Permet d'activer les liens "Modifier cette page" en définissant l'URL de base qu'ils doivent utiliser. Le lien final sera `editLink.baseUrl` + le chemin de la page actuelle. Par exemple, pour permettre l'édition de pages dans le repo `withastro/starlight` sur GitHub :

```js
starlight({
	editLink: {
		baseUrl: 'https://github.com/withastro/starlight/edit/main/',
	},
});
```

Avec cette configuration, une page `/introduction` aurait un lien d'édition pointant vers `https://github.com/withastro/starlight/edit/main/src/content/docs/introduction.md`.

### `sidebar`

**Type :** [`SidebarItem[]`](#sidebaritem)

Configure les éléments de navigation de la barre latérale de votre site.

Une barre latérale est un tableau de liens et de groupes de liens.
À l'exception des éléments utilisant `slug`, chaque élément doit comporter un `label` et l'une des propriétés suivantes :

- `link` — un lien uninque vers une URL spécifique, comme `'/home'` ou `'https://example.com'`.

- `slug` — une référence à une page interne, par exemple `'guides/getting-started'`.

- `items` — un tableau contenant plus de liens et des sous-groupes.

- `autogenerate` — un objet indiquant un répertoire de vos docs depuis lequel générer automatiquement un groupe de liens.

Les liens internes peuvent également être spécifiés sous forme de chaîne de caractères au lieu d'un objet avec une propriété `slug`.

```js
starlight({
	sidebar: [
		// Un lien unique étiqueté “Accueil”.
		{ label: 'Accueil', link: '/' },
		// Un groupe étiqueté “Débuter ici” contenant quatre liens.
		{
			label: 'Débuter ici',
			items: [
				// Utilisation de `slug` pour les liens internes.
				{ slug: 'intro' },
				{ slug: 'installation' },
				// Ou la forme simplifiée pour les liens internes.
				'tutorial',
				'next-steps',
			],
		},
		// Un groupe liant toutes les pages présentes dans le répertoire reference.
		{
			label: 'Référence',
			autogenerate: { directory: 'reference' },
		},
	],
});
```

#### Tri

Les groupes de barres latérales générées automatiquement sont triés par nom de fichier et par ordre alphabétique.
Par exemple, une page générée à partir de `astro.md` apparaîtrait au-dessus de la page de `starlight.md`.

#### Groupes rétractables

Les groupes de liens sont développés par défaut. Vous pouvez modifier ce comportement en définissant la propriété `collapsed` d'un groupe sur `true`.

Les sous-groupes générés automatiquement respectent la propriété `collapsed` de leur groupe parent par défaut. Définissez la propriété `autogenerate.collapsed` pour remplacer ce comportement.

```js {5,13}
sidebar: [
  // Un groupe rétractable de liens.
  {
    label: 'Collapsed Links',
    collapsed: true,
    items: ['intro', 'next-steps'],
  },
  // Un groupe développé contenant des sous-groupes générés automatiquement rétractés.
  {
    label: 'Reference',
    autogenerate: {
      directory: 'reference',
      collapsed: true,
    },
  },
],
```

#### Traduire les étiquettes

Si votre site est multilingue, le `label` de chaque élément est considéré comme étant dans la locale par défaut. Vous pouvez définir une propriété `translations` pour fournir des étiquettes pour les autres langues supportées :

```js {5,9,14}
sidebar: [
	// Un exemple de barre latérale avec des étiquettes traduites en portugais brésilien.
	{
		label: 'Commencer ici',
		translations: { 'pt-BR': 'Comece Aqui' },
		items: [
			{
				label: 'Bien démarrer',
				translations: { 'pt-BR': 'Introdução' },
				link: '/getting-started',
			},
			{
				label: 'Structure du projet',
				translations: { 'pt-BR': 'Estrutura de Projetos' },
				link: '/structure',
			},
		],
	},
];
```

#### `SidebarItem`

```ts
type SidebarItem =
	| string
	| ({
			translations?: Record<string, string>;
			badge?: string | BadgeConfig;
	  } & (
			| {
					// Lien
					link: string;
					label: string;
					attrs?: Record<string, string | number | boolean | undefined>;
			  }
			| {
					// Lien interne
					slug: string;
					label?: string;
					attrs?: Record<string, string | number | boolean | undefined>;
			  }
			| {
					// Groupe de liens
					label: string;
					items: SidebarItem[];
					collapsed?: boolean;
			  }
			| {
					// Groupe de liens généré automatiquement
					label: string;
					autogenerate: { directory: string; collapsed?: boolean };
					collapsed?: boolean;
			  }
	  ));
```

#### `BadgeConfig`

```ts
interface BadgeConfig {
	text: string;
	variant?: 'note' | 'tip' | 'caution' | 'danger' | 'success' | 'default';
	class?: string;
}
```

### `locales`

**Type :** <code>\{ \[dir: string\]: [LocaleConfig](#localeconfig) \}</code>

[Configurez l'internationalisation (i18n)](/fr/guides/i18n/) de votre site en définissant les `locales` supportées.

Chaque entrée doit utiliser comme clé le répertoire dans lequel les fichiers de cette langue sont sauvegardés.

```js
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'My Site',
			// Définit l'anglais comme langue par défaut pour ce site.
			defaultLocale: 'en',
			locales: {
				// Documentations en anglais danse trouve dans `src/content/docs/en/`
				en: {
					label: 'English',
				},
				// Documentations en Chinois simplifié se trouve dans `src/content/docs/zh-cn/`
				'zh-cn': {
					label: '简体中文',
					lang: 'zh-CN',
				},
				// Documentations en Arabe se trouve dans `src/content/docs/ar/`
				ar: {
					label: 'العربية',
					dir: 'rtl',
				},
			},
		}),
	],
});
```

#### `LocaleConfig`

```ts
interface LocaleConfig {
	label: string;
	lang?: string;
	dir?: 'ltr' | 'rtl';
}
```

Vous pouvez définir les options suivantes pour chaque locale :

##### `label` (obligatoire)

**Type :** `string`

L'étiquette de cette langue à afficher aux utilisateurs, par exemple dans le sélecteur de langue. Le plus souvent, il s'agit du nom de la langue tel qu'un utilisateur de cette langue s'attendrait à le lire, par exemple `"English"`, `"العربية"`, ou `"简体中文"`.

##### `lang`

**Type :** `string`

L'étiquette d’identification BCP-47 pour cette langue, par exemple `"en"`, `"ar"`, ou `"zh-CN"`. Si elle n'est pas définie, le nom du répertoire de la langue sera utilisé par défaut. Les étiquettes de langue avec des sous-étiquettes régionales (par exemple `"pt-BR"` ou `"en-US"`) utiliseront les traductions de l'interface utilisateur intégrées pour leur langue de base si aucune traduction spécifique à la région n'est trouvée.

##### `dir`

**Type :** `'ltr' | 'rtl'`

Le sens d'écriture de cette langue ; `"ltr"` pour gauche à droite (par défaut) ou `"rtl"` pour droite à gauche.

#### Locale racine

Vous pouvez servir la langue par défaut sans répertoire `/lang/` en définissant une locale avec la clé `root` :

```js {3-6}
starlight({
	locales: {
		root: {
			label: 'English',
			lang: 'en',
		},
		fr: {
			label: 'Français',
		},
	},
});
```

Par exemple, cela vous permet de servir `/getting-started/` comme une route anglaise et d'utiliser `/fr/getting-started/` comme la page française équivalente.

### `defaultLocale`

**Type :** `string`

Définit la langue par défaut pour ce site.
La valeur doit correspondre à l'une des clés de votre objet [`locales`](#locales).
(Si votre langue par défaut est votre [locale racine](#locale-racine), vous pouvez sauter cette étape).

La locale par défaut sera utilisée pour fournir un contenu de remplacement lorsque les traductions sont manquantes.

### `social`

import SocialLinksType from '~/components/social-links-type.astro';

**Type :** <SocialLinksType />

Détails optionnels sur les comptes de médias sociaux pour ce site. L'ajout de l'un d'entre eux les affichera sous forme de liens iconiques dans l'en-tête du site.

```js
starlight({
	social: {
		codeberg: 'https://codeberg.org/knut/examples',
		discord: 'https://astro.build/chat',
		github: 'https://github.com/withastro/starlight',
		gitlab: 'https://gitlab.com/delucis',
		linkedin: 'https://www.linkedin.com/company/astroinc',
		mastodon: 'https://m.webtoo.ls/@astro',
		threads: 'https://www.threads.net/@nmoodev',
		twitch: 'https://www.twitch.tv/bholmesdev',
		twitter: 'https://twitter.com/astrodotbuild',
		'x.com': 'https://x.com/astrodotbuild',
		youtube: 'https://youtube.com/@astrodotbuild',
	},
});
```

### `customCss`

**Type :** `string[]`

Fournit des fichiers CSS pour personnaliser l'aspect et la convivialité de votre site Starlight.

Prend en charge les fichiers CSS locaux relatifs à la racine de votre projet, par exemple `'./src/custom.css'`, et les CSS que vous avez installés en tant que module npm, par exemple `'@fontsource/roboto'`.

```js
starlight({
	customCss: ['./src/custom-styles.css', '@fontsource/roboto'],
});
```

### `expressiveCode`

**Type :** `StarlightExpressiveCodeOptions | boolean`  
**Par défaut :** `true`

Starlight utilise [Expressive Code](https://github.com/expressive-code/expressive-code/tree/main/packages/astro-expressive-code) pour afficher les blocs de code et ajouter le support pour mettre en évidence des portions d'exemples de code, ajouter des noms de fichiers aux blocs de code, et plus encore.
Consultez le [guide « Blocs de code »](/fr/guides/authoring-content/#blocs-de-code) pour apprendre à utiliser la syntaxe d'Expressive Code dans votre contenu Markdown et MDX.

Vous pouvez utiliser n'importe laquelle des [options de configuration standard d'Expressive Code](https://github.com/expressive-code/expressive-code/blob/main/packages/astro-expressive-code/README.md#configuration) ainsi que certaines propriétés spécifiques à Starlight, en les définissant dans l'option `expressiveCode` de Starlight.
Par exemple, définissez l'option `styleOverrides` d'Expressive Code pour remplacer le CSS par défaut. Cela permet des personnalisations comme donner à vos blocs de code des coins arrondis :

```js ins={2-4}
starlight({
	expressiveCode: {
		styleOverrides: { borderRadius: '0.5rem' },
	},
});
```

Si vous souhaitez désactiver Expressive Code, définissez `expressiveCode: false` dans votre configuration de Starlight :

```js ins={2}
starlight({
	expressiveCode: false,
});
```

En plus des options standard d'Expressive Code, vous pouvez également définir les propriétés suivantes spécifiques à Starlight dans votre configuration `expressiveCode` pour personnaliser davantage le comportement du thème pour vos blocs de code :

#### `themes`

**Type :** `Array<string | ThemeObject | ExpressiveCodeTheme>`  
**Par défaut :** `['starlight-dark', 'starlight-light']`

Définit les thèmes utilisés pour styliser les blocs de code.
Consultez la [documentation des `themes` d'Expressive Code](https://github.com/expressive-code/expressive-code/blob/main/packages/astro-expressive-code/README.md#themes) pour plus de détails sur les formats de thème pris en charge.

Starlight utilise les variantes claires et sombres du [thème Night Owl](https://github.com/sdras/night-owl-vscode-theme) de Sarah Drasner par défaut.

Si vous définissez au moins un thème clair et un thème sombre, Starlight gardera automatiquement le thème de bloc de code actif en phase avec le thème actuel du site.
Configurez ce comportement avec l'option [`useStarlightDarkModeSwitch`](#usestarlightdarkmodeswitch).

#### `useStarlightDarkModeSwitch`

**Type :** `boolean`  
**Par défaut :** `true`

Lorsque la valeur est `true`, les blocs de code basculent automatiquement entre les thèmes clairs et sombres lorsque le thème du site change.
Lorsque la valeur est `false`, vous devez ajouter manuellement du CSS pour gérer le basculement entre plusieurs thèmes.

:::note
Quand `themes` est défini, vous devez fournir au moins un thème sombre et un thème clair pour que le changement en mode sombre de Starlight fonctionne.
:::

#### `useStarlightUiThemeColors`

**Type :** `boolean`  
**Par défaut :** `true` si `themes` n'est pas défini, sinon `false`

Lorsque la valeur est `true`, les variables CSS de Starlight sont utilisées pour les couleurs des éléments d'interface utilisateur des blocs de code (arrière-plans, boutons, ombres, etc.), uniformément avec le [thème de couleur du site](/fr/guides/css-and-tailwind/#personnalisation-du-thème).
Lorsque la valeur est `false`, les couleurs fournies par le thème de coloration syntaxique actif sont utilisées pour ces éléments.

:::note
Lorsque des thèmes personnalisés sont utilisés et que cette option est définie à `true`, vous devez fournir au moins un thème sombre et un thème clair pour assurer un contraste de couleur approprié.
:::

### `pagefind`

**Type :** `boolean`  
**Par défaut :** `false`

Définit si le système de recherche du site par défaut de Starlight, [Pagefind](https://pagefind.app/), est activé.

Utilisez la valeur `false` pour désactiver l'indexation de votre site avec Pagefind.
Cela désactivera également l'interface de recherche par défaut de Starlight si utilisée.

### `head`

**Type :** [`HeadConfig[]`](#headconfig)

Ajoute des balises personnalisées au `<head>` de votre site Starlight.
Cela peut être utile pour ajouter des analyses et d'autres scripts et ressources tiers.

```js
starlight({
	head: [
		// Exemple : ajouter un script d'analyse Fathom tag.
		{
			tag: 'script',
			attrs: {
				src: 'https://cdn.usefathom.com/script.js',
				'data-site': 'MY-FATHOM-ID',
				defer: true,
			},
		},
	],
});
```

Les entrées dans `head` sont converties directement en éléments HTML et ne passent pas par le traitement de [script](https://docs.astro.build/fr/guides/client-side-scripts/#regroupement-de-script) ou de [style](https://docs.astro.build/fr/guides/styling/#styliser-avec-astro) d'Astro.
Si vous avez besoin d'importer des ressources locales comme des scripts, des styles ou des images, [redéfinissez le composant Head](/fr/guides/overriding-components/#réutiliser-un-composant-intégré).

#### `HeadConfig`

```ts
interface HeadConfig {
	tag: string;
	attrs?: Record<string, string | boolean | undefined>;
	content?: string;
}
```

### `lastUpdated`

**Type :** `boolean`  
**Par défaut :** `false`

Contrôlez si le pied de page affiche la date de la dernière mise à jour de la page.

Par défaut, cette fonctionnalité s'appuie sur l'historique Git de votre dépôt et peut ne pas être précise sur certaines plateformes de déploiement effectuant des [clonages superficiels](https://git-scm.com/docs/git-clone#Documentation/git-clone.txt---depthltdepthgt). Une page peut remplacer ce paramètre ou la date basée sur Git en utilisant [le champ `lastUpdated` du frontmatter](/fr/reference/frontmatter/#lastupdated).

### `pagination`

**Type :** `boolean`  
**Par défaut :** `true`

Définissez si le pied de page doit inclure des liens vers les pages précédentes et suivantes.

Une page peut remplacer ce paramètre ou le texte du lien et/ou l'URL en utilisant les champs de frontmatter [`prev`](/fr/reference/frontmatter/#prev) et [`next`](/fr/reference/frontmatter/#next).

### `favicon`

**Type :** `string`  
**Par défaut :** `'/favicon.svg'`

Définissez le chemin de l'icône par défaut pour votre site Web qui doit être situé dans le répertoire `public/` et être un fichier d'icône valide (`.ico`, `.gif`, `.jpg`, `.png` ou `.svg`).

```js
starlight({
  favicon: '/images/favicon.svg',
}),
```

Si vous avez besoin de définir des variantes supplémentaires ou des icônes de secours, vous pouvez ajouter des balises en utilisant l'option [`head`](#head) :

```js
starlight({
	favicon: '/images/favicon.svg',
	head: [
		// Ajouter une icône ICO de secours pour Safari.
		{
			tag: 'link',
			attrs: {
				rel: 'icon',
				href: '/images/favicon.ico',
				sizes: '32x32',
			},
		},
	],
});
```

### `titleDelimiter`

**Type :** `string`  
**Par défaut :** `'|'`

Définit le délimiteur entre le titre de la page et le titre du site dans la balise `<title>` de la page, qui s'affiche dans les onglets du navigateur.

Par défaut, chaque page a une balise `<title>` contenant `Titre de la page | Titre du site`.
Par example, cette page a pour titre « Référence de configuration » et ce site a pour titre « Starlight », donc la balise `<title>` de cette page contient « Référence de configuration | Starlight ».

### `disable404Route`

**Type :** `boolean`  
**Par défaut :** `false`

Désactive l'injection de la [page 404](https://docs.astro.build/fr/core-concepts/astro-pages/#page-derreur-404-personnalis%C3%A9e) par défaut de Starlight. Pour utiliser une route personnalisée `src/pages/404.astro` dans votre projet, définissez cette option à `true`.

### `components`

**Type :** `Record<string, string>`

Fournit les chemins vers les composants pour redéfinir les implémentations par défaut de Starlight.

```js
starlight({
	components: {
		SocialLinks: './src/components/MySocialLinks.astro',
	},
});
```

Consultez la [référence des redéfinitions](/fr/reference/overrides/) pour plus de détails sur tous les composants que vous pouvez redéfinir.

### `plugins`

**type:** [`StarlightPlugin[]`](/fr/reference/plugins/#référence-rapide-de-lapi)

Étendez Starlight avec des modules d'extension personnalisés.
Les modules d'extension appliquent des changements à votre projet pour modifier ou ajouter des fonctionnalités à Starlight.

Visitez la [vitrine des modules d'extension](/fr/resources/plugins/#modules-dextension) pour voir une liste des modules d'extension disponibles.

```js
starlight({
	plugins: [starlightPlugin()],
});
```

Consultez la [référence des modules d'extension](/fr/reference/plugins/) pour plus de détails pour créer vos propres modules d'extension.

### `credits`

Permet l'affichage d'un lien "Créé avec Starlight" dans le pied de page de votre site.

```js
starlight({
	credits: true,
});
```
