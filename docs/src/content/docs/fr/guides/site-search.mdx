---
title: Recherche
description: Découvrez les fonctionnalités de recherche intégrées à Starlight et comment les personnaliser.
---

import { Tabs, TabItem, Steps } from '@astrojs/starlight/components';

Par dé<PERSON><PERSON>, les sites utilisant Starlight incluent une recherche plein texte utilisant [Pagefind](https://pagefind.app/), un outil de recherche rapide et à faible bande passante pour sites statiques.

Aucune configuration n'est requise pour activer la recherche. Créez et déployez votre site, puis utilisez la barre de recherche dans l'en-tête du site pour trouver du contenu.

## Cacher du contenu dans les résultats de recherche

### Exclure une page

Pour exclure une page de votre index de recherche, ajoutez [`pagefind: false`](/fr/reference/frontmatter/#pagefind) au frontmatter de la page :

```md title="src/content/docs/sans-recherche.md" ins={3}
---
title: Contenu à cacher de la recherche
pagefind: false
---
```

### Exclure une partie d'une page

Pagefind ignorera le contenu à l'intérieur d'un élément avec l'attribut [`data-pagefind-ignore`](https://pagefind.app/docs/indexing/#removing-individual-elements-from-the-index).

Dans l'exemple suivant, le premier paragraphe s'affichera dans les résultats de recherche, mais le contenu de la `<div>` ne s'affichera pas :

```md title="src/content/docs/resultats-partiels.md" ins="data-pagefind-ignore"
---
title: Page partiellement indexée
---

Ce texte sera trouvable via la recherche.

<div data-pagefind-ignore>

Ce texte sera caché de la recherche.

</div>
```

## Système de recherche alternatif

### Algolia DocSearch

Si vous avez accès au [programme DocSearch d'Algolia](https://docsearch.algolia.com/) et que vous souhaitez l'utiliser à la place de Pagefind, vous pouvez utiliser le module d'extension officiel DocSearch de Starlight.

<Steps>

1. Installez `@astrojs/starlight-docsearch` :

   <Tabs>

   <TabItem label="npm">

   ```sh
   npm install @astrojs/starlight-docsearch
   ```

   </TabItem>

   <TabItem label="pnpm">

   ```sh
   pnpm add @astrojs/starlight-docsearch
   ```

   </TabItem>

   <TabItem label="Yarn">

   ```sh
   yarn add @astrojs/starlight-docsearch
   ```

   </TabItem>

   </Tabs>

2. Ajouter DocSearch à votre configuration [`plugins`](/fr/reference/configuration/#plugins) de Starlight dans le fichier `astro.config.mjs` et spécifiez votre `appId`, `apiKey` et `indexName` d'Algolia :

   ```js ins={4,10-16}
   // astro.config.mjs
   import { defineConfig } from 'astro/config';
   import starlight from '@astrojs/starlight';
   import starlightDocSearch from '@astrojs/starlight-docsearch';

   export default defineConfig({
   	integrations: [
   		starlight({
   			title: 'Site avec DocSearch',
   			plugins: [
   				starlightDocSearch({
   					appId: 'VOTRE_APP_ID',
   					apiKey: 'VOTRE_CLE_API_DE_RECHERCHE',
   					indexName: 'VOTRE_NOM_D_INDEX',
   				}),
   			],
   		}),
   	],
   });
   ```

</Steps>

Avec cette configuration mise à jour, la barre de recherche de votre site ouvrira désormais une modale Algolia au lieu de celle par défaut.

#### Traduire l'interface utilisateur de DocSearch

DocSearch fournit seulement des chaînes de l'interface utilisateur en anglais par défaut.
Ajoutez des traductions de l'interface utilisateur de la modale pour votre langue en utilisant le [système d'internationalisation](/fr/guides/i18n/#traduire-linterface-utilisateur-de-starlight) intégré à Starlight.

<Steps>

1. Étendez la définition de la collection de contenus `i18n` de Starlight avec le schéma DocSearch dans `src/content/config.ts` :

   ```js ins={4} ins=/{ extend: .+ }/
   // src/content/config.ts
   import { defineCollection } from 'astro:content';
   import { docsSchema, i18nSchema } from '@astrojs/starlight/schema';
   import { docSearchI18nSchema } from '@astrojs/starlight-docsearch/schema';

   export const collections = {
   	docs: defineCollection({ schema: docsSchema() }),
   	i18n: defineCollection({
   		type: 'data',
   		schema: i18nSchema({ extend: docSearchI18nSchema() }),
   	}),
   };
   ```

2. Ajoutez des traductions à vos fichiers JSON dans `src/content/i18n/`.

   Voici les valeurs par défaut en anglais utilisées par DocSearch :

   ```json title="src/content/i18n/en.json"
   {
   	"docsearch.searchBox.resetButtonTitle": "Clear the query",
   	"docsearch.searchBox.resetButtonAriaLabel": "Clear the query",
   	"docsearch.searchBox.cancelButtonText": "Cancel",
   	"docsearch.searchBox.cancelButtonAriaLabel": "Cancel",
   	"docsearch.searchBox.searchInputLabel": "Search",

   	"docsearch.startScreen.recentSearchesTitle": "Recent",
   	"docsearch.startScreen.noRecentSearchesText": "No recent searches",
   	"docsearch.startScreen.saveRecentSearchButtonTitle": "Save this search",
   	"docsearch.startScreen.removeRecentSearchButtonTitle": "Remove this search from history",
   	"docsearch.startScreen.favoriteSearchesTitle": "Favorite",
   	"docsearch.startScreen.removeFavoriteSearchButtonTitle": "Remove this search from favorites",

   	"docsearch.errorScreen.titleText": "Unable to fetch results",
   	"docsearch.errorScreen.helpText": "You might want to check your network connection.",

   	"docsearch.footer.selectText": "to select",
   	"docsearch.footer.selectKeyAriaLabel": "Enter key",
   	"docsearch.footer.navigateText": "to navigate",
   	"docsearch.footer.navigateUpKeyAriaLabel": "Arrow up",
   	"docsearch.footer.navigateDownKeyAriaLabel": "Arrow down",
   	"docsearch.footer.closeText": "to close",
   	"docsearch.footer.closeKeyAriaLabel": "Escape key",
   	"docsearch.footer.searchByText": "Search by",

   	"docsearch.noResultsScreen.noResultsText": "No results for",
   	"docsearch.noResultsScreen.suggestedQueryText": "Try searching for",
   	"docsearch.noResultsScreen.reportMissingResultsText": "Believe this query should return results?",
   	"docsearch.noResultsScreen.reportMissingResultsLinkText": "Let us know."
   }
   ```

</Steps>
