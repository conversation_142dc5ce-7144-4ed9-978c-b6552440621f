---
title: Pages
description: Learn how to create and manage your documentation site’s pages with Starlight.
sidebar:
  order: 1
---

Starlight generates your site’s HTML pages based on your content, with flexible options provided via Markdown frontmatter.
In addition, Starlight projects have full access to [Astro’s powerful page generation tools](https://docs.astro.build/en/basics/astro-pages/).
This guide shows how page generation works in Starlight.

## Content pages

### File formats

Starlight supports authoring content in Markdown and MDX with no configuration required.
You can add support for Markdoc by installing the experimental [Astro Markdoc integration](https://docs.astro.build/en/guides/integrations-guide/markdoc/).

### Add pages

Add new pages to your site by creating `.md` or `.mdx` files in `src/content/docs/`.
Use sub-folders to organize your files and to create multiple path segments.

For example, the following file structure will generate pages at `example.com/hello-world` and `example.com/reference/faq`:

import { FileTree } from '@astrojs/starlight/components';

<FileTree>

- src/
  - content/
    - docs/
      - hello-world.md
      - reference/
        - faq.md

</FileTree>

### Type-safe frontmatter

All Starlight pages share a customizable [common set of frontmatter properties](/reference/frontmatter/) to control how the page appears:

```md
---
title: Hello, World!
description: This is a page in my Starlight-powered site
---
```

If you forget anything important, Starlight will let you know.

## Custom pages

For advanced use cases, you can add custom pages by creating a `src/pages/` directory.
The `src/pages/` directory uses [Astro's file-based routing](https://docs.astro.build/en/basics/astro-pages/#file-based-routing) and includes support for `.astro` files amongst other page formats.
This is helpful if you need to build pages with a completely custom layout or generate a page from an alternative data source.

For example, this project mixes Markdown content in `src/content/docs/` with Astro and HTML routes in `src/pages/`:

<FileTree>

- src/
  - content/
    - docs/
      - hello-world.md
  - pages/
    - custom.astro
    - archived.html

</FileTree>

Read more in the [“Pages” guide in the Astro docs](https://docs.astro.build/en/basics/astro-pages/).

### Using Starlight’s design in custom pages

To use the Starlight layout in custom pages, wrap your page content with the `<StarlightPage />` component.
This can be helpful if you are generating content dynamically but still want to use Starlight’s design.

```astro
---
// src/pages/custom-page/example.astro
import StarlightPage from '@astrojs/starlight/components/StarlightPage.astro';
import CustomComponent from './CustomComponent.astro';
---

<StarlightPage frontmatter={{ title: 'My custom page' }}>
	<p>This is a custom page with a custom component:</p>
	<CustomComponent />
</StarlightPage>
```

#### Props

The `<StarlightPage />` component accepts the following props.

##### `frontmatter` (required)

**type:** `StarlightPageFrontmatter`

Set the [frontmatter properties](/reference/frontmatter/) for this page, similar to frontmatter in Markdown pages.
The [`title`](/reference/frontmatter/#title-required) property is required and all other properties are optional.

The following properties differ from Markdown frontmatter:

- The [`slug`](/reference/frontmatter/#slug) property is not supported and is automatically set based on the custom page’s URL.
- The [`editUrl`](/reference/frontmatter/#editurl) option requires a URL to display an edit link.
- The [`sidebar`](/reference/frontmatter/#sidebar) frontmatter property for customizing how the page appears in [autogenerated link groups](/reference/configuration/#sidebar) is not available. Pages using the `<StarlightPage />` component are not part of a collection and cannot be added to an autogenerated sidebar group.
- The [`draft`](/reference/frontmatter/#draft) option only displays a [notice](/reference/overrides/#draftcontentnotice) that the page is a draft but does not automatically exclude it from production builds.

##### `sidebar`

**type:** `SidebarEntry[]`  
**default:** the sidebar generated based on the [global `sidebar` config](/reference/configuration/#sidebar)

Provide a custom site navigation sidebar for this page.
If not set, the page will use the default global sidebar.

For example, the following page overrides the default sidebar with a link to the homepage and a group of links to different constellations.
The current page in the sidebar is set using the `isCurrent` property and an optional `badge` has been added to a link item.

```astro {3-13}
<StarlightPage
	frontmatter={{ title: 'Orion' }}
	sidebar={[
		{ label: 'Home', href: '/' },
		{
			label: 'Constellations',
			items: [
				{ label: 'Andromeda', href: '/andromeda/' },
				{ label: 'Orion', href: '/orion/', isCurrent: true },
				{ label: 'Ursa Minor', href: '/ursa-minor/', badge: 'Stub' },
			],
		},
	]}
>
	Example content.
</StarlightPage>
```

##### `hasSidebar`

**type:** `boolean`  
**default:** `false` if [`frontmatter.template`](/reference/frontmatter/#template) is `'splash'`, otherwise `true`

Control whether or not the sidebar should be displayed on this page.

##### `headings`

**type:** `{ depth: number; slug: string; text: string }[]`  
**default:** `[]`

Provide an array of all the headings on this page.
Starlight will generate the page table of contents from these headings if provided.

##### `dir`

**type:** `'ltr' | 'rtl'`  
**default:** the writing direction for the current locale

Set the writing direction for this page’s content.

##### `lang`

**type:** `string`  
**default:** the language of the current locale

Set the BCP-47 language tag for this page’s content, e.g. `en`, `zh-CN`, or `pt-BR`.

##### `isFallback`

**type:** `boolean`  
**default:** `false`

Indicate if this page is using [fallback content](/guides/i18n/#fallback-content) because there is no translation for the current language.
