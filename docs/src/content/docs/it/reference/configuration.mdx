---
title: Riferimenti configurazione
description: Una panoramica sulle configurazione supportate da Starlight.
---

## Configurare l'integrazione `starlight`

Starlight è un'integrazione costruita sul framework [Astro](https://astro.build). Puoi configurare il tuo progetto nel file `astro.config.mjs`:

```js
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'Il mio bellissimo sito per la documentazione',
		}),
	],
});
```

Puoi configurare le seguenti opzioni nell'integrazione `starlight`.

### `title` (obbligatorio)

**tipo:** `string`

Definisce il titolo del tuo sito. Sarà usato nei metadati e nel titolo della finestra.

### `description`

**tipo:** `string`

Definisce la descrizione del tuo sito. Sarà usato nei metadati condivisi con le engine di ricerca nel tag `<meta name="description">` se `description` non è specificato nell'intestazione della pagina.

### `logo`

**tipo:** [`LogoConfig`](#logoconfig)

Definisce il logo da rappresentare nella barra di navigazione con o al posto del nome del sito. Puoi definire un singolo `src` o indicare due separate immagini per `light` e `dark`.

```js
starlight({
	logo: {
		src: './src/assets/my-logo.svg',
	},
});
```

#### `LogoConfig`

```ts
type LogoConfig = { alt?: string; replacesTitle?: boolean } & (
	| { src: string }
	| { light: string; dark: string }
);
```

### `tableOfContents`

**tipo:** `false | { minHeadingLevel?: number; maxHeadingLevel?: number; }`  
**predefinito:** `{ minHeadingLevel: 2; maxHeadingLevel: 3; }`

Configura la tabella dei contenuti vista a destra di ogni pagina. Per predefinite, i titoli `<h2>` e `<h3>` saranno incluse in essa.

### `editLink`

**tipo:** `{ baseUrl: string }`

Abilita il link "Modifica questa pagina" definendo l'URL base da usare. Il link finale sarà `editLink.baseUrl` + il percorso corrente. Per esempio, per abilitare la possibilità di modificare la repository `withastro/starlight` su GitHub:

```js
starlight({
	editLink: {
		baseUrl: 'https://github.com/withastro/starlight/edit/main/',
	},
});
```

Con questa configurazione, una pagina `/introduction` avrà un link di modifica a `https://github.com/withastro/starlight/edit/main/src/content/docs/introduction.md`.

### `sidebar`

**tipo:** [`SidebarItem[]`](#sidebaritem)

Configura la barra laterale di navigazione del tuo sito.

Una barra laterale è un array di collegamenti e gruppi di collegamenti.
Ogni elemento avente un `label` e una delle seguenti proprietà:

- `link` — un singolo colelgamento a uno specifico URL, ad es. `'/home'` o `'https://example.com'`.

- `items` — an aray contenente più collegamenti della barra laterale e sottogruppi.

- `autogenerate` — un oggetto indicante una cartella dei tuoi documenti di cui generare automaticamente i collegamenti.

```js
starlight({
	sidebar: [
		// Un singolo elemento chiamato “Home”.
		{ label: 'Home', link: '/' },
		// Un gruppo intitolato "Inizia qui" contenente due link.
		{
			label: 'Inizia qui',
			items: [
				{ label: 'Introduzione', link: '/intro' },
				{ label: 'Prossimi passi', link: '/next-steps' },
			],
		},
		// Un gruppo che si riferisce a tutte le pagine nella cartella reference.
		{
			label: 'Riferimenti',
			autogenerate: { directory: 'reference' },
		},
	],
});
```

#### Ordinamento

I gruppi generati automaticamente sono ordinati alfabeticamente.
Per esempio, una pagina generata da `astro.md` apparirà sopra a quella generata da `starlight.md`.

#### Gruppi comprimibili

I gruppi di collegamenti vengono espansi per impostazione predefinita. Puoi modificare questo comportamento impostando la proprietà `collapsed` di un gruppo su `true`.

I sottogruppi generati automaticamente rispettano la proprietà `collapsed` del gruppo principale per impostazione predefinita. Imposta la proprietà `autogenerate.collapsed` per sovrascriverla.

```js {5,16}
sidebar: [
  // Un gruppo di collegamenti compresso.
  {
    label: 'Collegamenti compressi',
    collapsed: true,
    items: [
      { label: 'Introduzione', link: '/intro' },
      { label: 'Prossimi passi', link: '/next-steps' },
    ],
  },
  // An expanded group containing collapsed autogenerated subgroups.
  {
    label: 'Riferimenti',
    autogenerate: {
      directory: 'reference',
      collapsed: true,
    },
  },
],
```

#### Tradurre i titoli

Se il tuo sito è multilingua, ogni elemento di `label` è considerato come appartenente alla lingua di default. Puoi definire `translations` per fornire i titoli per le altre lingue:

```js {5,9,14}
sidebar: [
  // Un esempio di barra laterale con traduzioni in italiano.
  {
    label: 'Start here',
    translations: { it: 'Inizia qui' },
    items: [
      {
        label: 'Getting Started',
        translations: { it: 'Iniziamo' },
        link: '/getting-started',
      },
      {
        label: 'Project Structure',
        translations: { it: 'Struttura del progetto' },
        link: '/structure',
      },
    ],
  },
],
```

#### `SidebarItem`

```ts
type SidebarItem = {
	label: string;
	translations?: Record<string, string>;
} & (
	| {
			link: string;
			badge?: string | BadgeConfig;
			attrs?: Record<string, string | number | boolean | undefined>;
	  }
	| { items: SidebarItem[]; collapsed?: boolean }
	| {
			autogenerate: { directory: string; collapsed?: boolean };
			collapsed?: boolean;
	  }
);
```

#### `BadgeConfig`

```ts
interface BadgeConfig {
	text: string;
	variant: 'note' | 'tip' | 'caution' | 'danger' | 'success' | 'default';
}
```

### `locales`

**tipo:** <code>\{ \[dir: string\]: [LocaleConfig](#localeconfig) }</code>

[Configura l'internazionalizzazione (i18n)](/it/guides/i18n/) del sito impostando quali `locales` sono supportati.

Ogni elemento deve utilizzare come chiave la cartella dove i file della lingua associata si trovano.

```js
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'My Site',
			// Imposta inglese come il linguaggio predefinito.
			defaultLocale: 'en',
			locales: {
				// La documentazione in inglese si trova in `src/content/docs/en/`
				en: {
					label: 'English',
				},
				// La documentazione in cinese semplificato si trova in `src/content/docs/zh-cn/`
				'zh-cn': {
					label: '简体中文',
					lang: 'zh-CN',
				},
				// La documentazione in arabo si trova in `src/content/docs/ar/`
				ar: {
					label: 'العربية',
					dir: 'rtl',
				},
			},
		}),
	],
});
```

#### `LocaleConfig`

```ts
interface LocaleConfig {
	label: string;
	lang?: string;
	dir?: 'ltr' | 'rtl';
}
```

Puoi impostare le seguenti opzioni per ogni locale:

##### `label` (obbligatorio)

**tipo:** `string`

L'etichetta per questa lingua da rappresentare agli utenti, per esempio nel selettore di lingue. Spesso vorrai usare il nome della lingua per come l'utente si aspetta di leggere, per esempio `"English"`, `"العربية"`, o `"简体中文"`.

##### `lang`

**tipo:** `string`

Il tag BCP-47 per la lingua, per esempio `"en"`, `"ar"`, o `"zh-CN"`. Se non lo imposti sarà utilizzato il nome della cartella.

##### `dir`

**tipo:** `'ltr' | 'rtl'`

Il verso di scrittura della lingua; `"ltr"` per sinistra a destra (predefinita) e `"rtl"` per destra a sinistra.

#### Lingua principale

Puoi definire un linguaggio principale senza una cartella `/lang/` definendo `root`:

```js {3-6}
starlight({
	locales: {
		root: {
			label: 'English',
			lang: 'en',
		},
		it: {
			label: 'Italiano',
		},
	},
});
```

Per esempio, questo ti permette di fornire `/getting-started/` come un percorso in inglese e `/it/getting-started/` come quello equivalente in italiano.

### `defaultLocale`

**tipo:** `string`

Definisce la lingua predefinita del sito.
Il valore deve corrispondere ad una chiave di [`locales`](#locales).
(Se la lingua predefinita è il [root](#lingua-principale), non è necessario).

Verrà utilizzato come fallback per le pagine non tradotte.

### `social`

import SocialLinksType from '~/components/social-links-type.astro';

**tipo:** <SocialLinksType />

Dettagli opzionali per gli account social del sito. Se vengono aggiunti apparirà l'icona corrispondente nella barra superiore.

```js
starlight({
	social: {
		codeberg: 'https://codeberg.org/knut/examples',
		discord: 'https://astro.build/chat',
		github: 'https://github.com/withastro/starlight',
		gitlab: 'https://gitlab.com/delucis',
		linkedin: 'https://www.linkedin.com/company/astroinc',
		mastodon: 'https://m.webtoo.ls/@astro',
		threads: 'https://www.threads.net/@nmoodev',
		twitch: 'https://www.twitch.tv/bholmesdev',
		twitter: 'https://twitter.com/astrodotbuild',
		'x.com': 'https://x.com/astrodotbuild',
		youtube: 'https://youtube.com/@astrodotbuild',
	},
});
```

### `customCss`

**tipo:** `string[]`

Utilizza file CSS aggiuntivi per personalizzare il sito Starlight.

Supporta file CSS locali relativi alla cartella principale del progetto, ad esempio `'/src/custom.css'`, e CSS installato come modulo npm, per esempio `'@fontsource/roboto'`.

```js
starlight({
	customCss: ['/src/custom-styles.css', '@fontsource/roboto'],
});
```

### `expressiveCode`

**tipo:** `StarlightExpressiveCodeOptions | boolean`
**predefinito:** `true`

Starlight utilizza [Expressive Code](https://github.com/expressive-code/expressive-code/tree/main/packages/astro-expressive-code) per rendere i blocchi di codice e aggiungere il supporto per evidenziare parti di esempi di codice, aggiungere nomi di file ai blocchi di codice e altro ancora.
Consulta la guida ["Blocchi codice"](/it/guides/authoring-content/#blocco-di-codice) per scoprire come utilizzare la sintassi Expressive Code nel tuo contenuto Markdown e MDX.

Puoi utilizzare tutte le opzioni di configurazione standard di [Expressive Code](https://github.com/expressive-code/expressive-code/blob/main/packages/astro-expressive-code/README.md#configuration) nonché alcune proprietà specifiche di Starlight, impostandole nell'opzione `expressiveCode` di Starlight.
Ad esempio, imposta l'opzione `styleOverrides` di Expressive Code per sovrascrivere il CSS predefinito. Ciò consente personalizzazioni come dare ai tuoi blocchi di codice angoli arrotondati:

```js ins={2-4}
starlight({
	expressiveCode: {
		styleOverrides: { borderRadius: '0.5rem' },
	},
});
```

Se vuoi disabilitare Expressive Code, imposta `expressiveCode: false` nella configurazione di Starlight:

```js ins={2}
starlight({
	expressiveCode: false,
});
```

In aggiunta alle opzioni standard di Expressive Code, puoi anche impostare le seguenti proprietà specifiche di Starlight nella tua configurazione `expressiveCode` per personalizzare ulteriormente il comportamento del tema per i tuoi blocchi di codice:

#### `themes`

**tipo:** `Array<string | ThemeObject | ExpressiveCodeTheme>`
**predefinito:** `['starlight-dark', 'starlight-light']`

Imposta i temi utilizzati per dare stile ai blocchi di codice.
Consulta la [documentazione `themes` di Expressive Code](https://github.com/expressive-code/expressive-code/blob/main/packages/astro-expressive-code/README.md#themes) per i dettagli dei formati di tema supportati.

Per impostazione predefinita, Starlight utilizza le varianti chiara e scura del tema [Night Owl](https://github.com/sdras/night-owl-vscode-theme) di Sarah Drasner.

Se fornisci almeno un tema chiaro e uno scuro, Starlight sincronizzerà automaticamente il tema del blocco di codice attivo con il tema corrente del sito.
Configura questo comportamento con l'opzione [`useStarlightDarkModeSwitch`](#usestarlightdarkmodeswitch).

#### `useStarlightDarkModeSwitch`

**tipo:** `boolean`
**predefinito:** `true`

Quando è impostato su `true`, i blocchi di codice passano automaticamente tra temi chiari e scuri quando cambia il tema del sito.  
Quando è impostato su `false`, devi aggiungere manualmente CSS per gestire il passaggio tra più temi.

:::note
Quando imposti `themes`, devi fornire almeno un tema chiaro e uno scuro affinché il cambio di tema in modalità scura di Starlight funzioni.
:::

#### `useStarlightUiThemeColors`

**tipo:** `boolean`
**predefinito:** `true` se `themes` non è impostato, altrimenti `false`

Quando è impostato su `true`, le variabili CSS di Starlight vengono utilizzate per i colori degli elementi dell'interfaccia utente dei blocchi di codice (sfondi, pulsanti, ombre ecc.), in linea con il [tema di colore del sito](/it/guides/css-and-tailwind/#temi).  
Quando è impostato su `false`, vengono utilizzati i colori forniti dal tema di evidenziazione della sintassi attivo per questi elementi.

:::note  
Quando si utilizzano temi personalizzati e si imposta su `true`, è necessario fornire almeno un tema chiaro e uno scuro per garantire un contrasto di colore adeguato.
:::

### `pagefind`

**tipo:** `boolean`
**predefinito:** `true`

Definisce se il provider di ricerca predefinito di Starlight [Pagefind](https://pagefind.app/) è abilitato.

Impostalo su `false` per disabilitare l'indicizzazione del tuo sito con Pagefind.
Ciò nasconderà anche l'interfaccia di ricerca predefinita, se in uso.

### `head`

**tipo:** [`HeadConfig[]`](#headconfig)

Aggiunge tag all'`<head>` del sito Starlight.
Può essere utile per aggiungere script e risorse di terze parti.

```js
starlight({
	head: [
		// Esempio : aggiunge Fathom analytics.
		{
			tag: 'script',
			attrs: {
				src: 'https://cdn.usefathom.com/script.js',
				'data-site': 'MY-FATHOM-ID',
				defer: true,
			},
		},
	],
});
```

#### `HeadConfig`

```ts
interface HeadConfig {
	tag: string;
	attrs?: Record<string, string | boolean | undefined>;
	content?: string;
}
```

### `lastUpdated`

**tipo:** `boolean`  
**predefinito:** `false`

Controlla se il piè di pagina mostra quando è stata aggiornata l'ultima volta la pagina.

Per impostazione predefinita, questa funzionalità si basa sulla cronologia Git del repository e potrebbe non essere accurata su alcune piattaforme di distribuzione che eseguono [cloni superficiali](https://git-scm.com/docs/git-clone#Documentation/git-clone.txt---depthltdepthgt). Una pagina può sovrascrivere questa impostazione o la data basata su Git utilizzando il [campo frontmatter `lastUpdated`](/it/reference/frontmatter/#lastupdated).

### `pagination`

**tipo:** `boolean`  
**predefinito:** `true`

Definisci se il piè di pagina deve includere i collegamenti alla pagina precedente e successiva.

Una pagina può sovrascrivere questa impostazione o il testo del collegamento e/o l'URL utilizzando i campi frontmatter [`prev`](/it/reference/frontmatter/#prev) e [`next`](/it/reference/frontmatter/#next).

### `favicon`

**tipo:** `string`  
**predefinito:** `'/favicon.svg'`

Imposta il percorso della favicon predefinita per il tuo sito web che dovrebbe trovarsi nella directory `public/` ed essere valido (`.ico`, `.gif`, `.jpg`, `.png` o `.svg`) file di icone.

```js
starlight({
  favicon: '/images/favicon.svg',
}),
```

Se devi impostare varianti aggiuntive o favicon di fallback, puoi aggiungere tag utilizzando l'[opzione`head`](#head):

```js
starlight({
  favicon: '/images/favicon.svg'.
  head: [
    // Aggiungi il fallback della favicon ICO per Safari.
    {
      tag: 'link',
      attrs: {
        rel: 'icon',
        href:'/images/favicon.ico',
        sizes: '32x32',
      },
    },
  ],
});
```

### `titleDelimiter`

**tipo:** `string`  
**predefinito:** `'|'`

Imposta il delimitatore tra il titolo della pagina e il titolo del sito nel tag `<title>` della pagina, visualizzato nelle schede del browser.

Per impostazione predefinita, ogni pagina ha un `<title>` di `Titolo della pagina | Titolo del sito`.
Ad esempio, questa pagina è intitolata "Riferimenti configurazione" e questo sito è intitolato "Starlight", quindi il `<title>` per questa pagina è "Riferimenti configurazione | Starlight".

### `disable404Route`

**tipo:** `boolean`
**predefinito:** `false`

Disabilita l'iniezione della [pagina 404](https://docs.astro.build/it/basics/astro-pages/#pagina-di-errore-404-personalizzata) predefinita di Starlight. Per utilizzare un percorso `src/pages/404.astro` personalizzato nel tuo progetto, imposta questa opzione su `true`.

### `components`

**tipo:** `Record<string, string>`

Fornisci i percorsi ai componenti per sovrascrivere le implementazioni predefinite di Starlight.

```js
starlight({
	components: {
		SocialLinks: './src/components/MySocialLinks.astro',
	},
});
```

Consulta il [Riferimento alle sostituzioni](/it/reference/overrides/) per i dettagli di tutti i componenti che puoi sovrascrivere.

### `plugins`

**tipo:** [`StarlightPlugin[]`](/it/reference/plugins/#riferimento-rapido-per-le-api)

Estendi Starlight con plugin personalizzati.
I plugin applicano modifiche al tuo progetto per modificare o aggiungere funzionalità a Starlight.

Visita la [vetrina dei plugin](/it/resources/plugins/#plugins) per vedere un elenco dei plugin disponibili.

```js
starlight({
	plugins: [starlightPlugin()],
});
```

Consulta il [Riferimento ai plugin](/it/reference/plugins/) per i dettagli sulla creazione dei tuoi plugin personalizzati.
