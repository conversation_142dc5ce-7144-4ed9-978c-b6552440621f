---
title: Manual Setup
description: Learn how to configure Starlight manually to add it to an existing Astro project.
---

import { Tabs, TabItem } from '@astrojs/starlight/components';

The quickest way to create a new Starlight site is using `create astro` as shown in the [Getting Started guide](/getting-started/#create-a-new-project).
If you want to add Starlight to an existing Astro project, this guide will explain how.

## Set up Starlight

To follow this guide, you’ll need an existing Astro project.

### Add the Starlight integration

Starlight is an [Astro integration](https://docs.astro.build/en/guides/integrations-guide/). Add it to your site by running the `astro add` command in your project’s root directory:

<Tabs>
    <TabItem label="npm">
        ```sh
        npx astro add starlight
        ```

    </TabItem>
    <TabItem label="pnpm">
        ```sh
        pnpm astro add starlight
        ```
    </TabItem>
    <TabItem label="Yarn">
        ```sh
        yarn astro add starlight
        ```
    </TabItem>

</Tabs>

This will install the required dependencies and add Starlight to the `integrations` array in your Astro config file.

### Configure the integration

The Starlight integration is configured in your `astro.config.mjs` file.

Add a `title` to get started:

```js ins={8}
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'My delightful docs site',
		}),
	],
});
```

Find all available options in the [Starlight configuration reference](/reference/configuration/).

### Configure content collections

Starlight is built on top of Astro’s [content collections](https://docs.astro.build/en/guides/content-collections/), which are configured in the `src/content/config.ts` file.

Create or update the content config file, adding a `docs` collection that uses Starlight’s `docsSchema`:

```js ins={3,6}
// src/content/config.ts
import { defineCollection } from 'astro:content';
import { docsSchema } from '@astrojs/starlight/schema';

export const collections = {
	docs: defineCollection({ schema: docsSchema() }),
};
```

### Add content

Starlight is now configured and it’s time to add some content!

Create a `src/content/docs/` directory and start by adding an `index.md` file.
This will be the homepage of your new site:

```md
---
# src/content/docs/index.md
title: My docs
description: Learn more about my project in this docs site built with Starlight.
---

Welcome to my project!
```

Starlight uses file-based routing, which means every Markdown, MDX, or Markdoc file in `src/content/docs/` will turn into a page on your site. Frontmatter metadata (the `title` and `description` fields in the example above) can change how each page is displayed.
See all the available options in the [frontmatter reference](/reference/frontmatter/).

## Tips for existing sites

If you have an existing Astro project, you can use Starlight to quickly add a documentation section to your site.

### Use Starlight at a subpath

To add all Starlight pages at a subpath, place all your docs content inside a subdirectory of `src/content/docs/`.

For example, if Starlight pages should all start with `/guides/`, add your content in the `src/content/docs/guides/` directory:

import { FileTree } from '@astrojs/starlight/components';

<FileTree>

- src/
  - content/
    - docs/
      - **guides/**
        - guide.md
        - index.md
  - pages/
- astro.config.mjs

</FileTree>

In the future, we plan to support this use case better to avoid the need for the extra nested directory in `src/content/docs/`.

### Use Starlight with SSR

You can use Starlight alongside custom on-demand rendered pages in your project by following the [“On-demand Rendering Adapters”](https://docs.astro.build/en/guides/server-side-rendering/) guide in Astro’s docs.

Currently, documentation pages generated by Starlight are always prerendered regardless of your project's output mode. We hope to be able to support on-demand rendering for Starlight pages soon.
