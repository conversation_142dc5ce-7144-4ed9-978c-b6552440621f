---
title: Code organization
---

The viclass project follows single repo, multiple projects structure. There are a few groups of project inside the repo:

* **Portal projects** - these projects are for the main portal, handling users interactions and data on the portal. Under the portal project group, there are backend and front end projects.

* **Vinet projects** - these projects implements the synchronization capabilities, which allow editors, which doesn't have any notion of the classrooms, to synchronize their data in the classroom context. In the future, will provide the infrastructure for audio and video calling inside the classrooms.

* **Editor projects** - these are the projects implementing the frontend and backend of the editors, and coordinators (the components to coordinate the editors in various environments, classroom, standalone, embedding, etc.. and provides api for application, e.g classroom or other app to controls the editors)

## Portal projects

### Frontend portal projects 

All projects are Angular projects.

Location: **/portal/ui**

Contains all the UI that the user will see for the MVP. Frontend projects are . The projects are built using Angular.

#### Homepage project 
Containing the Homepage UI. This is the first page user sees when visiting our domain. It also handle things such as user registration, login, etc..

#### LSession (frontend) project 
Containing the UI that handles user interactions related to learning sessions, such as create, update session, registration and deregistration of session users, etc...

#### Classroom project 
Containing the UI that handles user flows inside the classroom, such as displaying user status, classroom notification, functionalities such as "Giơ tay", "Gọi lên bảng", "Câu hỏi nhanh", are implemented here

#### Common project 
Containing common tools / components used by other projects, for example, common form utilities, authentication utilities, dialog, etc... 

#### Whiteboard project 

Containing components that form the UI of the whiteboard used inside a classroom, such as the toolbar UI, the viewport management UI, etc..

### Backend portal projects
Contains all the backend services that handle data storage and querying, API service, notification service, etc... All projects are implemented using Kotlin, either using Playframework or Ktor or Jersey (socketio). The build system for the backend projects is gradle.

Location: **/portal** 

All projects are written in Kotlin using:

  * Play 2.8 (project: backend, might be changed to ktor soon)
  * Ktor (all other projects)
  * Jetty (project: socketio)

#### Backend project

Location: **./backend**

Implement the aggregated api that the frontend projects use. This project calls other backend service for data, and functionalities. It also validate data and contains some business logic, where the logic concern other multiple backend projects.

#### LSession project

Location: **./lsession**

Implement the logic related to the learning session like creation, modification, starting, stopping, acceptting registration etc... It also provides methods to query, search, information about learning sesion

#### User project

Location: **./user**

Implement logic related to user data

#### Notification project

Location: **./notification**

Implement logic for notification data

#### Configuration project

Location: **./configurations**

There are many configurations used by the frontend pages such as the list of supporting class level, or subjects, etc... This project provides API to request these configurations from the API.



## Vinet projects

Location: **/vinet**

The projects inside this group implement the Viclass network realtime synchronization infrastructure through the use of webrtc. They synchronize data from browsers to browsers, provide audio and video streaming, connecting the frontend with other services such as the editor backend project services, they record classroom presentation and provide playback streaming as well.

The projects are implemented using:

* Golang - for syncer project, because it needs to use Pion/webrtc for backend Webrtc
* Ktor - for other backend projects

#### **Syncer project**

Location: **./sync**

This project is the webrtc server that the frontend browsers will connect to. It sync the commands, video and audio from one browser to other browsers. It also stream this streamed data to the editor backend services using HTTP, with CCS as the proxy.

This project is based on the [pion/webrtc](https://github.com/pion/webrtc) library for its webrtc protocol implementation.

#### **Classroom oordinator service (CCS) project**

Location: **./ccs**

This project provides API to coordinate the synchronization flows inside a classroom, e.g. setup synchronization room, register peers, recording peer online statuses, etc... It also provides a signaling channels among webrtc peers so that they can send and receive control messages.

The CCS is also a proxy to the editor backend services so that other services inside vinet, e.g. the syncer, need not to know what an editor is or to work with editor specifics (e.g. global ids)

## Editor projects

This is the place where we implements the frontend and backend logics of the editors. 

### Editor backends
Location: **/editor/backend**

#### Freedrawing editor

Location: **./freedrawing**

Contains the logic to deal with freedrawing document, such as insert, load documents, save document, update document, insert various objects and layers.

<!-- #### Geo editor

#### Math editor

#### Image / Pdf editor

#### Statistic visualization editor

#### Mindmap editor

#### Diagram editor

#### History editor

#### Physic editor -->

### Editor frontend

Language: **Typescript**
Framework: **None**

Contains the projects that handle frontend user interaction. It contains a common logic and interfaces as well as individual projects for individual editors. These individual projects follows the API and structure defined in the common logic project.

The projects are implemented without a foundation framework such as Angular or React. They are aimed to be as compact as possible so that they can be embedded into other third party websites in future. They don't contains a lot of UI but mostly handling user interaction and data. The UI such as the toolbar UI of the editors are considered Application level and implemented separately (e.g. for classroom, the UI are implemented using angular inside whiteboard project)

#### Editor core

Location: **/editors/client/editor.core**

This project define common interfaces for editors components, and concepts such as editor commands, viewport, event managers, toolbars, coordinators. This project also implements the command gateways, command channel and logics related to the classroom synchronization and coordination (e.g. working with the CCS, API for frontend app (whiteboard) to create page (i.e. viewport), synchronization coordination logic, etc... ). The classroom specifics, however, should be separated into different project.

#### Editor freedrawing

Location: **/editors/client/editor.freedrawing**

This project implements the logic for handle user interaction command with the freedrawing editor. Actions such as pencil draw, rectangle draw, etc... are converted into commands and applied by the logic to display drawing result and sending to other freedrawing instances on other browsers through the data provided by the **editor.core**
