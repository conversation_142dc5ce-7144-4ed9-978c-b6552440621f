{
  // https://typedoc.org/options/
  "categoryOrder": [
    "Mathfield",
    "Accessing and changing the content", // Secondary category
    "Selection",
    "Customization", // Secondary category
    "Styles",
    "Options",
    "Macros",
    "Registers",
    "Editing Commands",
    "Speech",

    "Focus", // Secondary category

    "Prompts", // Secondary category
    "Undo", // Secondary category

    "Keyboard Shortcuts", // Secondary category
    "Menu", // Secondary category
    "Virtual Keyboard",

    "Localization", // Secondary category

    "Static Rendering",
    "Conversion",

    "MathJSON",
    "Other",
    "*"
  ],

  "out": "./temp-docs",
  "compilerOptions": {
    // "esModuleInterop": true,

    // More lenient...
    "noImplicitAny": false,
    "strictNullChecks": false
  },
  // "includes": "./src/compute-engine/**/*",
  "externalPattern": ["**/node_modules/**"],
  "excludeInternal": true,
  "excludePrivate": true,
  "excludeProtected": true,
  "excludeReferences": true,
  "name": "Mathfield API Reference",
  "disableSources": true,
  "disableGit": true,
  "outputFileStrategy": "modules",
  "entryPoints": ["./src/mathlive.ts"],
  "readme": "none",
  "excludeExternals": true,

  // https://typedoc-plugin-markdown.org/options
  "plugin": [
    // "typedoc-plugin-no-inherit",
    "typedoc-plugin-markdown",
    "./plugins/grok-theme/index.mjs"
  ],
  "theme": "grok-theme",
  "useHTMLAnchors": true,
  "hidePageHeader": true,
  "hidePageTitle": true,
  "hideBreadcrumbs": true,
  "excludeGroups": true,
  "useCodeBlocks": true, // Use code blocks for function signatures
  "expandParameters": false, // Don't include type in function signatures
  "parametersFormat": "list", // table | list
  "propertiesFormat": "list", // table | list
  "typeDeclarationFormat": "list", // table | list
  "expandObjects": true,
  "notRenderedTags": [
    "@noInheritDoc",
    "@keywords",
    "@command",
    "@category",
    "@version"
  ]
}
